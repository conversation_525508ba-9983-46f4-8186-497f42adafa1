#ifndef SAMPLES_SEARCH_HANDLER_RESULT_PRINTER_H
#define SAMPLES_SEARCH_HANDLER_RESULT_PRINTER_H
#include <vector>

#include "search_def.h"
void PrintPlaceBriefs(int task_id,
                      const std::vector<aurora::search::PlaceBrief>& places,
                      size_t max_num = 3);
void PrintPlaceDetail(int task_id, aurora::search::PlaceDetail& place);
#endif  // SAMPLES_SEARCH_HANDLER_RESULT_PRINTER_H
