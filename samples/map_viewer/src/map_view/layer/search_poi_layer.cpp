#include "search_poi_layer.h"
#include <QPainter>
#include <iostream>
#include "search/include/search_def.h"
#include "map_camera.h"
#include "map_widget_manage.h"

const int poi_image_w = 38;
const int poi_image_h = 38;

SearchPoiLayer::SearchPoiLayer(MapWidget *parent)
    : m_poiImg(":MainWindow/resources/poi.png")
    , m_poiSelectedImg(":MainWindow/resources/poi_selected.png")
{
    m_select_idx = INVALID_UINT32;
    m_map_widget_ptr = parent;


}

SearchPoiLayer::~SearchPoiLayer()
{

}

void SearchPoiLayer::paint(QPainter& painter, MapCamera& camera)
{
    // 画搜索结果图标
    if (m_response == nullptr || m_response->place_briefs.size() == 0)
        return;

    QPainterPath demo_path;
    demo_path.moveTo(0,0);
    demo_path.lineTo(1,1);
    painter.drawPath(demo_path);

    for (size_t i = 0; i < m_response->place_briefs.size(); ++i)
    {
        auto poi = m_response->place_briefs.at(i);
        QPointF screenPos;
        RGeoPoint pos(poi.location.lng(), poi.location.lat());
        camera.worldToScreen(&pos, screenPos);

        if (i == m_select_idx) {
            painter.drawImage(screenPos.x() - (poi_image_w/2), screenPos.y() - poi_image_h, m_poiSelectedImg);
        } else {
            painter.drawImage(screenPos.x() - (poi_image_w/2), screenPos.y() - poi_image_h, m_poiImg);
        }
    }
}


void SearchPoiLayer::onMouseDown(QMouseEvent* event, MapCamera& camera)
{

}

void SearchPoiLayer::setSearchResult(const SearchByTextResponsePtr response)
{    
    m_response = response;
    if (m_response && m_response->place_briefs.size() > 0)
    {
        auto poi = m_response->place_briefs.at(0);
        RGeoPoint world_center = RGeoPoint(poi.location.lng(), poi.location.lat());
        m_map_widget_ptr->setWorldCenter(&world_center);
    }
    g_app->showSearchResultWidget(m_response);
    emit needRedraw();
}

void SearchPoiLayer::setSelectIdx(uint32 idx)
{
    if (m_response == nullptr)
        return;

    if (idx < m_response->place_briefs.size())
    {
        m_select_idx = idx;
        auto &poi = m_response->place_briefs.at(idx);
        const RGeoPoint pos(poi.location.lng(), poi.location.lat());
        std::cout << "SearchPoiLayer -> lng: " << poi.location.lng() << ", lat: " << poi.location.lat() << std::endl;

        m_map_widget_ptr->setWorldCenter(&pos);
    }
    emit needRedraw();
}
