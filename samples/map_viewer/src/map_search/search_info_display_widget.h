﻿
#ifndef SEARCH_INFO_DISPLAY_WIDGET_H
#define SEARCH_INFO_DISPLAY_WIDGET_H

#include "ui_search_info_display_widget.h"
#include <QDockWidget>
#include "map_engine_manage.h"
#include "search/include/search_def.h"
#include "map_widget.h"

namespace Ui { class SearchInfoDisplayWidget; }

/**
	@brief 搜索结果信息显示窗口
*/
class SearchInfoDisplayWidget : public QDockWidget
{
	Q_OBJECT

public:
    SearchInfoDisplayWidget(QWidget *parent = 0);
    ~SearchInfoDisplayWidget();

    void updateDisplay(SearchByTextResponsePtr response);



private slots:
	void onItemSelectionChanged();
	void onTreeWidgetContextMenu(const QPoint& pos);

private:
    void showPoiInfo(const aurora::search::PlaceBrief & poi);

private:
    Ui::SearchInfoDisplayWidget *ui;
    MapWidget * m_map_widget_ptr;

};

#endif // SEARCH_INFO_DISPLAY_WIDGET_H
