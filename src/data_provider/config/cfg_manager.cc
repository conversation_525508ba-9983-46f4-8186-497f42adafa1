#include "config_data/cfg_manager.h"

#include <assert.h>

#include <cstdint>
#include <iostream>

#include "cfg_desc.h"

namespace aurora {
namespace parser {
CfgManager::CfgManager() : desc_(new CfgDesc), file_path_() {}

CfgManager::CfgManager(const std::string& path) : desc_(new CfgDesc), file_path_(path) {}

CfgManager::~CfgManager() {
  if (desc_ != nullptr) {
    delete desc_;
    desc_ = nullptr;
  }
}

int32_t CfgManager::Init(const std::string& path) {
  if (!path.empty()) {
    file_path_ = path;
  }
  if (desc_ != nullptr) {
    std::cout << "desc " << desc_ << std::endl;
    std::cout << "path " << path.c_str() << std::endl;
    return desc_->Init(file_path_);
  }

  return 0;
}

int32_t CfgManager::Reload() {
  desc_->Clear();
  return desc_->Init(file_path_);
}

ViewObjStyle* CfgManager::GetViewObjStyle(uint32_t map_scale, uint32_t layer_id) const {
  return (ViewObjStyle*)desc_->GetViewObjStyle(map_scale, layer_id);
}

MapRouteCfg* CfgManager::GetMapRouteCfg() const { return desc_->GetMapRouteCfg(); }

MapDirectionCfg* CfgManager::GetMapDirectionCfg() const { return desc_->GetMapDirectionCfg(); }

ViewScaleInfoSet* CfgManager::GetViewScaleInfo() const { return desc_->GetViewScaleInfoSet(); }

}  // namespace parser
}  // namespace aurora
