#ifndef MAP_SRC_DATA_PROVIDER_CONFIG_CFG_DESC_H
#define MAP_SRC_DATA_PROVIDER_CONFIG_CFG_DESC_H

#include <stdint.h>
#include <stdio.h>

#include <string>
#include <vector>

#include "config_data/cfg_data_def.h"

namespace aurora {

namespace parser {
struct MapViewObjIndex;
class CfgReader;
class CfgDesc {
public:
  CfgDesc();

  CfgDesc(const CfgDesc&) = delete;

  CfgDesc& operator=(const CfgDesc&) = delete;

  virtual ~CfgDesc();

  int32_t Init(const std::string& path);

  void* GetViewObjStyle(uint32_t map_scale, uint32_t layer_id);

  void Clear();

  MapRouteCfg* GetMapRouteCfg() const;

  MapDirectionCfg* GetMapDirectionCfg() const;

  MapSearchCfg* GetMapSearchCfg() const;

  MapCommonCfg* GetMapCommonCfg() const;

  MapMatchCfg* GetMapMatchCfg() const;

  ViewScaleInfoSet* GetViewScaleInfoSet() const;

private:
  void* FindViewObjStyle(uint32_t layer_id, uint32_t map_scale, MapViewObjIndex* view);

  int32_t BuildStyleObjIndex(ViewObjStyle* obj, uint32_t num);

private:
  MapViewMode* map_view_mode_;
  ViewScaleInfoSet* view_scale_info_set_;
  MapRouteCfg* route_cfg_;
  MapMatchCfg* match_cfg_;
  MapDirectionCfg* direction_cfg_;
  MapSearchCfg* search_cfg_;
  MapCommonCfg* common_cfg_;
  ViewObjStyle* view_style_cfg_;
  uint32_t view_style_num_;

  MapViewObjIndex* view_obj_index_;
  CfgReader* reader_;
};

}  // namespace parser
}  // namespace aurora

#endif  // MAP_SRC_DATA_PROVIDER_CONFIG_CFG_DESC_H
