#include "cfg_desc.h"

#include <assert.h>
#include <memory.h>

#include <map>
#include <string>

#include "config/file_reader.h"
#include "config_data/cfg_data_def.h"
#include "errorcode.h"

namespace aurora {
namespace parser {

#define FAST_SEARCH_STYLE 0
#if FAST_SEARCH_STYLE
typedef ViewObjStyle* ViewObjStylePtr;
typedef ViewObjStylePtr* ViewObjStyleSet;
struct MapViewObjIndex {
  uint32_t min_layer_id;
  uint32_t max_layer_id;
  ViewObjStyleSet* view_obj_info;

  MapViewObjIndex() : min_layer_id(0), max_layer_id(0), view_obj_info(nullptr) {}

  ViewObjStyle* GetViewObjInfo(uint32_t layer_id, uint32_t scale) {
    if (view_obj_info == nullptr || layer_id < min_layer_id || layer_id > max_layer_id) {
      return nullptr;
    }
    ViewObjStyleSet& infos = view_obj_info[layer_id - min_layer_id];
    if (infos != nullptr) {
      return infos[scale];
    }
    return nullptr;
  }

  ~MapViewObjIndex() {
    if (view_obj_info != nullptr) {
      delete[] view_obj_info;
      view_obj_info = nullptr;
    }
  }
};
#else
typedef std::vector<ViewObjStyle> ViewObjStyleSet;
struct MapViewObjInfo {
  uint32_t layer_id;
  ViewObjStyleSet view_style_set;
};
// index struct for retrieve layer style information in more good performance
struct MapViewObjIndex {
  uint32_t min_layer_id;
  uint32_t max_layer_id;
  uint32_t layer_id_nums;
  MapViewObjInfo* view_obj_info;

  MapViewObjIndex() : min_layer_id(0), max_layer_id(0), layer_id_nums(0), view_obj_info(nullptr) {}

  ~MapViewObjIndex() {
    if (view_obj_info != nullptr) {
      delete[] view_obj_info;
      view_obj_info = nullptr;
    }
  }
};
#endif

CfgDesc::CfgDesc()
    : map_view_mode_(nullptr),
      view_scale_info_set_(nullptr),
      route_cfg_(nullptr),
      match_cfg_(nullptr),
      direction_cfg_(nullptr),
      search_cfg_(nullptr),
      common_cfg_(nullptr),
      view_style_cfg_(nullptr),
      view_style_num_(0),
      view_obj_index_(nullptr),
      reader_(nullptr) {}

CfgDesc::~CfgDesc() { Clear(); }

void CfgDesc::Clear() {
  if (!reader_->IsApplyCache()) {
    if (map_view_mode_ != nullptr) {
      delete map_view_mode_;
      map_view_mode_ = nullptr;
    }

    if (view_scale_info_set_ != nullptr) {
      free(view_scale_info_set_);
      view_scale_info_set_ = nullptr;
    }

    if (view_style_cfg_ != nullptr) {
      free(view_style_cfg_);
      view_style_cfg_ = nullptr;
    }

    if (route_cfg_ != nullptr) {
      delete route_cfg_;
      route_cfg_ = nullptr;
    }

    if (match_cfg_ != nullptr) {
      delete match_cfg_;
      match_cfg_ = nullptr;
    }

    if (direction_cfg_ != nullptr) {
      delete direction_cfg_;
      direction_cfg_ = nullptr;
    }

    if (search_cfg_ != nullptr) {
      delete search_cfg_;
      search_cfg_ = nullptr;
    }

    if (common_cfg_ != nullptr) {
      delete common_cfg_;
      common_cfg_ = nullptr;
    }
  } else {
    map_view_mode_ = nullptr;
    view_scale_info_set_ = nullptr;
    view_style_cfg_ = nullptr;
    route_cfg_ = nullptr;
    match_cfg_ = nullptr;
    direction_cfg_ = nullptr;
    search_cfg_ = nullptr;
    common_cfg_ = nullptr;
    reader_->Clear();
  }

  view_style_num_ = 0;

  if (view_obj_index_ != nullptr) {
    delete view_obj_index_;
    view_obj_index_ = nullptr;
  }

  if (reader_ != nullptr) {
    delete reader_;
    reader_ = nullptr;
  }
}

int32_t CfgDesc::Init(const std::string& file_path) {
  if (reader_ == nullptr) {
#ifdef AURORA_DEBUG
    reader_ = new JsonReader();
#else
    reader_ = new FileReader();
#endif
  }
  if (reader_ != nullptr) {
    if (reader_->LoadFile(file_path)) {
      map_view_mode_ = reader_->GetMapViewMode();
      view_scale_info_set_ = reader_->GetViewScaleInfoSet();
      route_cfg_ = reader_->GetMapRouteCfg();
      match_cfg_ = reader_->GetMapMatchCfg();
      direction_cfg_ = reader_->GetMapDirectionCfg();
      search_cfg_ = reader_->GetMapSearchCfg();
      common_cfg_ = reader_->GetMapCommonCfg();
      view_style_num_ = reader_->GetLayerCfg(view_style_cfg_);

      if (view_style_cfg_ != nullptr && view_style_num_ > 0) {
        BuildStyleObjIndex(view_style_cfg_, view_style_num_);
      }

      return ErrorCode::kErrorCodeOk;
    }
  }
  return ErrorCode::kErrorCodeFileOpen;
}

void* CfgDesc::GetViewObjStyle(uint32_t map_scale, uint32_t layer_id) {
  if (view_obj_index_ != nullptr) {
    return FindViewObjStyle(layer_id, map_scale, view_obj_index_);
  }

  return nullptr;
}

void* CfgDesc::FindViewObjStyle(uint32_t layer_id, uint32_t map_scale, MapViewObjIndex* view) {
#if FAST_SEARCH_STYLE
  if (view != nullptr) {
    return view->GetViewObjInfo(layer_id, map_scale);
  }
  return nullptr;
#else
  if (layer_id > view->max_layer_id || layer_id < view->min_layer_id) {
    return nullptr;
  }
  uint32_t begin = 0;
  uint32_t end = view->layer_id_nums - 1;
  while (begin <= end) {
    uint32_t m = (begin + end) / 2;
    if (view->view_obj_info[m].layer_id < layer_id) {
      begin = m + 1;
    } else if (view->view_obj_info[m].layer_id > layer_id) {
      end = m - 1;
    } else {
      if (view->view_obj_info[m].view_style_set.size() > 0) {
        uint32_t s = 0;
        uint32_t e = view->view_obj_info[m].view_style_set.size();
        for (uint32_t d = 0; d < e; d++) {
          if (view->view_obj_info[m].view_style_set[d].min_view_scale <= map_scale &&
              view->view_obj_info[m].view_style_set[d].max_view_scale >= map_scale)
            return &view->view_obj_info[m].view_style_set[d];
        }
      }
      return nullptr;
    }
  }
  return nullptr;
#endif
}

int32_t CfgDesc::BuildStyleObjIndex(ViewObjStyle* obj, uint32_t num) {
  if (obj == nullptr || num == 0) {
    return ErrorCode::kErrorCodeFailed;
  }

#if FAST_SEARCH_STYLE
  if (view_scale_info_set_ == nullptr || view_scale_info_set_->nums == 0) {
    return ErrorCode::kErrorCodeFailed;
  }
  uint32_t min_layer = obj->layer_id;
  uint32_t max_layer = obj->layer_id;
  for (uint32_t i = 1; i < num; ++i) {
    if (min_layer > obj[i].layer_id) {
      min_layer = obj[i].layer_id;
    }
    if (max_layer < obj[i].layer_id) {
      max_layer = obj[i].layer_id;
    }
  }
  uint32_t count = max_layer - min_layer + 1;
  if (view_obj_index_ == nullptr) {
    view_obj_index_ = new MapViewObjIndex();
  }
  view_obj_index_->min_layer_id = min_layer;
  view_obj_index_->max_layer_id = max_layer;
  if (view_obj_index_->view_obj_info != nullptr) {
    delete[] view_obj_index_->view_obj_info;
  }
  view_obj_index_->view_obj_info = new ViewObjStyleSet[count];
  ::memset(view_obj_index_->view_obj_info, 0, sizeof(ViewObjStyleSet) * count);

  for (uint32_t i = 0; i < num; ++i) {
    ViewObjStyleSet& style_set = view_obj_index_->view_obj_info[obj[i].layer_id - min_layer];
    if (style_set == nullptr) {
      style_set = new ViewObjStylePtr[view_scale_info_set_->nums];
      ::memset(style_set, 0, sizeof(ViewObjStylePtr) * view_scale_info_set_->nums);
    }
    for (uint32_t s = obj[i].min_view_scale; s <= obj[i].max_view_scale; ++s) {
      style_set[s] = &obj[i];
    }
  }

#else
  if (view_obj_index_ == nullptr) {
    view_obj_index_ = new MapViewObjIndex();
  }
  std::map<uint32_t, std::vector<ViewObjStyle> > viewstyle_store;
  for (uint32_t i = 0; i < num; i++) {
    auto iter = viewstyle_store.find(obj[i].layer_id);
    if (iter != viewstyle_store.end()) {
      iter->second.push_back(obj[i]);
    } else {
      std::vector<ViewObjStyle> views;
      views.push_back(obj[i]);
      viewstyle_store.insert(std::make_pair(obj[i].layer_id, views));
    }
  }

  view_obj_index_->min_layer_id = viewstyle_store.begin()->first;
  view_obj_index_->max_layer_id = (--(viewstyle_store.end()))->first;
  view_obj_index_->layer_id_nums = viewstyle_store.size();
  if (view_obj_index_->layer_id_nums > 0) {
    view_obj_index_->view_obj_info = new MapViewObjInfo[view_obj_index_->layer_id_nums];
    int i = 0;
    for (auto iter = viewstyle_store.begin(); iter != viewstyle_store.end(); ++iter) {
      view_obj_index_->view_obj_info[i].layer_id = iter->first;
      view_obj_index_->view_obj_info[i].view_style_set = iter->second;
      i++;
    }
  }
#endif
  return 0;
}

MapRouteCfg* CfgDesc::GetMapRouteCfg() const { return route_cfg_; }

MapDirectionCfg* CfgDesc::GetMapDirectionCfg() const { return direction_cfg_; }

MapSearchCfg* CfgDesc::GetMapSearchCfg() const { return search_cfg_; }

MapCommonCfg* CfgDesc::GetMapCommonCfg() const { return common_cfg_; }

MapMatchCfg* CfgDesc::GetMapMatchCfg() const { return match_cfg_; }

ViewScaleInfoSet* CfgDesc::GetViewScaleInfoSet() const { return view_scale_info_set_; }

}  // namespace parser
}  // namespace aurora
