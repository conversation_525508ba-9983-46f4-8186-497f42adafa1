#ifndef MAP_SRC_DATA_PROVIDER_INCLUDE_CONFIG_DATA_CFG_MANAGER_H_
#define MAP_SRC_DATA_PROVIDER_INCLUDE_CONFIG_DATA_CFG_MANAGER_H_

#include <string>
#include <vector>

#include "cfg_data_def.h"
#include "errorcode.h"
#include "export_type_def.h"

namespace aurora {

namespace parser {
class CfgDesc;

/**
 * @brief th interface class can be used retrieve the configuration inforation about map engine.
 *
 *
 *
 */

class AURORA_EXPORT CfgManager {
public:
  CfgManager();

  CfgManager(const std::string& path);

  virtual ~CfgManager();

  CfgManager(const CfgManager&) = delete;

  CfgManager& operator=(const CfgManager&) = delete;
  /**
   * @brief Initialize the context environment about configuration file
   * @param[in] path the path of configuration file.
   * @return @c 0 if initialization succeeds, @c  otherwise.
   */
  int32_t Init(const std::string& path);

  /**
   * @brief update the context environment about configuration file when modify the content of
   * configuration file.
   * @param[in] .
   * @return @c 0 if Reload succeeds, @c false otherwise.
   */
  int32_t Reload();

  /**
   * @brief retrieve the render information of layer data which the data can be poi, road,
   * background etc.
   * @param[in] map_scale, map scale.
   * @param[in] layer_id  the layer index for data rendered
   * @return @c data content if exist, @c null pointer otherwise.
   */
  ViewObjStyle* GetViewObjStyle(uint32_t map_scale, uint32_t layer_id) const;

  /**
   * @brief retrieve the route plan configuration information
   * @return
   */
  MapRouteCfg* GetMapRouteCfg() const;

  /**
   * @brief retrieve the guidance configuration information.
   * @return
   */
  MapDirectionCfg* GetMapDirectionCfg() const;

  /**
   * @brief retrieve the all map scale information, map scale index and corresponding value
   * @return @c data content if succeeds, @c null point otherwise.
   */
  ViewScaleInfoSet* GetViewScaleInfo() const;

private:
  CfgDesc* desc_;
  std::string file_path_;
};

}  // namespace parser
}  // namespace aurora

#endif  // MAP_SRC_DATA_PROVIDER_INCLUDE_CONFIG_DATA_CFG_MANAGER_H_
