/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "map_render_data_fetcher.h"
#include "render_data_cache_manager.h"

namespace aurora {

    RenderDataFetcher::RenderDataFetcher() 
    {
        cache_manager_ = std::make_shared<DataCacheManager>();
    } 

    RenderDataFetcher::~RenderDataFetcher()
    {
    }

    bool RenderDataFetcher::Init()
    {
        if (nullptr == cache_manager_) {
            cache_manager_ = std::make_shared<DataCacheManager>();
        }
        if (cache_manager_ ) {
            cache_manager_->Init();
        }
        return true;
    }

    void RenderDataFetcher::FetchRenderTileData(const std::vector<TileID>& req_tile_ids, std::vector<TileID>& not_cached_tile_ids, 
                            std::vector<std::shared_ptr<CacheTile>>& cached_tile_datas)
    {
        // TODO: 缓存策略实现后，这里放开
         if (cache_manager_)
         {
            cache_manager_->FetchRenderTileData(req_tile_ids, not_cached_tile_ids, cached_tile_datas);
        }
    
    }

    bool RenderDataFetcher::FetchRenderTileDataById(const TileID& id, std::shared_ptr<CacheTile>& tile_data)
    {
        if (cache_manager_)
        {
            return cache_manager_->FetchRenderTileDataById(id, tile_data);
        }
        return false;
    }

    bool RenderDataFetcher::IsRenderTileDataCached(const TileID& id)
    {
        if (cache_manager_)
        {
            std::shared_ptr<CacheTile> tile_data;   
            return cache_manager_->FetchRenderTileDataById(id, tile_data);
        }
        return false;
    }

    void RenderDataFetcher::CacheRenderTileData(const TileID& id, const std::shared_ptr<CacheTile>& tile_data, bool add_to_active)
    {
        if (cache_manager_)
        {
            cache_manager_->CacheRenderTileData(id, tile_data, add_to_active);
        }

    }

    void RenderDataFetcher::SetDataCacheStrategy()
    {
        if (cache_manager_)
        {
            // TODO 策略要设置
            // cache_manager_->SetCacheStrategy();
        }
    }

    void RenderDataFetcher::GetDataCacheStrategy()
    {

    }

} //namespace 
/* EOF */
