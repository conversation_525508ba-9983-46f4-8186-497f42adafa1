/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file render_data_cache_manager.h
 * @brief Declaration file of class DataCacheManager.
 * @attention used for C/C++ only.
 */

#ifndef MAPRENDER_DATACACHE_MANAGER_H_
#define MAPRENDER_DATACACHE_MANAGER_H_

#include <memory>
#include <vector>
#include <map>
#include "util_tile_define.h"

namespace aurora {

class RenderDataCacheIF;
struct CacheTile;

class DataCacheManager {
public:
    // 定义缓存策略的枚举
    enum class CacheStrategy {
        LRU,
        LFU,
        // 可按需添加其他缓存策略
    };

    DataCacheManager();              
    ~DataCacheManager();   

    void Init();

    void FetchRenderTileData(const std::vector<TileID>& req_tile_ids, std::vector<TileID>& not_cached_tile_ids, 
                            std::vector<std::shared_ptr<CacheTile>>& cached_tile_datas);

    bool FetchRenderTileDataById(const TileID& id, std::shared_ptr<CacheTile>& tile_data);

    void CacheRenderTileData(const TileID& id, const std::shared_ptr<CacheTile>& tile_data, bool add_to_active = true);

    void RemoveRenderTileData(const TileID& id);

    void SetCacheStrategy(const CacheStrategy strategy);

    void CacheRenderData();

private:
    // 将 cache_ 改为 std::map，键为缓存策略，值为对应的缓存指针
    std::map<CacheStrategy, std::shared_ptr<RenderDataCacheIF>> cache_;
    CacheStrategy current_strategy_ = CacheStrategy::LRU;
};
    
} //namespace 

#endif //MAPRENDER_DATACACHE_MANAGER_H_
/* EOF */
