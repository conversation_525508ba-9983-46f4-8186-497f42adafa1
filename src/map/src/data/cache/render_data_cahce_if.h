/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file render_data_cache_if.h
 * @brief Declaration file of class RenderDataCacheIF.
 * @attention used for C/C++ only.
 */

 #ifndef MAPRENDER_DATA_CACHE_IF_H_
 #define MAPRENDER_DATA_CACHE_IF_H_
 
 #include <memory>
 #include "util_tile_define.h"

 namespace aurora {

 struct CacheTile;
 
 /**
 * class breif description
 *
 * RenderDataCacheIF
 *
 */
    
 class RenderDataCacheIF {
 public:           
    virtual ~RenderDataCacheIF() = default;   

   /// @brief  获取当前屏幕渲染的tile
   /// @return 成功返回: true, 失败返回: false
   virtual bool GetRenderTileData() noexcept = 0;

   /// @brief  通过tile id获取tile
   /// @return 成功返回: true, 失败返回: false
   virtual bool GetRenderTileDataById(const TileID& id, std::shared_ptr<CacheTile>& tile_data) noexcept = 0;

   /// @brief  获取渲染数据
   /// @return 成功返回: true, 失败返回: false
   virtual bool GetRenderData() noexcept = 0;

   /// @brief  通过tile id获取渲染数据
   /// @return 成功返回: true, 失败返回: false
   virtual bool GetRenderDataById(const TileID& id) noexcept = 0;

   /// @brief  验证数据有效性
   /// @return 成功返回: true, 失败返回: false
   virtual bool IsValid() const noexcept = 0;

   /// @brief  将数据加入缓存中
   /// @param id 数据所在的tile id
   virtual void AddCache() noexcept = 0;

   /// @brief  将tile数据加入缓存中
   /// @param id 数据所在的tile id
   virtual void AddRenderTileCache(const TileID& id, const std::shared_ptr<CacheTile>& tile_data, bool add_to_active = true) noexcept = 0;

   /// @brief 删除对应tile id的缓存数据
   /// @param id 数据所在的tile ids
   virtual void RemoveCache(const TileID& id) noexcept = 0;
 };
     
 } //namespace 
 
 #endif //MAPRENDER_DATA_CACHE_IF_H_
 /* EOF */
