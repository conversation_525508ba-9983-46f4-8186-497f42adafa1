#include "render_data_cache_lru.h"

namespace aurora {

RenderDataCacheLRU::RenderDataCacheLRU(size_t active_capacity, size_t preload_capacity)
    : active_cache_(active_capacity), preload_cache_(preload_capacity) {}

bool RenderDataCacheLRU::GetRenderTileDataById(const TileID& id, CacheTilePtr& tile_data) noexcept {
    // 优先查找active缓存（加锁）
    {
        std::lock_guard<std::mutex> lock(active_cache_.mutex);  // 锁定active缓存的互斥锁
        auto it_active = active_cache_.map.find(id);
        if (it_active != active_cache_.map.end()) {
            tile_data = it_active->second->second;
            MoveToFront(active_cache_, id);  // 访问后标记为最近使用（已在锁保护下）
            return true;
        }
    }

    // 再查找preload缓存（加锁）
    {
        std::lock_guard<std::mutex> lock(preload_cache_.mutex);  // 锁定preload缓存的互斥锁
        auto it_preload = preload_cache_.map.find(id);
        if (it_preload != preload_cache_.map.end()) {
            tile_data = it_preload->second->second;
            MoveToFront(preload_cache_, id);  // 访问后标记为最近使用（已在锁保护下）
            return true;
        }
    }

    return false;
}

void RenderDataCacheLRU::AddRenderTileCache(const TileID& id, const CacheTilePtr& tile_data, bool add_to_active) noexcept {
    LRUCache& target_cache = add_to_active ? active_cache_ : preload_cache_;
    std::lock_guard<std::mutex> lock(target_cache.mutex);  // 锁定目标缓存的互斥锁

    // 若已存在则更新值并移动到头部
    auto it = target_cache.map.find(id);
    if (it != target_cache.map.end()) {
        it->second->second = tile_data;
        MoveToFront(target_cache, id);
        return;
    }

    // 新增条目到列表头部
    target_cache.list.emplace_front(id, tile_data);
    target_cache.map[id] = target_cache.list.begin();

    // 超过容量时淘汰最久未使用的条目
    EvictIfNeeded(target_cache);
}

void RenderDataCacheLRU::MoveToFront(LRUCache& cache, const TileID& id) {
    // 注意：调用此函数时必须已持有cache.mutex的锁
    auto it = cache.map.find(id);
    if (it != cache.map.end()) {
        cache.list.splice(cache.list.begin(), cache.list, it->second);  // 移动到列表头部
    }
}

void RenderDataCacheLRU::EvictIfNeeded(LRUCache& cache) {
    // 注意：调用此函数时必须已持有cache.mutex的锁
    while (cache.list.size() > cache.capacity) {
        // 移除列表尾部（最久未使用）
        const auto& tail = cache.list.back();
        cache.map.erase(tail.first);
        cache.list.pop_back();
    }
}

void RenderDataCacheLRU::RemoveCache(const TileID& id) noexcept {
    // 处理active缓存（加锁）
    {
        std::lock_guard<std::mutex> lock(active_cache_.mutex);
        auto it_active = active_cache_.map.find(id);
        if (it_active != active_cache_.map.end()) {
            active_cache_.list.erase(it_active->second);
            active_cache_.map.erase(it_active);
            return;
        }
    }

    // 处理preload缓存（加锁）
    {
        std::lock_guard<std::mutex> lock(preload_cache_.mutex);
        auto it_preload = preload_cache_.map.find(id);
        if (it_preload != preload_cache_.map.end()) {
            preload_cache_.list.erase(it_preload->second);
            preload_cache_.map.erase(it_preload);
            return;
        }
    }
}

bool RenderDataCacheLRU::GetRenderTileData() noexcept {
    return false;
}

bool RenderDataCacheLRU::GetRenderData() noexcept {
    return false;
}

bool RenderDataCacheLRU::IsValid() const noexcept {
    return true;
}

void RenderDataCacheLRU::AddCache() noexcept {
    // 这里可根据需求实现将数据加入缓存的逻辑
    // 示例不做处理，实际使用时需完善
}

bool RenderDataCacheLRU::GetRenderDataById(const TileID& id) noexcept {
    return false;
}

    
} // namespace aurora
