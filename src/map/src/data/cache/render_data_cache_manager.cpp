/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

// #include <future>
#include "render_data_cache_manager.h"
#include "render_data_cache_lru.h"
#include <algorithm>

namespace aurora {

    const uint32_t max_buffer_size = 500;
    const uint32_t max_preload_buffer_size = 500;
    DataCacheManager::DataCacheManager() 
    {
        //保证最少有一个缓存
        if (cache_.empty()) {
            // 最少保证有一个默认的
            if (current_strategy_ == CacheStrategy::LRU) {
                std::shared_ptr<RenderDataCacheIF> lru_cache =  std::make_shared<RenderDataCacheLRU>(max_buffer_size, max_preload_buffer_size);
                cache_[current_strategy_] = lru_cache;
            }
           
        }
    }   

    DataCacheManager::~DataCacheManager()
    {

    }

    void DataCacheManager::Init()
    {

    }

    void DataCacheManager::FetchRenderTileData(const std::vector<TileID>& req_tile_ids, std::vector<TileID>& not_cached_tile_ids,
                            std::vector<std::shared_ptr<CacheTile> >& cached_tile_datas)
    {
        not_cached_tile_ids = req_tile_ids;
        if (cache_.find(current_strategy_) != cache_.end()) {
           auto cache = cache_[current_strategy_];
           for (auto& id : req_tile_ids) {
                std::shared_ptr<CacheTile> tile_data;
                if (cache->GetRenderTileDataById(id, tile_data)) {
                    cached_tile_datas.push_back(tile_data);
                    // 从 not_cached_tile_ids 中移除找到的 id
                    not_cached_tile_ids.erase(
                        std::remove(not_cached_tile_ids.begin(), not_cached_tile_ids.end(), id),
                        not_cached_tile_ids.end()
                    );
                }
            }
        }
    }

    bool DataCacheManager::FetchRenderTileDataById(const TileID& id, std::shared_ptr<CacheTile>& tile_data)
    {
        if (cache_.find(current_strategy_) != cache_.end()) {
           auto cache = cache_[current_strategy_];
            if (cache->GetRenderTileDataById(id, tile_data)) {
                return true;
            }
        }
        return false;
    }

    void DataCacheManager::CacheRenderTileData(const TileID& id, const std::shared_ptr<CacheTile>& tile_data, bool add_to_active)
    {
         if (cache_.find(current_strategy_) != cache_.end()) {
            auto cache = cache_[current_strategy_];
            cache->AddRenderTileCache(id, tile_data, add_to_active);
            
        }
    }

    void DataCacheManager::RemoveRenderTileData(const TileID& id)
    {
         if (cache_.find(current_strategy_) != cache_.end()) {
            auto cache = cache_[current_strategy_];
            cache->RemoveCache(id);
            
        }
    }

    void DataCacheManager::SetCacheStrategy(const CacheStrategy strategy)
    {
        current_strategy_ = strategy;
    }


    void DataCacheManager::CacheRenderData()
    {

    }

} //namespace 
/* EOF */
