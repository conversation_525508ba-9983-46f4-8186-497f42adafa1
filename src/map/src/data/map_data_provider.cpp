/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "map_data_provider.h"
#include "data_provider.h"
#include "display_data/display_tile_reader.h"
#include "display_data/display_data_def.h"
#include <mutex>
#include <thread>
#include <condition_variable> // 新增头文件
#include "map_render_camera_if.h"
#include "map_render_data_fetcher.h"

namespace aurora {

    static std::map<int64_t, AABB2<Point2d> > global_tile_mbrs_map; // 所有的tile是唯一的，缓存下mbr
    static std::mutex global_tile_mbrs_mutex; // 新增全局互斥锁
    static std::condition_variable global_tile_mbrs_cv; // 新增全局条件变量

    namespace parser {
        class TileReceiver : public TileDataReceiver
        {
            public:
            TileReceiver(std::vector<MapTileDataReceiverPtr> data_recerviers, CameraPtr& camera, uint32_t logic_level, std::shared_ptr<RenderDataFetcher> data_cacher)
             {
                 data_recerviers_ = data_recerviers;
                 request_camera_ = camera;
                 if (logic_level > 0) {
                    request_level_ = logic_level;
                 }
                 else {
                    request_level_ = camera->GetMapScale();
                 }
                 data_cacher_ = data_cacher;
                 
             }   
            virtual void OnTileData(uint32_t track_id, DisplayTilePackagePtr ptr) {
                for (auto& data_receiver : data_recerviers_) {
                     DisplayTileReader reader;
                    reader.SetTarget(ptr);
                    #ifdef DEBUG
                    printf("++++++++++tick:%ld OnTileData  %d, %d, %d, request_level:%d, data_receiver:%p\n", 
                        std::chrono::duration_cast<std::chrono::milliseconds>(Clock::now().time_since_epoch()).count(),  reader.GetTileID().row, reader.GetTileID().col, reader.GetTileID().level, request_level_, data_receiver.get());
                    #endif
                    {
                        std::lock_guard<std::mutex> lock(global_tile_mbrs_mutex); // 加锁
                        if (global_tile_mbrs_map.count(reader.GetTileID().value) == 0) 
                        {
                            global_tile_mbrs_map[reader.GetTileID().value] = *reader.GetMbr();
                            #ifdef DEBUG
                            printf("mbr: %f, %f, %f, %f\n", reader.GetMbr()->minpt().x(), reader.GetMbr()->minpt().y(), reader.GetMbr()->maxpt().x(), reader.GetMbr()->maxpt().y());
                            #endif
                            global_tile_mbrs_cv.notify_all(); // 通知所有等待的线程
                        }
                        // 对于请求的时候关注这个tilelist里每个tile的camera，回到一个就更新一个到camera
                        std::map<int64_t, AABB2<Point2d>> update_mbrs;
                        auto tile_id = reader.GetTileID();
                        if (request_level_ != tile_id.level) {
                            // 比例尺变化了，需要更新mbr
                            tile_id.level = request_level_;
                        }
                        // TileID cached_tile_id(tile_id.row, tile_id.col, request_level_);
                        // if (data_cacher_ && data_cacher_->IsRenderTileDataCached(cached_tile_id)) { // 已经通知过且缓存过做好的数据了
                        //     return;
                        // }
                    }
                    data_receiver->OnTileData(track_id, ptr, request_level_, data_cacher_, request_camera_);
                }
            }
            virtual void OnNullTiles(uint32_t track_id, std::vector<parser::DisplayTileID>& tiles)
            {
                for (auto& data_receiver : data_recerviers_) {
                    data_receiver->OnNullTiles(track_id, tiles, request_level_);
                }
            }
            virtual void OnAllTileData(uint32_t track_id, std::vector<DisplayTilePackagePtr>& tiles) {
                for (auto& data_receiver : data_recerviers_) {
                    data_receiver->OnAllTileData(track_id, tiles, request_level_, data_cacher_, request_camera_);
                }
            }
            
        private:
            std::vector<MapTileDataReceiverPtr> data_recerviers_;
            CameraPtr request_camera_;
            uint32_t request_level_; // 新增请求的level，camera可能在请求过程中比例尺又变化了，这里在请求的时候记录
            std::shared_ptr<RenderDataFetcher> data_cacher_;

        };
    };

    MapDataProvider::MapDataProvider(const std::string& data_path)  {
        data_path_ = data_path;
        data_provider_ = std::make_shared<aurora::parser::DataProvider>();
        if (data_provider_ && data_path_ != "") {
            data_provider_->InitDisplayParser(data_path_.c_str());
        }
    }

    MapDataProvider::~MapDataProvider() {

    }

    bool MapDataProvider::Init(const std::string& data_path) {
        data_path_ = data_path;
        if (data_provider_ && data_path_ != "") {
            data_provider_->InitDisplayParser(data_path_.c_str());
        }
        return true;
    }

    void MapDataProvider::AddDataReceiver(MapTileDataReceiverPtr data_receiver) {
        std::lock_guard<std::recursive_mutex> lock(data_receivers_mutex_); // Use recursive mutex
        if (data_receiver == nullptr) {
            return;
        }
        if (std::find(data_receivers_.begin(), data_receivers_.end(), data_receiver) != data_receivers_.end()) {
            return;
        }
        data_receivers_.push_back(data_receiver);
    }

    void MapDataProvider::RemoveDataReceiver(MapTileDataReceiverPtr data_receiver) {
        std::lock_guard<std::recursive_mutex> lock(data_receivers_mutex_); // Use recursive mutex
        if (data_receiver == nullptr) {
            return;
        }
        auto it = std::find(data_receivers_.begin(), data_receivers_.end(), data_receiver);
        if (it != data_receivers_.end()) {
            data_receivers_.erase(it);
        }
    }

    bool MapDataProvider::GetDisplayTileIDs(int level, const AABB2<Point2d>& mbr, aurora::parser::DisplayTileIDSet& tiles)
    {
        if (data_provider_ == nullptr || mbr.maxpt().x() == 0.0 || mbr.maxpt().y() == 0.0) {
            printf("data_provider_ [%p]is null or mbr is invalid\n", data_provider_.get());
            return false;
        }
        // Bug fix: Remove 'bool' keyword
        
        data_provider_->GetDisplayTileIDByMbr(level, mbr, tiles);
        #ifdef DEBUG
        printf("levle:%d mbr: %f, %f, %f, %f tiles:%d\n", level, mbr.minpt().x(), mbr.minpt().y(), mbr.maxpt().x(), mbr.maxpt().y(), tiles.size());
        #endif
        if (tiles.empty()) {
            printf("levle:%d tiles is empty \n", level);
            return false;
        }
        return true;
    }

    bool MapDataProvider::GetDisplayData(int level, const AABB2<Point2d>& mbr,  
                                        aurora::parser::DisplayTileIDSet& tiles,
                                        CameraPtr camera, uint32_t track_id) 
    {
        if (GetDisplayTileIDs(level, mbr, tiles) == false) {
            printf("GetDisplayTileIDs failed\n");
            return false;
        }
       
        RequestDisplayTilePackageByID(tiles,  camera, track_id);
        // Note: You may need to handle the thread lifecycle properly
        
        return true;
    }

    void MapDataProvider::RequestDisplayTilePackageByID(const  std::vector<aurora::parser::DisplayTileID>& ids, 
                                                        CameraPtr camera, uint32_t track_id, uint32_t logic_level,
                                                        const std::shared_ptr<RenderDataFetcher>& data_cacher) 
    {
        if (data_provider_ == nullptr || ids.empty()) {
            printf("data_provider_ is null or ids is empty\n");
            return;
        }
        std::shared_ptr<parser::TileReceiver>  receiver;
        {
            std::lock_guard<std::recursive_mutex> lock(data_receivers_mutex_); // Use recursive mutex
            #ifdef DEBUG
            printf("data_provider_  get tile size:%d\n", ids.size());
            #endif
            // tiledata的回调本地是同步，在线是异步的，receiver不能用局部变量
            receiver = std::make_shared<parser::TileReceiver>(data_receivers_, camera, logic_level, data_cacher);
            // TODO:RequestDisplayTilePackageByID 在线需要改造，传的临时变量在异步的时候会被释放
           
        }
        data_provider_->RequestDisplayTilePackageByID(track_id, ids, *receiver.get());
        
    }


}