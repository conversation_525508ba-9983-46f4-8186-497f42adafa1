/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file map_render_data_fetcher.h
 * @brief Declaration file of class RenderDataFetcher.
 * @attention used for C/C++ only.
 */

#ifndef MAPRENDER_DATAFETCHER_H_
#define MAPRENDER_DATAFETCHER_H_
#include <memory>
#include "util_tile_define.h"
#include <vector>

namespace aurora {

class DataCacheManager;
struct CacheTile;

/**
* class breif description
*
* RenderDataFetcher
*
*/

class RenderDataFetcher {
public:
    RenderDataFetcher();              
    ~RenderDataFetcher();   

    bool Init();

    void FetchRenderTileData(const std::vector<TileID>& req_tile_ids, std::vector<TileID>& not_cached_tile_ids, 
                            std::vector<std::shared_ptr<CacheTile>>& cached_tile_datas);

    bool FetchRenderTileDataById(const TileID& id, std::shared_ptr<CacheTile>& tile_data);

    bool IsRenderTileDataCached(const TileID& id);

    void CacheRenderTileData(const TileID& id, const std::shared_ptr<CacheTile>& tile_data, bool add_to_active = true);

    void RemoveRenderTileData(const TileID& id);

    void SetDataCacheStrategy();

    void GetDataCacheStrategy();

private:
    std::shared_ptr<DataCacheManager>  cache_manager_;
};
    
} //namespace 

#endif //MAPRENDER_DATAFETCHER_H_
/* EOF */
