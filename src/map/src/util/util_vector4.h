/**
 * Copyright @ 2025 
 * All Rights Reserved.
 */

#ifndef MAPRENDERSERVICE_UTIL_VECTOR_4_H_
#define MAPRENDERSERVICE_UTIL_VECTOR_4_H_

#include "util_math.h"

namespace aurora
{
    // 4 维点/向量
    template <typename T>
    class Vector4
	{
	public:
        Vector4()
            : x(0)
            , y(0)
            , z(1)
            , w(1)
        {}

		Vector4(T ax, T ay, T az, T aw)
            : x(ax)
            , y(ay)
            , z(az)
            , w(aw)
		{}

        Vector4(const Vector4& rhs)
            : x(rhs.x)
            , y(rhs.y)
            , z(rhs.z)
            , w(rhs.w)
        {}

		~Vector4(){};

	public:
        Vector4& operator=(const Vector4& rhs)
        {
            if (&rhs != this)
            {
                x = rhs.x;
                y = rhs.y;
                z = rhs.z;
                w = rhs.w;
            }

            return *this;
        }

        template <typename U>
        operator Vector4<U>() const
        {
            return Vector4<U>(static_cast<U>(x), static_cast<U>(y), static_cast<U>(z), static_cast<U>(w));
        }

	public:
		T x;
		T y;
		T z;
		T w;
	};

} //namespace 

#endif // MAPRENDERSERVICE_UTIL_VECTOR_4_H_
