/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "map_render_state.h"
#include "scale_converter.h"

namespace aurora {

    MapRenderState::MapRenderState()
    : map_scale_(TILE_LOGIC_LEVEL_15)
    , map_level_(0)
    , map_rotation_(0.)
    , map_center_(0.,0.)
    , camera_(nullptr)
    , screen_width_(0)
    , screen_height_(0)
    {

    }

    MapRenderState::~MapRenderState()
    {

    }

    void MapRenderState::SetMapScale(float scale, const uint32_t animation_duration_ms)
    {
        if (map_scale_ == scale) {
            return;
        }
        map_scale_ = scale;
        std::lock_guard<std::recursive_mutex > lock(map_camera_mutex_);
        if (camera_ == nullptr) {
            return;
        }
        
        if (animation_duration_ms == 0) {
            printf("MapRenderState::SetMapScale, camera:%p, scale %f\n", camera_.get(), map_scale_);
            camera_->SetMapScale(map_scale_);
        }
        else {
            AnimationOptions animation(Milliseconds(animation_duration_ms), camera_->GetMapScale(), map_scale_, AnimationType::kAnimationTypeZoomTo);
            printf("MapRenderState::SetMapScale11, camera:%p, scale %f\n", camera_.get(), map_scale_);
            camera_->SetMapScale(map_scale_, animation);
        }
    }

    void MapRenderState::SetMapCenter(double lon, double lat, const uint32_t animation_duration_ms)
    {
        if (map_center_.longitude_ == lon && map_center_.latitude_ == lat) {
            return;
        }
        map_center_.longitude_ = lon;
        map_center_.latitude_ = lat;
        std::lock_guard<std::recursive_mutex > lock(map_camera_mutex_);
        if (camera_ == nullptr) {
            return;
        }
        if (animation_duration_ms  == 0) {
            camera_->SetMapCenter(map_center_.longitude_, map_center_.latitude_);
            return;
        }
        AnimationOptions animation(Milliseconds(animation_duration_ms), 
            {camera_->GetMapCenter().longitude_, camera_->GetMapCenter().latitude_}, 
            {map_center_.longitude_, map_center_.latitude_}, AnimationType::kAnimationTypeMoveTo);
        camera_->SetMapCenter(map_center_.longitude_, map_center_.latitude_, animation);
    }

    void MapRenderState::SetMapRotation(float rot, const uint32_t animation_duration_ms)
    {
        if (map_rotation_ == rot) {
            return;
        }
        map_rotation_ = rot;
        std::lock_guard<std::recursive_mutex > lock(map_camera_mutex_);
        if (camera_ == nullptr) {
            return;
        }
        if (animation_duration_ms == 0) {
            camera_->SetMapRotation(map_rotation_);
            return;
        }
        AnimationOptions animation(Milliseconds(animation_duration_ms), camera_->GetMapRotation(), map_rotation_, AnimationType::kAnimationTypeRotateTo);
        camera_->SetMapRotation(map_rotation_, animation);
    }

    float MapRenderState::GetMapScale()
    {
         if (camera_ == nullptr) {
            return map_scale_;
        }
        return camera_->GetMapScale();
    }

    Lonlat MapRenderState::GetMapCenter()
    {
        if (camera_ == nullptr) {
            return map_center_;
        }
        return camera_->GetMapCenter();
    }

    float MapRenderState::GetMapRotation()
    {
        if (camera_ == nullptr) {
            return map_rotation_;
        }
        return camera_->GetMapRotation();
    }

    void MapRenderState::ScreenToMap()
    {

    }

    void MapRenderState::MapToScreen()
    {

    }

    void MapRenderState::AttachCamera(CameraPtr camera) {
        std::lock_guard<std::recursive_mutex > lock(map_camera_mutex_);
        camera_ = camera;
        UpdateCamera();
    }

    void MapRenderState::UpdateCamera() {
        std::lock_guard<std::recursive_mutex > lock(map_camera_mutex_);
        if (camera_ == nullptr) {
            return;
        }
        camera_->SetMapScale(map_scale_);
        camera_->SetMapCenter(map_center_.longitude_, map_center_.latitude_);
        camera_->SetMapRotation(map_rotation_);
        camera_->Update();
    }

    int32_t MapRenderState::SetScreenSize(const uint32_t& width, const uint32_t& height)
    {
        screen_width_ = width;
        screen_height_ = height;
        std::lock_guard<std::recursive_mutex > lock(map_camera_mutex_);
        if (camera_ == nullptr) {
            printf("camera is null\n");
            return -1;
        }
         printf("set camera w:%d h:%d\n", width, height);
        camera_->SetScreenSize(width, height);
        camera_->Update();
        return 0;
    }

    bool MapRenderState::GetScreenSize(uint32_t &width, uint32_t &height) {
        if (screen_width_ == 0 || screen_height_ == 0) {
            return false;
        }
        width = screen_width_;
        height = screen_height_;
        return true;
    }

    
} //namespace 