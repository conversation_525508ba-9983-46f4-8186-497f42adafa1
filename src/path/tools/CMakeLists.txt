# Tools
set(PATH_TOOLS_SRCS
    main.cpp
)

# Create tools
add_executable(path_tool ${PATH_TOOLS_SRCS})
add_dependencies(path_tool Boost_1_71_0)
target_link_libraries(path_tool aurora_path aurora_base Boost_INTERFACE)

# Create index_tile_test executable
add_executable(index_tile_test index_tile_test.cpp)
target_link_libraries(index_tile_test aurora_path aurora_base)

# Create boost_test executable
add_executable(boost_test boost_test.cpp)
add_dependencies(boost_test Boost_1_71_0)
target_link_libraries(boost_test Boost_INTERFACE)

# Create benchmark tool executable
add_executable(path_benchmark benchmark.cpp)
target_link_libraries(path_benchmark aurora_path aurora_base)

# 添加route server可执行文件
add_executable(simple_route_server
    simple_route_server.cpp
)

# 链接必要的库
target_link_libraries(simple_route_server
    aurora_path
    aurora_base
    pthread
)

# # 设置编译选项
# target_compile_features(simple_route_server PRIVATE cxx_std_17)
# target_compile_options(simple_route_server PRIVATE -Wall -Wextra)

# # 包含头文件目录
# target_include_directories(simple_route_server PRIVATE
#     ${CMAKE_SOURCE_DIR}/src/path/include
#     ${CMAKE_SOURCE_DIR}/src/base/include
#     ${CMAKE_SOURCE_DIR}/include
# )

# 安装目标
# install(TARGETS simple_route_server
#     RUNTIME DESTINATION bin
# )

# Create route baseline evaluator executable
add_executable(route_baseline_evaluator route_baseline_evaluator.cpp)
target_link_libraries(route_baseline_evaluator aurora_path aurora_base pthread)

# Install route baseline evaluator
install(TARGETS route_baseline_evaluator
    RUNTIME DESTINATION bin
)

# Create simple geojson test executable
add_executable(simple_geojson_test simple_geojson_test.cpp)
target_link_libraries(simple_geojson_test pthread)
