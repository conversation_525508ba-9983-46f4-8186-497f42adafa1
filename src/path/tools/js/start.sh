#!/bin/bash

# Leaflet + 高德地图应用启动脚本
# 用于快速启动本地开发服务器

echo "🗺️  Leaflet + 高德地图应用启动脚本"
echo "=================================="

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "📂 应用目录: $SCRIPT_DIR"

# 检查必要文件是否存在
required_files=("index.html" "main.js" "map-config.js" "coordinate-converter.js" "route-planner.js" "server.py")
missing_files=()

for file in "${required_files[@]}"; do
    if [ ! -f "$SCRIPT_DIR/$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo "❌ 缺少必要文件:"
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    echo "请确保所有文件都在正确位置"
    exit 1
fi

echo "✅ 所有必要文件检查完成"

# 检查端口是否可用
PORT=8000
while netstat -an 2>/dev/null | grep -q ":$PORT "; do
    echo "⚠️  端口 $PORT 已被占用，尝试端口 $((PORT+1))"
    PORT=$((PORT+1))
done

echo "🚀 准备启动服务器..."
echo "📍 端口: $PORT"
echo "🌐 访问地址: http://localhost:$PORT"
echo ""

# 启动服务器
cd "$SCRIPT_DIR"
echo "正在启动HTTP服务器..."
echo "按 Ctrl+C 停止服务器"
echo ""

# 尝试自动打开浏览器
if command -v xdg-open &> /dev/null; then
    echo "🌐 正在打开浏览器..."
    sleep 2 && xdg-open "http://localhost:$PORT" &
elif command -v open &> /dev/null; then
    echo "🌐 正在打开浏览器..."
    sleep 2 && open "http://localhost:$PORT" &
fi

# 启动Python服务器
python3 server.py -p $PORT
