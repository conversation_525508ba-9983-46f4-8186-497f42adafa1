/**
 * 路径规划模块
 * 使用高德地图API进行路径规划
 */

class RoutePlanner {
    constructor() {
        this.amap = null;
        this.driving = null;
        this.walking = null;
        this.riding = null;
        this.currentRoute = null;
        this.routeType = 'driving'; // driving, walking, riding
        this.isInitialized = false;
        this.initPromise = null;

        this.initPromise = this.initAMap();
    }
    
    /**
     * 初始化高德地图API
     */
    async initAMap() {
        try {
            // 等待高德地图API加载完成
            if (typeof AMap === 'undefined') {
                throw new Error('高德地图API未加载');
            }

            console.log('开始初始化高德地图路径规划服务...');

            // 创建地图实例（隐藏的，仅用于API调用）
            this.amap = new AMap.Map(document.createElement('div'));

            // 使用Promise包装插件加载
            await this.loadRoutingPlugins();

            this.isInitialized = true;
            console.log('高德地图路径规划服务初始化成功');
        } catch (error) {
            console.error('高德地图API初始化失败:', error);
            throw error;
        }
    }

    /**
     * 加载路径规划插件
     */
    async loadRoutingPlugins() {
        return new Promise((resolve, reject) => {
            // 加载路径规划相关插件
            AMap.plugin([
                'AMap.Driving',
                'AMap.Walking',
                'AMap.Riding'
            ], () => {
                try {
                    console.log('路径规划插件加载完成，开始初始化服务...');

                    // 初始化驾车路径规划
                    this.driving = new AMap.Driving({
                        map: null, // 不显示在地图上
                        policy: AMap.DrivingPolicy.LEAST_TIME // 最短时间策略
                    });

                    // 初始化步行路径规划
                    this.walking = new AMap.Walking({
                        map: null
                    });

                    // 初始化骑行路径规划
                    this.riding = new AMap.Riding({
                        map: null
                    });

                    console.log('所有路径规划服务初始化完成');
                    resolve();
                } catch (error) {
                    console.error('路径规划服务初始化失败:', error);
                    reject(error);
                }
            });
        });
    }
    
    /**
     * 设置路径规划类型
     * @param {string} type - driving, walking, riding
     */
    setRouteType(type) {
        if (['driving', 'walking', 'riding'].includes(type)) {
            this.routeType = type;
        }
    }

    /**
     * 检查是否已初始化
     * @returns {boolean}
     */
    isReady() {
        return this.isInitialized;
    }

    /**
     * 等待初始化完成
     * @returns {Promise}
     */
    async waitForInit() {
        if (!this.isInitialized) {
            await this.initPromise;
        }
        return this.isInitialized;
    }
    
    /**
     * 计算路径
     * @param {Array} startPoint - 起点坐标 [lng, lat]
     * @param {Array} endPoint - 终点坐标 [lng, lat]
     * @param {Array} waypoints - 途经点坐标数组 [[lng, lat], ...]
     * @returns {Promise} 路径规划结果
     */
    async calculateRoute(startPoint, endPoint, waypoints = []) {
        try {
            // 确保初始化完成
            if (!this.isInitialized) {
                console.log('等待路径规划服务初始化...');
                await this.initPromise;
            }

            console.log('开始计算路径:', {
                startPoint,
                endPoint,
                waypoints,
                routeType: this.routeType
            });

            return new Promise((resolve, reject) => {
                try {
                    // 转换坐标格式
                    const start = new AMap.LngLat(startPoint[0], startPoint[1]);
                    const end = new AMap.LngLat(endPoint[0], endPoint[1]);
                    const wayPointsAMap = waypoints.map(point => new AMap.LngLat(point[0], point[1]));

                    // 选择路径规划服务
                    let routeService;
                    switch (this.routeType) {
                        case 'walking':
                            routeService = this.walking;
                            break;
                        case 'riding':
                            routeService = this.riding;
                            break;
                        default:
                            routeService = this.driving;
                    }

                    // 检查服务是否已初始化
                    if (!routeService) {
                        reject(new Error(`路径规划服务 ${this.routeType} 未初始化`));
                        return;
                    }

                    // 执行路径规划
                    if (wayPointsAMap.length > 0 && this.routeType === 'driving') {
                        // 驾车支持途经点
                        console.log('正在计算驾车路径（含途经点）...');
                        routeService.search(start, end, {
                            waypoints: wayPointsAMap
                        }, (status, result) => {
                            if (status === 'complete') {
                                this.currentRoute = this.parseRouteResult(result);
                                resolve(this.currentRoute);
                            } else {
                                console.error('驾车路径规划失败:', result);
                                reject(new Error('驾车路径规划失败: ' + JSON.stringify(result)));
                            }
                        });
                    } else {
                        // 步行和骑行不支持途经点，或驾车无途经点
                        console.log(`正在计算${this.routeType}路径...`);
                        routeService.search(start, end, (status, result) => {
                            if (status === 'complete') {
                                this.currentRoute = this.parseRouteResult(result);
                                resolve(this.currentRoute);
                            } else {
                                console.error(`${this.routeType}路径规划失败:`, result);
                                reject(new Error(`${this.routeType}路径规划失败: ` + JSON.stringify(result)));
                            }
                        });
                    }
                } catch (error) {
                    reject(error);
                }
            });
        } catch (error) {
            throw error;
        }
    }
    
    /**
     * 解析路径规划结果
     * @param {Object} result - 高德API返回结果
     * @returns {Object} 解析后的路径数据
     */
    parseRouteResult(result) {
        const route = result.routes[0];
        const steps = route.steps;
        
        // 提取路径坐标点
        const coordinates = [];
        steps.forEach(step => {
            step.path.forEach(point => {
                coordinates.push([point.lng, point.lat]);
            });
        });
        
        // 计算总距离和时间
        const totalDistance = route.distance; // 米
        const totalTime = route.time; // 秒
        
        // 提取导航指令
        const instructions = steps.map(step => ({
            instruction: step.instruction,
            distance: step.distance,
            time: step.time,
            action: step.action
        }));
        
        return {
            coordinates,
            totalDistance,
            totalTime,
            instructions,
            type: this.routeType,
            raw: result
        };
    }
    
    /**
     * 获取当前路径
     * @returns {Object|null} 当前路径数据
     */
    getCurrentRoute() {
        return this.currentRoute;
    }
    
    /**
     * 清除当前路径
     */
    clearRoute() {
        this.currentRoute = null;
    }
    
    /**
     * 格式化距离显示
     * @param {number} distance - 距离(米)
     * @returns {string} 格式化后的距离
     */
    formatDistance(distance) {
        if (distance < 1000) {
            return Math.round(distance) + ' 米';
        } else {
            return (distance / 1000).toFixed(1) + ' 公里';
        }
    }
    
    /**
     * 格式化时间显示
     * @param {number} time - 时间(秒)
     * @returns {string} 格式化后的时间
     */
    formatTime(time) {
        const hours = Math.floor(time / 3600);
        const minutes = Math.floor((time % 3600) / 60);
        
        if (hours > 0) {
            return hours + ' 小时 ' + minutes + ' 分钟';
        } else {
            return minutes + ' 分钟';
        }
    }
    
    /**
     * 地理编码 - 地址转坐标
     * @param {string} address - 地址
     * @returns {Promise} 坐标结果
     */
    async geocode(address) {
        return new Promise((resolve, reject) => {
            AMap.plugin('AMap.Geocoder', () => {
                const geocoder = new AMap.Geocoder();
                geocoder.getLocation(address, (status, result) => {
                    if (status === 'complete' && result.geocodes.length > 0) {
                        const location = result.geocodes[0].location;
                        resolve([location.lng, location.lat]);
                    } else {
                        reject(new Error('地址解析失败'));
                    }
                });
            });
        });
    }
    
    /**
     * 逆地理编码 - 坐标转地址
     * @param {Array} coordinates - 坐标 [lng, lat]
     * @returns {Promise} 地址结果
     */
    async reverseGeocode(coordinates) {
        return new Promise((resolve, reject) => {
            AMap.plugin('AMap.Geocoder', () => {
                const geocoder = new AMap.Geocoder();
                const lnglat = new AMap.LngLat(coordinates[0], coordinates[1]);
                geocoder.getAddress(lnglat, (status, result) => {
                    if (status === 'complete' && result.regeocode) {
                        resolve(result.regeocode.formattedAddress);
                    } else {
                        reject(new Error('坐标解析失败'));
                    }
                });
            });
        });
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RoutePlanner;
}
