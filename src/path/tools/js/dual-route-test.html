<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双算路对比测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #1890ff;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0958d9;
        }
        .warning {
            background: #fffbe6;
            border: 1px solid #ffe58f;
            color: #d48806;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #e8e8e8;
            padding: 8px;
            text-align: left;
        }
        .comparison-table th {
            background: #f5f5f5;
            font-weight: bold;
        }
        .amap-result {
            background: #fff2f0;
            border-left: 4px solid #ff6b6b;
        }
        .cpp-result {
            background: #f6ffed;
            border-left: 4px solid #4CAF50;
        }
        .coordinate-input {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            align-items: center;
        }
        .coordinate-input input {
            padding: 5px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            width: 120px;
        }
        .coordinate-input label {
            min-width: 60px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 双算路对比测试</h1>
        <p>测试高德地图API与Path算路引擎的对比功能</p>

        <div class="test-section">
            <h3>🔧 服务状态检查</h3>
            <button onclick="checkServices()">检查服务状态</button>
            <div id="serviceStatus" class="status info">点击按钮检查服务状态</div>
        </div>

        <div class="test-section">
            <h3>📍 测试坐标设置</h3>
            <div class="coordinate-input">
                <label>起点:</label>
                <input type="number" id="startLng" placeholder="经度" value="116.4074" step="0.000001">
                <input type="number" id="startLat" placeholder="纬度" value="39.9042" step="0.000001">
                <span>天安门</span>
            </div>
            <div class="coordinate-input">
                <label>终点:</label>
                <input type="number" id="endLng" placeholder="经度" value="116.3974" step="0.000001">
                <input type="number" id="endLat" placeholder="纬度" value="39.9163" step="0.000001">
                <span>故宫</span>
            </div>
            <button onclick="setTestCoordinates('beijing')">北京测试点</button>
            <button onclick="setTestCoordinates('shanghai')">上海测试点</button>
            <button onclick="setTestCoordinates('guangzhou')">广州测试点</button>
        </div>

        <div class="test-section">
            <h3>🚀 双算路测试</h3>
            <button onclick="runDualRouteTest()" id="testBtn">开始双算路测试</button>
            <button onclick="clearResults()">清除结果</button>
            <div id="testStatus" class="status info">准备就绪，点击开始测试</div>
        </div>

        <div class="test-section">
            <h3>📊 对比结果</h3>
            <div id="comparisonResults">暂无测试结果</div>
        </div>

        <div class="test-section">
            <h3>📝 详细日志</h3>
            <button onclick="clearLog()">清除日志</button>
            <div id="testLog" class="log">等待测试开始...</div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=f7f401fd4ffb75720cba09ffb7b24a4c&plugin=AMap.Driving,AMap.Walking,AMap.Riding"></script>
    <script src="coordinate-converter.js"></script>
    <script src="route-planner.js"></script>
    <script src="cpp-route-client.js"></script>
    
    <script>
        let routePlanner = null;
        let cppRouteClient = null;
        let logElement = null;
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            logElement = document.getElementById('testLog');
            log('页面加载完成，开始初始化...');
            setTimeout(initializeServices, 1000);
        });
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            console.log(logMessage);
            
            if (logElement) {
                logElement.textContent += logMessage + '\n';
                logElement.scrollTop = logElement.scrollHeight;
            }
        }
        
        function setStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.className = `status ${type}`;
            }
        }
        
        function clearLog() {
            if (logElement) {
                logElement.textContent = '日志已清除\n';
            }
        }
        
        async function initializeServices() {
            try {
                log('初始化高德路径规划器...');
                routePlanner = new RoutePlanner();
                await routePlanner.waitForInit();
                log('✅ 高德路径规划器初始化完成');
                
                log('初始化Path算路客户端...');
                cppRouteClient = new CppRouteClient('http://localhost:8080');
                log('✅ Path算路客户端初始化完成');
                
                setStatus('serviceStatus', '服务初始化完成，可以开始测试', 'success');
                
            } catch (error) {
                log(`❌ 服务初始化失败: ${error.message}`);
                setStatus('serviceStatus', '服务初始化失败', 'error');
            }
        }
        
        async function checkServices() {
            setStatus('serviceStatus', '正在检查服务状态...', 'info');
            log('开始检查服务状态...');
            
            let amapOk = false;
            let cppOk = false;
            
            // 检查高德API
            try {
                if (typeof AMap !== 'undefined' && routePlanner && routePlanner.isReady()) {
                    amapOk = true;
                    log('✅ 高德地图API服务正常');
                } else {
                    log('❌ 高德地图API服务异常');
                }
            } catch (error) {
                log(`❌ 高德地图API检查失败: ${error.message}`);
            }
            
            // 检查C++服务器
            try {
                if (cppRouteClient) {
                    cppOk = await cppRouteClient.checkServerStatus();
                    if (cppOk) {
                        log('✅ Path算路服务器正常');
                    } else {
                        log('❌ Path算路服务器不可用');
                    }
                } else {
                    log('❌ Path算路客户端未初始化');
                }
            } catch (error) {
                log(`❌ Path算路服务器检查失败: ${error.message}`);
            }
            
            // 更新状态
            if (amapOk && cppOk) {
                setStatus('serviceStatus', '✅ 所有服务正常，可以进行双算路测试', 'success');
            } else if (amapOk) {
                setStatus('serviceStatus', '⚠️ 仅高德服务可用，C++服务不可用', 'warning');
            } else if (cppOk) {
                setStatus('serviceStatus', '⚠️ 仅C++服务可用，高德服务不可用', 'warning');
            } else {
                setStatus('serviceStatus', '❌ 所有服务都不可用', 'error');
            }
        }
        
        function setTestCoordinates(city) {
            const coordinates = {
                beijing: {
                    start: { lng: 116.4074, lat: 39.9042, name: '天安门' },
                    end: { lng: 116.3974, lat: 39.9163, name: '故宫' }
                },
                shanghai: {
                    start: { lng: 121.4737, lat: 31.2304, name: '人民广场' },
                    end: { lng: 121.5057, lat: 31.2453, name: '外滩' }
                },
                guangzhou: {
                    start: { lng: 113.2644, lat: 23.1291, name: '广州塔' },
                    end: { lng: 113.2765, lat: 23.1353, name: '珠江新城' }
                }
            };
            
            const coord = coordinates[city];
            if (coord) {
                document.getElementById('startLng').value = coord.start.lng;
                document.getElementById('startLat').value = coord.start.lat;
                document.getElementById('endLng').value = coord.end.lng;
                document.getElementById('endLat').value = coord.end.lat;
                
                log(`设置测试坐标: ${coord.start.name} → ${coord.end.name}`);
            }
        }
        
        async function runDualRouteTest() {
            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.textContent = '测试中...';
            
            try {
                setStatus('testStatus', '正在进行双算路测试...', 'info');
                log('开始双算路对比测试...');
                
                // 获取坐标
                const startLng = parseFloat(document.getElementById('startLng').value);
                const startLat = parseFloat(document.getElementById('startLat').value);
                const endLng = parseFloat(document.getElementById('endLng').value);
                const endLat = parseFloat(document.getElementById('endLat').value);
                
                if (isNaN(startLng) || isNaN(startLat) || isNaN(endLng) || isNaN(endLat)) {
                    throw new Error('请输入有效的坐标');
                }
                
                log(`测试坐标: 起点(${startLng}, ${startLat}) → 终点(${endLng}, ${endLat})`);
                
                // 坐标转换到GCJ02
                const startGCJ02 = CoordinateConverter.wgs84ToGcj02(startLng, startLat);
                const endGCJ02 = CoordinateConverter.wgs84ToGcj02(endLng, endLat);
                
                log(`GCJ02坐标: 起点(${startGCJ02[0]}, ${startGCJ02[1]}) → 终点(${endGCJ02[0]}, ${endGCJ02[1]})`);
                
                // 并行执行双算路
                const promises = [];
                
                // 高德算路
                if (routePlanner && routePlanner.isReady()) {
                    log('启动高德算路...');
                    promises.push(
                        routePlanner.calculateRoute(startGCJ02, endGCJ02, [])
                            .then(route => {
                                log('✅ 高德算路完成');
                                return { source: 'amap', route, success: true };
                            })
                            .catch(error => {
                                log(`❌ 高德算路失败: ${error.message}`);
                                return { source: 'amap', error, success: false };
                            })
                    );
                }
                
                // Path算路
                if (cppRouteClient) {
                    log('启动Path算路...');
                    promises.push(
                        cppRouteClient.calculateRoute(startGCJ02[0], startGCJ02[1], endGCJ02[0], endGCJ02[1])
                            .then(route => {
                                log('✅ Path算路完成');
                                return { source: 'cpp', route, success: true };
                            })
                            .catch(error => {
                                log(`❌ Path算路失败: ${error.message}`);
                                return { source: 'cpp', error, success: false };
                            })
                    );
                }
                
                if (promises.length === 0) {
                    throw new Error('没有可用的算路服务');
                }
                
                // 等待所有算路完成
                const results = await Promise.all(promises);
                
                // 处理结果
                let amapResult = null;
                let cppResult = null;
                
                results.forEach(result => {
                    if (result.source === 'amap' && result.success) {
                        amapResult = result.route;
                    } else if (result.source === 'cpp' && result.success) {
                        cppResult = result.route;
                    }
                });
                
                // 显示对比结果
                displayComparisonResults(amapResult, cppResult);
                
                if (amapResult || cppResult) {
                    setStatus('testStatus', '✅ 双算路测试完成', 'success');
                    log('🎉 双算路对比测试完成');
                } else {
                    setStatus('testStatus', '❌ 所有算路都失败了', 'error');
                    log('❌ 所有算路都失败了');
                }
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`);
                setStatus('testStatus', `测试失败: ${error.message}`, 'error');
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '开始双算路测试';
            }
        }
        
        function displayComparisonResults(amapRoute, cppRoute) {
            const resultsDiv = document.getElementById('comparisonResults');
            
            if (!amapRoute && !cppRoute) {
                resultsDiv.innerHTML = '<p style="color: #ff4d4f;">❌ 没有成功的算路结果</p>';
                return;
            }
            
            let html = '<table class="comparison-table">';
            html += '<thead><tr><th>项目</th><th>高德地图</th><th>Path算路</th><th>差异</th></tr></thead>';
            html += '<tbody>';
            
            // 距离对比
            if (amapRoute && cppRoute) {
                const distanceDiff = Math.abs(amapRoute.totalDistance - cppRoute.totalDistance);
                const distanceDiffPercent = ((distanceDiff / Math.min(amapRoute.totalDistance, cppRoute.totalDistance)) * 100).toFixed(1);
                
                html += '<tr>';
                html += '<td><strong>距离</strong></td>';
                html += `<td class="amap-result">${formatDistance(amapRoute.totalDistance)}</td>`;
                html += `<td class="cpp-result">${formatDistance(cppRoute.totalDistance)}</td>`;
                html += `<td>${formatDistance(distanceDiff)} (${distanceDiffPercent}%)</td>`;
                html += '</tr>';
                
                // 时间对比
                const timeDiff = Math.abs(amapRoute.totalTime - cppRoute.totalTime);
                const timeDiffPercent = ((timeDiff / Math.min(amapRoute.totalTime, cppRoute.totalTime)) * 100).toFixed(1);
                
                html += '<tr>';
                html += '<td><strong>时间</strong></td>';
                html += `<td class="amap-result">${formatTime(amapRoute.totalTime)}</td>`;
                html += `<td class="cpp-result">${formatTime(cppRoute.totalTime)}</td>`;
                html += `<td>${formatTime(timeDiff)} (${timeDiffPercent}%)</td>`;
                html += '</tr>';
                
                // 路径点数对比
                html += '<tr>';
                html += '<td><strong>路径点数</strong></td>';
                html += `<td class="amap-result">${amapRoute.coordinates.length}</td>`;
                html += `<td class="cpp-result">${cppRoute.coordinates.length}</td>`;
                html += `<td>${Math.abs(amapRoute.coordinates.length - cppRoute.coordinates.length)}</td>`;
                html += '</tr>';
                
                // 查询时间对比
                html += '<tr>';
                html += '<td><strong>查询时间</strong></td>';
                html += `<td class="amap-result">-</td>`;
                html += `<td class="cpp-result">${cppRoute.queryTime}ms</td>`;
                html += `<td>-</td>`;
                html += '</tr>';
                
            } else if (amapRoute) {
                html += '<tr><td><strong>距离</strong></td><td class="amap-result">' + formatDistance(amapRoute.totalDistance) + '</td><td>-</td><td>-</td></tr>';
                html += '<tr><td><strong>时间</strong></td><td class="amap-result">' + formatTime(amapRoute.totalTime) + '</td><td>-</td><td>-</td></tr>';
                html += '<tr><td><strong>路径点数</strong></td><td class="amap-result">' + amapRoute.coordinates.length + '</td><td>-</td><td>-</td></tr>';
            } else if (cppRoute) {
                html += '<tr><td><strong>距离</strong></td><td>-</td><td class="cpp-result">' + formatDistance(cppRoute.totalDistance) + '</td><td>-</td></tr>';
                html += '<tr><td><strong>时间</strong></td><td>-</td><td class="cpp-result">' + formatTime(cppRoute.totalTime) + '</td><td>-</td></tr>';
                html += '<tr><td><strong>路径点数</strong></td><td>-</td><td class="cpp-result">' + cppRoute.coordinates.length + '</td><td>-</td></tr>';
                html += '<tr><td><strong>查询时间</strong></td><td>-</td><td class="cpp-result">' + cppRoute.queryTime + 'ms</td><td>-</td></tr>';
            }
            
            html += '</tbody></table>';
            
            resultsDiv.innerHTML = html;
        }
        
        function clearResults() {
            document.getElementById('comparisonResults').innerHTML = '暂无测试结果';
            log('清除测试结果');
        }
        
        function formatDistance(distance) {
            if (distance < 1000) {
                return Math.round(distance) + ' 米';
            } else {
                return (distance / 1000).toFixed(1) + ' 公里';
            }
        }
        
        function formatTime(time) {
            const hours = Math.floor(time / 3600);
            const minutes = Math.floor((time % 3600) / 60);
            
            if (hours > 0) {
                return hours + ' 小时 ' + minutes + ' 分钟';
            } else {
                return minutes + ' 分钟';
            }
        }
    </script>
</body>
</html>
