# 批量双算路测试系统使用指南

## 🎯 功能概述

批量双算路测试系统是基于CSV地理数据文件的大规模路径规划对比测试工具，可以自动化测试高德地图API与C++算路引擎在不同场景下的性能表现。

### 主要特性

- 📊 **批量测试**: 支持一次性测试数十到数百个路径规划任务
- 🎯 **多种测试模式**: 随机配对、顺序配对、就近配对、跨城市测试
- ⚡ **并发执行**: 支持1-5个并发任务，提高测试效率
- 📈 **实时统计**: 实时显示测试进度、成功率、平均耗时等指标
- 📋 **详细结果**: 完整记录每个测试的详细数据和对比结果
- 📥 **数据导出**: 支持CSV、JSON格式导出和HTML报告生成

## 📁 文件结构

```
src/path/tools/js/
├── batch-route-test.html       # 批量测试主页面
├── csv-data-processor.js       # CSV数据处理器
├── coordinate-converter.js     # 坐标转换工具
├── route-planner.js           # 高德路径规划器
├── cpp-route-client.js        # C++算路客户端
└── [其他支持文件...]
```

## 🚀 快速开始

### 1. 启动服务

确保以下服务正在运行：

```bash
# C++算路服务器 (端口8080)
cd /home/<USER>/dlc/map_engine
./distribution/bin/simple_route_server

# Web服务器 (端口8001)
cd /home/<USER>/dlc/map_engine/src/path/tools/js
python3 server.py -p 8001
```

### 2. 访问测试页面

打开浏览器访问：http://localhost:8001/batch-route-test.html

### 3. 上传数据文件

- 点击文件上传区域或拖拽CSV文件
- 支持的文件格式：`route_test_input.txt` 或其他包含地理坐标的CSV文件
- 必需的列：`ProvName`, `CityName`, `DistName`, `CityLon`, `CityLat`, `DistLon`, `DistLat`

### 4. 配置测试参数

- **测试模式**: 选择路径配对策略
- **测试数量**: 设置要执行的测试数量(1-100)
- **并发数量**: 设置同时执行的任务数(1-5)
- **超时时间**: 设置单个测试的超时时间(10-120秒)
- **距离范围**: 设置起终点间的距离限制

### 5. 开始测试

点击"开始批量测试"按钮，系统将：
1. 根据配置生成测试配对
2. 并行执行高德和C++算路
3. 实时显示测试进度和结果
4. 自动计算对比数据和统计信息

## 🔧 测试模式详解

### 随机配对 (Random)
- 从数据集中随机选择起终点
- 适合测试算路引擎的整体性能
- 覆盖范围广，测试结果具有代表性

### 顺序配对 (Sequential)
- 按数据顺序依次配对相邻记录
- 适合测试特定区域的连续性
- 可以发现区域性的算路问题

### 就近配对 (Nearby)
- 在同一城市内进行配对测试
- 适合测试短距离路径规划
- 重点关注城市内部路网质量

### 跨城市测试 (Cross City)
- 确保起终点在不同城市
- 适合测试长距离路径规划
- 重点关注城际路网连接

## 📊 结果分析

### 实时统计指标

- **总测试数**: 计划执行的测试总数
- **已完成**: 已完成的测试数量
- **成功率**: 双算路都成功的测试比例
- **平均耗时**: 单个测试的平均执行时间
- **高德成功**: 高德算路成功的次数
- **C++成功**: C++算路成功的次数

### 详细结果表格

每个测试结果包含：
- 起终点信息和直线距离
- 高德算路状态、距离、时间
- C++算路状态、距离、时间、查询时间
- 距离差异和时间差异百分比
- 总执行耗时

### 结果状态说明

- ✅ **成功**: 算路正常完成
- ❌ **失败**: 算路过程中出现错误
- ⏰ **超时**: 算路超过设定时间限制
- 💥 **错误**: 系统级错误

## 📥 数据导出

### CSV导出
包含所有测试结果的详细数据，适合进一步分析：
- 起终点坐标和名称
- 算路结果和性能数据
- 对比分析结果

### JSON导出
完整的结构化数据，包含原始算路响应：
- 适合程序化处理
- 保留所有原始数据
- 支持自定义分析

### HTML报告
可视化的测试报告，包含：
- 测试概览和统计图表
- 性能对比分析
- 详细的数据表格

## 🔍 故障排除

### 常见问题

1. **文件上传失败**
   - 检查文件格式是否为CSV
   - 确认文件包含必需的列
   - 检查文件编码是否为UTF-8

2. **服务连接失败**
   - 确认C++算路服务器正在运行(端口8080)
   - 检查高德地图API密钥是否有效
   - 查看浏览器控制台错误信息

3. **测试执行缓慢**
   - 减少并发数量
   - 增加超时时间
   - 检查网络连接状况

4. **结果异常**
   - 检查坐标数据是否在有效范围内
   - 确认起终点距离是否合理
   - 查看详细日志信息

### 调试模式

在浏览器控制台中启用详细日志：
```javascript
localStorage.setItem('debug', 'true');
location.reload();
```

## 📈 性能优化建议

### 测试配置优化

1. **合理设置并发数**
   - 网络良好时可设置3-5个并发
   - 网络较差时建议使用1-2个并发

2. **适当的超时时间**
   - 短距离测试：15-30秒
   - 长距离测试：30-60秒
   - 跨省测试：60-120秒

3. **测试数量控制**
   - 初次测试建议10-20个
   - 正式测试可设置50-100个
   - 大规模测试需要充足时间

### 数据质量优化

1. **坐标数据验证**
   - 确保坐标在中国境内(70-140°E, 10-55°N)
   - 避免海上或无路网区域的坐标
   - 检查坐标精度是否合理

2. **距离范围设置**
   - 最小距离不少于1公里
   - 最大距离根据测试目的设定
   - 避免过短或过长的无意义测试

## 🎯 测试场景建议

### 基础功能测试
- 测试模式：随机配对
- 测试数量：20-30个
- 距离范围：5-50公里
- 目标：验证基本算路功能

### 性能压力测试
- 测试模式：随机配对
- 测试数量：100个
- 并发数量：5个
- 目标：测试系统负载能力

### 精度对比测试
- 测试模式：就近配对
- 测试数量：50个
- 距离范围：1-20公里
- 目标：对比算路精度差异

### 长距离测试
- 测试模式：跨城市
- 测试数量：30个
- 距离范围：50-500公里
- 目标：测试长距离算路能力

## 📝 测试报告解读

### 关键指标分析

1. **成功率指标**
   - 双算路成功率 > 90%：优秀
   - 双算路成功率 80-90%：良好
   - 双算路成功率 < 80%：需要优化

2. **性能指标**
   - C++查询时间 < 500ms：优秀
   - C++查询时间 500-1000ms：良好
   - C++查询时间 > 1000ms：需要优化

3. **精度指标**
   - 距离差异 < 5%：优秀
   - 距离差异 5-10%：良好
   - 距离差异 > 10%：需要分析

### 异常情况分析

1. **高失败率**
   - 检查网络连接
   - 验证服务器状态
   - 分析坐标数据质量

2. **大差异结果**
   - 分析具体路径差异
   - 检查算法参数设置
   - 考虑路网数据版本差异

## 🔄 持续改进

### 测试数据扩展
- 收集更多地区的测试数据
- 增加特殊场景的测试用例
- 定期更新测试数据集

### 功能增强
- 支持更多算路引擎对比
- 增加可视化分析功能
- 添加自动化测试调度

### 性能优化
- 优化并发处理逻辑
- 改进错误处理机制
- 增强测试结果缓存

---

## 📞 技术支持

如遇到问题或需要技术支持，请：
1. 查看浏览器控制台错误信息
2. 检查服务器运行状态
3. 参考本指南的故障排除部分
4. 联系技术团队获取帮助

批量双算路测试系统为路径规划算法的性能评估和优化提供了强大的工具支持！🚀
