<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0958d9;
        }
        .warning {
            background: #fffbe6;
            border: 1px solid #ffe58f;
            color: #d48806;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover {
            background: #40a9ff;
        }
        .log {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 路径规划修复验证</h1>
        
        <div id="status" class="status info">
            正在检查修复状态...
        </div>
        
        <div>
            <button onclick="runFullTest()">🧪 运行完整测试</button>
            <button onclick="testQuickRoute()">🚀 快速路径测试</button>
            <button onclick="clearLog()">🗑️ 清除日志</button>
        </div>
        
        <div id="log" class="log">等待测试开始...</div>
    </div>

    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=f7f401fd4ffb75720cba09ffb7b24a4c&plugin=AMap.Driving,AMap.Walking,AMap.Riding"></script>
    <script src="coordinate-converter.js"></script>
    <script src="route-planner.js"></script>
    
    <script>
        let routePlanner = null;
        let logElement = null;
        let statusElement = null;
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            logElement = document.getElementById('log');
            statusElement = document.getElementById('status');
            
            log('页面加载完成，开始初始化检查...');
            setTimeout(checkInitialStatus, 1000);
        });
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            console.log(logMessage);
            
            if (logElement) {
                logElement.textContent += logMessage + '\n';
                logElement.scrollTop = logElement.scrollHeight;
            }
        }
        
        function setStatus(message, type = 'info') {
            if (statusElement) {
                statusElement.textContent = message;
                statusElement.className = `status ${type}`;
            }
        }
        
        function clearLog() {
            if (logElement) {
                logElement.textContent = '日志已清除\n';
            }
        }
        
        async function checkInitialStatus() {
            try {
                log('检查高德地图API状态...');
                
                if (typeof AMap === 'undefined') {
                    throw new Error('高德地图API未加载');
                }
                
                log(`✓ 高德地图API已加载，版本: ${AMap.version || '未知'}`);
                
                // 检查插件是否可用
                const plugins = ['Driving', 'Walking', 'Riding'];
                for (const plugin of plugins) {
                    if (AMap[plugin]) {
                        log(`✓ ${plugin}插件可用`);
                    } else {
                        log(`✗ ${plugin}插件不可用`);
                    }
                }
                
                setStatus('✅ 初始检查通过，可以开始测试', 'success');
                
            } catch (error) {
                log(`❌ 初始检查失败: ${error.message}`);
                setStatus('❌ 初始检查失败', 'error');
            }
        }
        
        async function runFullTest() {
            try {
                setStatus('🧪 正在运行完整测试...', 'info');
                log('开始完整测试流程...');
                
                // 1. 创建路径规划器
                log('1. 创建RoutePlanner实例...');
                routePlanner = new RoutePlanner();
                log('✓ RoutePlanner实例创建成功');
                
                // 2. 等待初始化
                log('2. 等待路径规划器初始化...');
                const startTime = Date.now();
                await routePlanner.waitForInit();
                const initTime = Date.now() - startTime;
                log(`✓ 初始化完成，耗时: ${initTime}ms`);
                
                // 3. 检查服务状态
                log('3. 检查路径规划服务状态...');
                if (routePlanner.driving) {
                    log('✓ 驾车服务已初始化');
                } else {
                    throw new Error('驾车服务初始化失败');
                }
                
                if (routePlanner.walking) {
                    log('✓ 步行服务已初始化');
                } else {
                    throw new Error('步行服务初始化失败');
                }
                
                if (routePlanner.riding) {
                    log('✓ 骑行服务已初始化');
                } else {
                    throw new Error('骑行服务初始化失败');
                }
                
                // 4. 测试路径计算
                log('4. 测试路径计算功能...');
                await testRouteCalculation();
                
                setStatus('🎉 完整测试通过！修复成功', 'success');
                log('🎉 所有测试通过，修复验证成功！');
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`);
                setStatus('❌ 测试失败', 'error');
                console.error('完整测试失败:', error);
            }
        }
        
        async function testQuickRoute() {
            try {
                setStatus('🚀 正在进行快速路径测试...', 'info');
                log('开始快速路径测试...');
                
                if (!routePlanner) {
                    log('创建新的RoutePlanner实例...');
                    routePlanner = new RoutePlanner();
                    await routePlanner.waitForInit();
                }
                
                await testRouteCalculation();
                
                setStatus('✅ 快速测试通过', 'success');
                
            } catch (error) {
                log(`❌ 快速测试失败: ${error.message}`);
                setStatus('❌ 快速测试失败', 'error');
            }
        }
        
        async function testRouteCalculation() {
            // 测试坐标：北京天安门到故宫
            const startPoint = [116.4074, 39.9042]; // 天安门
            const endPoint = [116.3974, 39.9163];   // 故宫
            
            log(`测试路径: 天安门 → 故宫`);
            log(`起点: ${startPoint.join(', ')}`);
            log(`终点: ${endPoint.join(', ')}`);
            
            const startTime = Date.now();
            const route = await routePlanner.calculateRoute(startPoint, endPoint, []);
            const calcTime = Date.now() - startTime;
            
            log(`✓ 路径计算成功，耗时: ${calcTime}ms`);
            log(`  - 路径类型: ${route.type}`);
            log(`  - 总距离: ${routePlanner.formatDistance(route.totalDistance)}`);
            log(`  - 预计时间: ${routePlanner.formatTime(route.totalTime)}`);
            log(`  - 路径点数: ${route.coordinates.length}`);
            log(`  - 导航指令数: ${route.instructions.length}`);
            
            if (route.coordinates.length === 0) {
                throw new Error('路径坐标为空');
            }
            
            if (route.totalDistance <= 0) {
                throw new Error('路径距离无效');
            }
        }
    </script>
</body>
</html>
