#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML结构验证脚本
检查index.html的侧边栏结构是否正确
"""

import re
from pathlib import Path

def validate_html_structure(file_path):
    """验证HTML结构"""
    print(f"🔍 验证HTML文件: {file_path}")
    
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查基本结构
        print("\n📋 检查基本HTML结构:")
        
        # 检查容器结构
        if '<div id="container">' in content:
            print("✅ 找到主容器 #container")
        else:
            print("❌ 未找到主容器 #container")
            return False
        
        if '<div id="map"></div>' in content:
            print("✅ 找到地图容器 #map")
        else:
            print("❌ 未找到地图容器 #map")
            return False
        
        if '<div id="sidebar">' in content:
            print("✅ 找到侧边栏容器 #sidebar")
        else:
            print("❌ 未找到侧边栏容器 #sidebar")
            return False
        
        # 检查侧边栏内容
        print("\n📋 检查侧边栏控制组:")
        
        # 提取侧边栏内容
        sidebar_match = re.search(r'<div id="sidebar">(.*?)</div>\s*</div>', content, re.DOTALL)
        if not sidebar_match:
            print("❌ 无法提取侧边栏内容")
            return False
        
        sidebar_content = sidebar_match.group(1)
        
        # 检查控制组
        expected_groups = [
            ('🔍 POI搜索', 'POI搜索功能'),
            ('📍 地理编码', '地理编码功能'),
            ('📐 手动坐标输入', '手动坐标输入功能'),
            ('📚 坐标管理', '坐标管理功能'),
            ('地图图层', '地图图层控制'),
            ('🔄 双算路对比', '双算路对比功能'),
            ('路径点信息', '路径点信息显示'),
            ('路径信息', '路径信息显示')
        ]
        
        found_groups = []
        missing_groups = []
        
        for group_name, description in expected_groups:
            if group_name in sidebar_content:
                found_groups.append(group_name)
                print(f"✅ {group_name} - {description}")
            else:
                missing_groups.append(group_name)
                print(f"❌ {group_name} - {description}")
        
        # 检查重要元素
        print("\n📋 检查重要元素:")
        
        important_elements = [
            ('id="coordinates"', '坐标显示区域'),
            ('id="routeInfo"', '路径信息区域'),
            ('id="status"', '状态显示区域'),
            ('id="poiSearchInput"', 'POI搜索输入框'),
            ('id="geocodeInput"', '地理编码输入框'),
            ('id="startLng"', '起点经度输入框'),
            ('id="startLat"', '起点纬度输入框'),
            ('id="endLng"', '终点经度输入框'),
            ('id="endLat"', '终点纬度输入框'),
            ('id="coordinateHistory"', '坐标历史记录'),
            ('id="coordinateFavorites"', '坐标收藏夹')
        ]
        
        for element_id, description in important_elements:
            if element_id in sidebar_content:
                print(f"✅ {element_id} - {description}")
            else:
                print(f"❌ {element_id} - {description}")
        
        # 检查模态框
        print("\n📋 检查模态框:")
        
        modals = [
            ('id="contextMenu"', '右键菜单'),
            ('id="citySelector"', '城市选择器'),
            ('id="addFavoriteDialog"', '添加收藏对话框')
        ]
        
        for modal_id, description in modals:
            if modal_id in content:
                print(f"✅ {modal_id} - {description}")
            else:
                print(f"❌ {modal_id} - {description}")
        
        # 检查JavaScript文件引用
        print("\n📋 检查JavaScript文件引用:")
        
        js_files = [
            'map-config.js',
            'coordinate-converter.js',
            'route-planner.js',
            'cpp-route-client.js',
            'poi-search.js',
            'geocoding.js',
            'coordinate-manager.js',
            'main.js'
        ]
        
        for js_file in js_files:
            if f'src="{js_file}"' in content:
                print(f"✅ {js_file}")
            else:
                print(f"❌ {js_file}")
        
        # 统计结果
        print("\n📊 验证结果统计:")
        print(f"✅ 找到的控制组: {len(found_groups)}/{len(expected_groups)}")
        print(f"✅ 侧边栏结构: {'正确' if len(missing_groups) == 0 else '有问题'}")
        
        if missing_groups:
            print(f"❌ 缺少的控制组: {missing_groups}")
            return False
        
        # 检查标签嵌套
        print("\n📋 检查HTML标签嵌套:")
        
        # 简单的标签平衡检查
        div_open = content.count('<div')
        div_close = content.count('</div>')
        
        print(f"📊 <div> 标签: 开始 {div_open}, 结束 {div_close}")
        
        if div_open == div_close:
            print("✅ <div> 标签平衡")
        else:
            print("❌ <div> 标签不平衡")
            return False
        
        print("\n🎉 HTML结构验证通过!")
        return True
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("🧪 HTML结构验证工具")
    print("=" * 50)
    
    file_path = "index.html"
    success = validate_html_structure(file_path)
    
    if success:
        print("\n✅ 验证完成，HTML结构正确")
        print("\n💡 建议:")
        print("1. 在浏览器中测试所有功能")
        print("2. 检查侧边栏滚动是否正常")
        print("3. 验证响应式布局")
        print("4. 测试模态框显示")
    else:
        print("\n❌ 验证失败，请检查HTML结构")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
