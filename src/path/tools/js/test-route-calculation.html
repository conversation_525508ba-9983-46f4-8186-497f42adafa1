<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>算路功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .test-section {
            padding: 20px;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .test-section:last-child {
            border-bottom: none;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            border-left: 4px solid #1890ff;
            padding-left: 10px;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
        }
        
        .input-group label {
            min-width: 80px;
            font-weight: 500;
            color: #333;
        }
        
        .input-group input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }
        
        .btn-success {
            background: #52c41a;
            border-color: #52c41a;
            color: white;
        }
        
        .btn-warning {
            background: #faad14;
            border-color: #faad14;
            color: white;
        }
        
        .result {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .result.error {
            background: #fff2f0;
            border-color: #ffccc7;
        }
        
        .result.info {
            background: #e6f7ff;
            border-color: #91d5ff;
        }
        
        .test-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        
        .status-display {
            background: #fafafa;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
            min-height: 50px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛣️ 算路功能测试</h1>
            <p>测试手动坐标输入和收藏夹设置起终点后的算路功能</p>
        </div>

        <div class="test-section">
            <h3>📐 手动坐标输入算路测试</h3>
            <div class="input-group">
                <label>起点经度:</label>
                <input type="number" id="testStartLng" placeholder="经度" value="116.4074" step="0.000001">
                <label>起点纬度:</label>
                <input type="number" id="testStartLat" placeholder="纬度" value="39.9042" step="0.000001">
            </div>
            <div class="input-group">
                <label>终点经度:</label>
                <input type="number" id="testEndLng" placeholder="经度" value="121.4737" step="0.000001">
                <label>终点纬度:</label>
                <input type="number" id="testEndLat" placeholder="纬度" value="31.2304" step="0.000001">
            </div>
            <div class="test-actions">
                <button class="btn btn-success" onclick="testSetStartPoint()">设置起点</button>
                <button class="btn btn-warning" onclick="testSetEndPoint()">设置终点</button>
                <button class="btn btn-primary" onclick="testCalculateRoute()">开始算路</button>
                <button class="btn" onclick="testClearPoints()">清除所有点</button>
            </div>
            <div id="manualTestResult" class="result">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>⭐ 收藏夹算路测试</h3>
            <p style="margin: 0 0 15px 0; color: #666; font-size: 14px;">
                模拟从收藏夹设置起终点并自动算路的功能
            </p>
            <div class="test-actions">
                <button class="btn btn-success" onclick="testFavoriteAsStart()">收藏点设为起点</button>
                <button class="btn btn-warning" onclick="testFavoriteAsEnd()">收藏点设为终点</button>
                <button class="btn btn-primary" onclick="testAutoRoute()">检查自动算路</button>
                <button class="btn" onclick="testFillSampleCoords()">填充示例坐标</button>
            </div>
            <div id="favoriteTestResult" class="result">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>🔍 算路状态检查</h3>
            <div class="test-actions">
                <button class="btn" onclick="checkRouteStatus()">检查起终点状态</button>
                <button class="btn" onclick="checkRouteFunctions()">检查算路函数</button>
                <button class="btn" onclick="simulateCalculateRoute()">模拟算路调用</button>
            </div>
            <div id="statusCheckResult" class="result">点击按钮检查状态...</div>
        </div>

        <div class="test-section">
            <h3>📊 测试日志</h3>
            <div class="test-actions">
                <button class="btn" onclick="clearTestLog()">清空日志</button>
                <button class="btn" onclick="exportTestLog()">导出日志</button>
            </div>
            <div id="testLog" class="status-display">测试日志将在这里显示...</div>
        </div>
    </div>

    <script>
        // 测试日志
        let testLog = [];
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('zh-CN');
            const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            testLog.push(logEntry);
            
            const logDiv = document.getElementById('testLog');
            logDiv.innerHTML = testLog.slice(-20).join('<br>'); // 只显示最近20条
            logDiv.scrollTop = logDiv.scrollHeight;
            
            console.log(logEntry);
        }

        // 模拟全局变量
        let mockStartPoint = null;
        let mockEndPoint = null;
        let mockWaypoints = [];

        // 模拟函数
        function mockSetStartPointByCoords(coords, name) {
            mockStartPoint = {
                coordinates: coords,
                name: name,
                marker: { id: 'start_marker' }
            };
            addLog(`设置起点: ${name} (${coords[1].toFixed(6)}, ${coords[0].toFixed(6)})`, 'success');
            return true;
        }

        function mockSetEndPointByCoords(coords, name) {
            mockEndPoint = {
                coordinates: coords,
                name: name,
                marker: { id: 'end_marker' }
            };
            addLog(`设置终点: ${name} (${coords[1].toFixed(6)}, ${coords[0].toFixed(6)})`, 'success');
            return true;
        }

        function mockCalculateRoute() {
            if (!mockStartPoint || !mockEndPoint) {
                addLog('算路失败: 请先设置起点和终点', 'error');
                return false;
            }
            
            addLog('开始算路...', 'info');
            addLog(`起点: ${mockStartPoint.name}`, 'info');
            addLog(`终点: ${mockEndPoint.name}`, 'info');
            
            // 模拟算路延迟
            setTimeout(() => {
                addLog('算路完成 (模拟)', 'success');
            }, 1000);
            
            return true;
        }

        // 测试函数
        function testSetStartPoint() {
            const lng = parseFloat(document.getElementById('testStartLng').value);
            const lat = parseFloat(document.getElementById('testStartLat').value);
            
            if (isNaN(lng) || isNaN(lat)) {
                updateTestResult('manualTestResult', '请输入有效的起点坐标', 'error');
                return;
            }
            
            const coords = [lng, lat];
            const name = `测试起点: ${lat.toFixed(6)}, ${lng.toFixed(6)}`;
            
            if (mockSetStartPointByCoords(coords, name)) {
                updateTestResult('manualTestResult', `✅ 起点设置成功: ${name}`, 'success');
            }
        }

        function testSetEndPoint() {
            const lng = parseFloat(document.getElementById('testEndLng').value);
            const lat = parseFloat(document.getElementById('testEndLat').value);
            
            if (isNaN(lng) || isNaN(lat)) {
                updateTestResult('manualTestResult', '请输入有效的终点坐标', 'error');
                return;
            }
            
            const coords = [lng, lat];
            const name = `测试终点: ${lat.toFixed(6)}, ${lng.toFixed(6)}`;
            
            if (mockSetEndPointByCoords(coords, name)) {
                updateTestResult('manualTestResult', `✅ 终点设置成功: ${name}`, 'success');
            }
        }

        function testCalculateRoute() {
            if (mockCalculateRoute()) {
                updateTestResult('manualTestResult', '✅ 算路请求已发送，请查看测试日志', 'success');
            } else {
                updateTestResult('manualTestResult', '❌ 算路失败，请先设置起终点', 'error');
            }
        }

        function testClearPoints() {
            mockStartPoint = null;
            mockEndPoint = null;
            mockWaypoints = [];
            addLog('已清除所有点', 'info');
            updateTestResult('manualTestResult', '✅ 所有点已清除', 'info');
        }

        function testFavoriteAsStart() {
            const coords = [116.4074, 39.9042];
            const name = '收藏点-北京天安门';
            
            if (mockSetStartPointByCoords(coords, name)) {
                updateTestResult('favoriteTestResult', `✅ 从收藏夹设置起点: ${name}`, 'success');
                
                // 检查是否可以自动算路
                setTimeout(() => {
                    if (mockStartPoint && mockEndPoint) {
                        addLog('检测到起终点都已设置，尝试自动算路...', 'info');
                        mockCalculateRoute();
                    }
                }, 200);
            }
        }

        function testFavoriteAsEnd() {
            const coords = [121.4737, 31.2304];
            const name = '收藏点-上海人民广场';
            
            if (mockSetEndPointByCoords(coords, name)) {
                updateTestResult('favoriteTestResult', `✅ 从收藏夹设置终点: ${name}`, 'success');
                
                // 检查是否可以自动算路
                setTimeout(() => {
                    if (mockStartPoint && mockEndPoint) {
                        addLog('检测到起终点都已设置，尝试自动算路...', 'info');
                        mockCalculateRoute();
                    }
                }, 200);
            }
        }

        function testAutoRoute() {
            if (mockStartPoint && mockEndPoint) {
                updateTestResult('favoriteTestResult', '✅ 起终点已设置，可以自动算路', 'success');
                mockCalculateRoute();
            } else {
                updateTestResult('favoriteTestResult', '❌ 起终点未完全设置，无法自动算路', 'error');
            }
        }

        function testFillSampleCoords() {
            document.getElementById('testStartLng').value = '116.4074';
            document.getElementById('testStartLat').value = '39.9042';
            document.getElementById('testEndLng').value = '121.4737';
            document.getElementById('testEndLat').value = '31.2304';
            updateTestResult('favoriteTestResult', '✅ 已填充示例坐标 (北京→上海)', 'info');
        }

        function checkRouteStatus() {
            let status = '<strong>起终点状态检查:</strong><br>';
            status += `起点: ${mockStartPoint ? '✅ 已设置' : '❌ 未设置'}<br>`;
            status += `终点: ${mockEndPoint ? '✅ 已设置' : '❌ 未设置'}<br>`;
            status += `途经点: ${mockWaypoints.length} 个<br>`;
            
            if (mockStartPoint) {
                status += `起点坐标: ${mockStartPoint.coordinates[1].toFixed(6)}, ${mockStartPoint.coordinates[0].toFixed(6)}<br>`;
            }
            if (mockEndPoint) {
                status += `终点坐标: ${mockEndPoint.coordinates[1].toFixed(6)}, ${mockEndPoint.coordinates[0].toFixed(6)}<br>`;
            }
            
            updateTestResult('statusCheckResult', status, 'info');
        }

        function checkRouteFunctions() {
            let status = '<strong>算路函数检查:</strong><br>';
            status += `setStartPointByCoords: ${typeof mockSetStartPointByCoords === 'function' ? '✅ 可用' : '❌ 不可用'}<br>`;
            status += `setEndPointByCoords: ${typeof mockSetEndPointByCoords === 'function' ? '✅ 可用' : '❌ 不可用'}<br>`;
            status += `calculateRoute: ${typeof mockCalculateRoute === 'function' ? '✅ 可用' : '❌ 不可用'}<br>`;
            
            updateTestResult('statusCheckResult', status, 'info');
        }

        function simulateCalculateRoute() {
            addLog('模拟算路调用...', 'info');
            
            if (!mockStartPoint || !mockEndPoint) {
                updateTestResult('statusCheckResult', '❌ 模拟算路失败: 起终点未设置', 'error');
                return;
            }
            
            updateTestResult('statusCheckResult', '✅ 模拟算路成功，请查看测试日志', 'success');
            mockCalculateRoute();
        }

        function updateTestResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = `result ${type}`;
        }

        function clearTestLog() {
            testLog = [];
            document.getElementById('testLog').innerHTML = '测试日志已清空';
        }

        function exportTestLog() {
            if (testLog.length === 0) {
                alert('没有测试日志可导出');
                return;
            }
            
            const logContent = testLog.join('\n');
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `route_test_log_${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('算路功能测试页面已加载', 'info');
            addLog('这是一个模拟测试环境，用于验证算路逻辑', 'info');
        });
    </script>
</body>
</html>
