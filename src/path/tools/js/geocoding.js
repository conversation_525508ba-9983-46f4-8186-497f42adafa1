/**
 * 地理编码功能
 * 基于高德地图JS API 2.0的Geocoder插件
 */

class Geocoding {
    constructor() {
        this.geocoder = null;
        this.isInitialized = false;
        this.geocodeMarker = null;
        
        this.init();
    }

    /**
     * 初始化地理编码服务
     */
    async init() {
        try {
            // 等待高德地图API加载完成
            if (typeof AMap === 'undefined') {
                await this.waitForAMap();
            }

            // 初始化Geocoder
            this.geocoder = new AMap.Geocoder({
                city: '全国',
                radius: 1000
            });

            this.isInitialized = true;
            console.log('地理编码服务初始化完成');
            
        } catch (error) {
            console.error('地理编码服务初始化失败:', error);
        }
    }

    /**
     * 等待高德地图API加载
     */
    waitForAMap() {
        return new Promise((resolve) => {
            const checkAMap = () => {
                if (typeof AMap !== 'undefined') {
                    resolve();
                } else {
                    setTimeout(checkAMap, 100);
                }
            };
            checkAMap();
        });
    }

    /**
     * 地址转坐标（地理编码）
     */
    geocode(address) {
        if (!this.isInitialized) {
            console.error('地理编码服务未初始化');
            return Promise.reject('地理编码服务未初始化');
        }

        return new Promise((resolve, reject) => {
            this.geocoder.getLocation(address, (status, result) => {
                if (status === 'complete' && result.geocodes && result.geocodes.length > 0) {
                    const geocode = result.geocodes[0];
                    resolve(geocode);
                } else {
                    reject('地理编码失败');
                }
            });
        });
    }

    /**
     * 坐标转地址（逆地理编码）
     */
    reverseGeocode(lng, lat) {
        if (!this.isInitialized) {
            console.error('地理编码服务未初始化');
            return Promise.reject('地理编码服务未初始化');
        }

        return new Promise((resolve, reject) => {
            const lnglat = new AMap.LngLat(lng, lat);
            
            this.geocoder.getAddress(lnglat, (status, result) => {
                if (status === 'complete' && result.regeocode) {
                    resolve(result.regeocode);
                } else {
                    reject('逆地理编码失败');
                }
            });
        });
    }

    /**
     * 显示地理编码结果
     */
    displayGeocodeResult(geocode, isReverse = false) {
        const resultDiv = document.getElementById('geocodeResult');
        if (!resultDiv) return;

        if (isReverse) {
            // 逆地理编码结果
            const address = geocode.formattedAddress || '';
            const province = geocode.addressComponent?.province || '';
            const city = geocode.addressComponent?.city || '';
            const district = geocode.addressComponent?.district || '';
            const street = geocode.addressComponent?.street || '';
            const streetNumber = geocode.addressComponent?.streetNumber || '';

            resultDiv.innerHTML = `
                <strong>逆地理编码结果:</strong><br>
                <strong>详细地址:</strong> ${address}<br>
                <strong>省份:</strong> ${province}<br>
                <strong>城市:</strong> ${city}<br>
                <strong>区县:</strong> ${district}<br>
                <strong>街道:</strong> ${street}<br>
                <strong>门牌号:</strong> ${streetNumber}<br>
                <div style="margin-top: 10px;">
                    <button class="btn btn-small btn-success" onclick="geocoding.setGeocodeAsStartPoint(true)">设为起点</button>
                    <button class="btn btn-small btn-warning" onclick="geocoding.setGeocodeAsEndPoint(true)">设为终点</button>
                    <button class="btn btn-small" onclick="geocoding.addGeocodeToFavorites(true)" style="background: #faad14; color: white;">⭐ 收藏</button>
                </div>
            `;
        } else {
            // 地理编码结果
            const gcj02Coords = [geocode.location.lng, geocode.location.lat];

            resultDiv.innerHTML = `
                <strong>地理编码结果:</strong><br>
                <strong>地址:</strong> ${geocode.formattedAddress}<br>
                <strong>GCJ02坐标:</strong> ${geocode.location.lat.toFixed(6)}, ${geocode.location.lng.toFixed(6)}<br>
                <strong>置信度:</strong> ${geocode.level || '未知'}<br>
                <div style="margin-top: 10px;">
                    <button class="btn btn-small btn-success" onclick="geocoding.setGeocodeAsStartPoint(false)">设为起点</button>
                    <button class="btn btn-small btn-warning" onclick="geocoding.setGeocodeAsEndPoint(false)">设为终点</button>
                    <button class="btn btn-small" onclick="geocoding.showGeocodeOnMap()">查看位置</button>
                    <button class="btn btn-small" onclick="geocoding.addGeocodeToFavorites(false)" style="background: #faad14; color: white;">⭐ 收藏</button>
                </div>
            `;

            // 在地图上显示位置
            this.showGeocodeMarker(gcj02Coords, geocode.formattedAddress);
        }
    }

    /**
     * 在地图上显示地理编码标记
     */
    showGeocodeMarker(gcj02Coords, address) {
        // 清除之前的标记
        if (this.geocodeMarker) {
            map.removeLayer(this.geocodeMarker);
        }

        // 高德地图使用GCJ02坐标系，需要转换为WGS84用于Leaflet地图显示
        const wgs84Coords = CoordinateConverter.gcj02Togcj02(gcj02Coords[0], gcj02Coords[1]);

        // 创建新标记
        this.geocodeMarker = L.circleMarker([wgs84Coords[1], wgs84Coords[0]], {
            color: '#52c41a',
            fillColor: '#73d13d',
            fillOpacity: 0.8,
            radius: 8
        }).addTo(map);

        // 添加弹窗
        this.geocodeMarker.bindPopup(`
            <div style="min-width: 200px;">
                <h4 style="margin: 0 0 8px 0;">地理编码结果</h4>
                <p style="margin: 0 0 8px 0; font-size: 12px; color: #666;">
                    ${address}
                </p>
                <p style="margin: 0 0 8px 0; font-size: 12px; color: #666;">
                    GCJ02坐标: ${gcj02Coords[1].toFixed(6)}, ${gcj02Coords[0].toFixed(6)}
                </p>
                <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                    <button class="btn btn-small btn-success" onclick="geocoding.setGeocodeAsStartPoint(false)">设为起点</button>
                    <button class="btn btn-small btn-warning" onclick="geocoding.setGeocodeAsEndPoint(false)">设为终点</button>
                    <button class="btn btn-small" onclick="geocoding.addGeocodeToFavorites(false)" style="background: #faad14; color: white;">⭐ 收藏</button>
                </div>
            </div>
        `).openPopup();

        // 移动地图到该位置
        map.setView([wgs84Coords[1], wgs84Coords[0]], 16);
    }

    /**
     * 设置地理编码结果为起点
     */
    setGeocodeAsStartPoint(isReverse) {
        let coords, name;

        if (isReverse && this.lastReverseGeocodeCoords) {
            // 逆地理编码的坐标已经是WGS84格式（来自Leaflet地图）
            coords = this.lastReverseGeocodeCoords;
            name = this.lastReverseGeocodeResult?.formattedAddress || '逆地理编码位置';
        } else if (!isReverse && this.lastGeocodeResult) {
            // 地理编码结果是GCJ02坐标，需要转换为WGS84用于填充输入框
            coords = CoordinateConverter.gcj02Togcj02(
                this.lastGeocodeResult.location.lng,
                this.lastGeocodeResult.location.lat
            );
            name = this.lastGeocodeResult.formattedAddress;
        } else {
            updateStatus('没有可用的地理编码结果', 'error');
            return;
        }

        // 填充到起点输入框
        document.getElementById('startLng').value = coords[0].toFixed(6);
        document.getElementById('startLat').value = coords[1].toFixed(6);

        updateStatus(`已填充起点坐标: ${name}，请点击"开始算路"按钮`, 'success');

        // 添加到历史记录
        if (coordinateManager) {
            coordinateManager.addToHistory(coords[0], coords[1], name, '地理编码设置的起点');
        }
    }

    /**
     * 设置地理编码结果为终点
     */
    setGeocodeAsEndPoint(isReverse) {
        let coords, name;

        if (isReverse && this.lastReverseGeocodeCoords) {
            // 逆地理编码的坐标已经是WGS84格式（来自Leaflet地图）
            coords = this.lastReverseGeocodeCoords;
            name = this.lastReverseGeocodeResult?.formattedAddress || '逆地理编码位置';
        } else if (!isReverse && this.lastGeocodeResult) {
            // 地理编码结果是GCJ02坐标，需要转换为WGS84用于填充输入框
            coords = CoordinateConverter.gcj02Togcj02(
                this.lastGeocodeResult.location.lng,
                this.lastGeocodeResult.location.lat
            );
            name = this.lastGeocodeResult.formattedAddress;
        } else {
            updateStatus('没有可用的地理编码结果', 'error');
            return;
        }

        // 填充到终点输入框
        document.getElementById('endLng').value = coords[0].toFixed(6);
        document.getElementById('endLat').value = coords[1].toFixed(6);

        updateStatus(`已填充终点坐标: ${name}，请点击"开始算路"按钮`, 'success');

        // 添加到历史记录
        if (coordinateManager) {
            coordinateManager.addToHistory(coords[0], coords[1], name, '地理编码设置的终点');
        }
    }

    /**
     * 在地图上显示地理编码位置
     */
    showGeocodeOnMap() {
        if (this.geocodeMarker) {
            this.geocodeMarker.openPopup();
        }
    }

    /**
     * 清除地理编码标记
     */
    clearGeocodeMarker() {
        if (this.geocodeMarker) {
            map.removeLayer(this.geocodeMarker);
            this.geocodeMarker = null;
        }
    }

    /**
     * 添加地理编码结果到收藏夹
     */
    addGeocodeToFavorites(isReverse) {
        let coords, name, note;

        if (isReverse && this.lastReverseGeocodeResult && this.lastReverseGeocodeCoords) {
            // 逆地理编码的坐标已经是WGS84格式
            coords = this.lastReverseGeocodeCoords;
            name = this.lastReverseGeocodeResult.formattedAddress || '逆地理编码位置';
            note = `逆地理编码结果 - ${name}`;
        } else if (!isReverse && this.lastGeocodeResult) {
            // 地理编码结果是GCJ02坐标，需要转换为WGS84用于收藏
            const wgs84Coords = CoordinateConverter.gcj02Togcj02(
                this.lastGeocodeResult.location.lng,
                this.lastGeocodeResult.location.lat
            );
            coords = wgs84Coords;
            name = this.lastGeocodeResult.formattedAddress;
            note = `地理编码结果 - ${name}`;
        } else {
            updateStatus('没有可用的地理编码结果', 'error');
            return;
        }

        // 添加到收藏夹
        if (coordinateManager) {
            if (coordinateManager.addToFavorites(coords[0], coords[1], name, note)) {
                updateStatus(`已收藏地址: ${name}`, 'success');
            }
        } else {
            updateStatus('坐标管理器未初始化', 'error');
        }
    }
}

// 全局地理编码实例
let geocoding = null;

// 初始化地理编码
document.addEventListener('DOMContentLoaded', function() {
    geocoding = new Geocoding();
});

// 导出的全局函数
async function geocodeAddress() {
    const address = document.getElementById('geocodeInput').value.trim();
    if (!address) {
        updateStatus('请输入地址', 'error');
        return;
    }

    if (!geocoding) {
        updateStatus('地理编码服务未初始化', 'error');
        return;
    }

    try {
        updateStatus('正在进行地理编码...', 'info');
        const result = await geocoding.geocode(address);
        geocoding.lastGeocodeResult = result;
        geocoding.displayGeocodeResult(result, false);
        updateStatus('地理编码完成', 'success');
    } catch (error) {
        console.error('地理编码失败:', error);
        updateStatus('地理编码失败: ' + error, 'error');
    }
}

async function reverseGeocode() {
    if (!map) {
        updateStatus('地图未初始化', 'error');
        return;
    }

    if (!geocoding) {
        updateStatus('地理编码服务未初始化', 'error');
        return;
    }

    try {
        const center = map.getCenter();
        // Leaflet地图中心是WGS84坐标，需要转换为GCJ02进行逆地理编码
        const gcj02Coords = CoordinateConverter.gcj02Togcj02(center.lng, center.lat);

        updateStatus('正在进行逆地理编码...', 'info');
        const result = await geocoding.reverseGeocode(gcj02Coords[0], gcj02Coords[1]);

        geocoding.lastReverseGeocodeResult = result;
        geocoding.lastReverseGeocodeCoords = [center.lng, center.lat]; // 保存WGS84坐标
        geocoding.displayGeocodeResult(result, true);
        updateStatus('逆地理编码完成', 'success');
    } catch (error) {
        console.error('逆地理编码失败:', error);
        updateStatus('逆地理编码失败: ' + error, 'error');
    }
}

async function reverseGeocodePoint() {
    if (!rightClickPosition || !geocoding) {
        hideContextMenu();
        return;
    }

    try {
        // rightClickPosition是WGS84坐标，需要转换为GCJ02进行逆地理编码
        const gcj02Coords = CoordinateConverter.gcj02Togcj02(rightClickPosition[0], rightClickPosition[1]);

        updateStatus('正在获取地址信息...', 'info');
        const result = await geocoding.reverseGeocode(gcj02Coords[0], gcj02Coords[1]);

        geocoding.lastReverseGeocodeResult = result;
        geocoding.lastReverseGeocodeCoords = rightClickPosition; // 保存WGS84坐标
        geocoding.displayGeocodeResult(result, true);
        updateStatus('地址获取完成', 'success');
    } catch (error) {
        console.error('逆地理编码失败:', error);
        updateStatus('地址获取失败: ' + error, 'error');
    }

    hideContextMenu();
}
