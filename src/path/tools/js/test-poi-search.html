<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POI搜索和地理编码测试</title>
    
    <!-- 高德地图 JS SDK -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=f7f401fd4ffb75720cba09ffb7b24a4c&plugin=AMap.PlaceSearch,AMap.AutoComplete,AMap.Geocoder"></script>
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .input-group {
            margin: 10px 0;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .input-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px 5px 5px 0;
        }
        
        .btn:hover {
            background: #40a9ff;
        }
        
        .result {
            background: white;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            border: 1px solid #e8e8e8;
            min-height: 50px;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        
        .status.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
        
        .status.info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0958d9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 POI搜索和地理编码功能测试</h1>
        
        <div class="test-section">
            <h3>📍 POI搜索测试</h3>
            <div class="input-group">
                <label for="poiKeyword">搜索关键词:</label>
                <input type="text" id="poiKeyword" placeholder="例如: 星巴克、加油站、银行等">
            </div>
            <button class="btn" onclick="testPOISearch()">搜索POI</button>
            <button class="btn" onclick="testAutoComplete()">测试自动补全</button>
            <div id="poiResult" class="result">等待搜索...</div>
        </div>
        
        <div class="test-section">
            <h3>🗺️ 地理编码测试</h3>
            <div class="input-group">
                <label for="addressInput">地址:</label>
                <input type="text" id="addressInput" placeholder="例如: 北京市朝阳区望京SOHO">
            </div>
            <button class="btn" onclick="testGeocode()">地址转坐标</button>
            <div id="geocodeResult" class="result">等待地理编码...</div>
        </div>
        
        <div class="test-section">
            <h3>📌 逆地理编码测试</h3>
            <div class="input-group">
                <label for="lngInput">经度:</label>
                <input type="number" id="lngInput" placeholder="例如: 116.4074" step="0.000001">
            </div>
            <div class="input-group">
                <label for="latInput">纬度:</label>
                <input type="number" id="latInput" placeholder="例如: 39.9042" step="0.000001">
            </div>
            <button class="btn" onclick="testReverseGeocode()">坐标转地址</button>
            <button class="btn" onclick="useBeijingCoords()">使用北京坐标</button>
            <div id="reverseGeocodeResult" class="result">等待逆地理编码...</div>
        </div>
        
        <div id="status" class="status info">
            页面已加载，等待高德地图API初始化...
        </div>
    </div>

    <script>
        let placeSearch = null;
        let autoComplete = null;
        let geocoder = null;
        let isInitialized = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                updateStatus('正在初始化高德地图API...', 'info');
                
                // 等待高德地图API加载
                await waitForAMap();
                
                // 初始化服务
                placeSearch = new AMap.PlaceSearch({
                    pageSize: 10,
                    pageIndex: 1,
                    city: '全国',
                    citylimit: false
                });

                autoComplete = new AMap.AutoComplete({
                    city: '全国',
                    citylimit: false
                });

                geocoder = new AMap.Geocoder({
                    city: '全国',
                    radius: 1000
                });

                isInitialized = true;
                updateStatus('高德地图API初始化完成，可以开始测试', 'success');
                
            } catch (error) {
                console.error('初始化失败:', error);
                updateStatus('初始化失败: ' + error.message, 'error');
            }
        });

        function waitForAMap() {
            return new Promise((resolve) => {
                const checkAMap = () => {
                    if (typeof AMap !== 'undefined') {
                        resolve();
                    } else {
                        setTimeout(checkAMap, 100);
                    }
                };
                checkAMap();
            });
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function testPOISearch() {
            if (!isInitialized) {
                updateStatus('服务未初始化', 'error');
                return;
            }

            const keyword = document.getElementById('poiKeyword').value.trim();
            if (!keyword) {
                updateStatus('请输入搜索关键词', 'error');
                return;
            }

            updateStatus('正在搜索POI...', 'info');
            
            placeSearch.search(keyword, (status, result) => {
                const resultDiv = document.getElementById('poiResult');
                
                if (status === 'complete' && result.poiList && result.poiList.pois.length > 0) {
                    const pois = result.poiList.pois;
                    let html = `<h4>找到 ${pois.length} 个结果:</h4>`;
                    
                    pois.slice(0, 5).forEach((poi, index) => {
                        html += `
                            <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                                <strong>${poi.name}</strong><br>
                                <small style="color: #666;">
                                    ${poi.pname || ''}${poi.cityname || ''}${poi.adname || ''}${poi.address || ''}<br>
                                    坐标: ${poi.location.lat}, ${poi.location.lng}
                                </small>
                            </div>
                        `;
                    });
                    
                    resultDiv.innerHTML = html;
                    updateStatus('POI搜索完成', 'success');
                } else {
                    resultDiv.innerHTML = '<p>未找到相关POI</p>';
                    updateStatus('未找到相关POI', 'info');
                }
            });
        }

        function testAutoComplete() {
            if (!isInitialized) {
                updateStatus('服务未初始化', 'error');
                return;
            }

            const keyword = document.getElementById('poiKeyword').value.trim();
            if (!keyword) {
                updateStatus('请输入搜索关键词', 'error');
                return;
            }

            updateStatus('正在获取自动补全建议...', 'info');
            
            autoComplete.search(keyword, (status, result) => {
                const resultDiv = document.getElementById('poiResult');
                
                if (status === 'complete' && result.tips && result.tips.length > 0) {
                    const tips = result.tips;
                    let html = `<h4>自动补全建议 (${tips.length} 个):</h4>`;
                    
                    tips.slice(0, 8).forEach((tip, index) => {
                        html += `
                            <div style="border-bottom: 1px solid #eee; padding: 8px 0;">
                                <strong>${tip.name}</strong><br>
                                <small style="color: #666;">
                                    ${tip.district || ''}${tip.address || ''}
                                </small>
                            </div>
                        `;
                    });
                    
                    resultDiv.innerHTML = html;
                    updateStatus('自动补全获取完成', 'success');
                } else {
                    resultDiv.innerHTML = '<p>未找到自动补全建议</p>';
                    updateStatus('未找到自动补全建议', 'info');
                }
            });
        }

        function testGeocode() {
            if (!isInitialized) {
                updateStatus('服务未初始化', 'error');
                return;
            }

            const address = document.getElementById('addressInput').value.trim();
            if (!address) {
                updateStatus('请输入地址', 'error');
                return;
            }

            updateStatus('正在进行地理编码...', 'info');
            
            geocoder.getLocation(address, (status, result) => {
                const resultDiv = document.getElementById('geocodeResult');
                
                if (status === 'complete' && result.geocodes && result.geocodes.length > 0) {
                    const geocode = result.geocodes[0];
                    resultDiv.innerHTML = `
                        <h4>地理编码结果:</h4>
                        <p><strong>地址:</strong> ${geocode.formattedAddress}</p>
                        <p><strong>坐标:</strong> ${geocode.location.lat}, ${geocode.location.lng}</p>
                        <p><strong>置信度:</strong> ${geocode.level || '未知'}</p>
                        <p><strong>省份:</strong> ${geocode.addressComponent?.province || '未知'}</p>
                        <p><strong>城市:</strong> ${geocode.addressComponent?.city || '未知'}</p>
                        <p><strong>区县:</strong> ${geocode.addressComponent?.district || '未知'}</p>
                    `;
                    updateStatus('地理编码完成', 'success');
                } else {
                    resultDiv.innerHTML = '<p>地理编码失败</p>';
                    updateStatus('地理编码失败', 'error');
                }
            });
        }

        function testReverseGeocode() {
            if (!isInitialized) {
                updateStatus('服务未初始化', 'error');
                return;
            }

            const lng = parseFloat(document.getElementById('lngInput').value);
            const lat = parseFloat(document.getElementById('latInput').value);
            
            if (isNaN(lng) || isNaN(lat)) {
                updateStatus('请输入有效的经纬度', 'error');
                return;
            }

            updateStatus('正在进行逆地理编码...', 'info');
            
            const lnglat = new AMap.LngLat(lng, lat);
            
            geocoder.getAddress(lnglat, (status, result) => {
                const resultDiv = document.getElementById('reverseGeocodeResult');
                
                if (status === 'complete' && result.regeocode) {
                    const regeocode = result.regeocode;
                    resultDiv.innerHTML = `
                        <h4>逆地理编码结果:</h4>
                        <p><strong>详细地址:</strong> ${regeocode.formattedAddress}</p>
                        <p><strong>省份:</strong> ${regeocode.addressComponent?.province || '未知'}</p>
                        <p><strong>城市:</strong> ${regeocode.addressComponent?.city || '未知'}</p>
                        <p><strong>区县:</strong> ${regeocode.addressComponent?.district || '未知'}</p>
                        <p><strong>街道:</strong> ${regeocode.addressComponent?.street || '未知'}</p>
                        <p><strong>门牌号:</strong> ${regeocode.addressComponent?.streetNumber || '未知'}</p>
                    `;
                    updateStatus('逆地理编码完成', 'success');
                } else {
                    resultDiv.innerHTML = '<p>逆地理编码失败</p>';
                    updateStatus('逆地理编码失败', 'error');
                }
            });
        }

        function useBeijingCoords() {
            document.getElementById('lngInput').value = '116.4074';
            document.getElementById('latInput').value = '39.9042';
            updateStatus('已设置北京天安门坐标', 'info');
        }
    </script>
</body>
</html>
