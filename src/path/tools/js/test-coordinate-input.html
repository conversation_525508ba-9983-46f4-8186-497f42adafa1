<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手动坐标输入功能测试</title>
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-section {
            background: #fafafa;
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            border: 1px solid #e8e8e8;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .coordinate-input-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .coordinate-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            text-align: center;
        }
        
        .coordinate-input:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px 5px 5px 0;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #40a9ff;
        }
        
        .btn-success {
            background: #52c41a;
        }
        
        .btn-success:hover {
            background: #73d13d;
        }
        
        .btn-warning {
            background: #faad14;
        }
        
        .btn-warning:hover {
            background: #ffc53d;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .btn-city {
            padding: 4px 8px;
            font-size: 11px;
            background: #f0f0f0;
            border: 1px solid #d9d9d9;
            border-radius: 3px;
            cursor: pointer;
            color: #666;
            margin: 2px;
        }
        
        .btn-city:hover {
            background: #e6f7ff;
            border-color: #91d5ff;
            color: #1890ff;
        }
        
        .coordinate-actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
            margin: 10px 0;
        }
        
        .result {
            background: white;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            border: 1px solid #e8e8e8;
            min-height: 50px;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        
        .status.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
        
        .status.info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0958d9;
        }
        
        .city-buttons {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
            margin: 10px 0;
        }
        
        .coordinate-display {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 13px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📐 手动坐标输入功能测试</h1>
        
        <div class="test-section">
            <h3>🟢 起点坐标设置</h3>
            <div class="coordinate-input-row">
                <input type="number" id="startLng" class="coordinate-input" placeholder="经度 (Longitude)" step="0.000001" min="-180" max="180">
                <input type="number" id="startLat" class="coordinate-input" placeholder="纬度 (Latitude)" step="0.000001" min="-90" max="90">
            </div>
            <div class="coordinate-actions">
                <button class="btn btn-success" onclick="setStartPointFromInput()">设置起点</button>
                <button class="btn btn-small" onclick="getCurrentLocation('start')">获取当前位置</button>
                <button class="btn btn-small" onclick="validateCoordinates('start')">验证坐标</button>
            </div>
            <div id="startResult" class="result">等待设置起点坐标...</div>
        </div>
        
        <div class="test-section">
            <h3>🔴 终点坐标设置</h3>
            <div class="coordinate-input-row">
                <input type="number" id="endLng" class="coordinate-input" placeholder="经度 (Longitude)" step="0.000001" min="-180" max="180">
                <input type="number" id="endLat" class="coordinate-input" placeholder="纬度 (Latitude)" step="0.000001" min="-90" max="90">
            </div>
            <div class="coordinate-actions">
                <button class="btn btn-warning" onclick="setEndPointFromInput()">设置终点</button>
                <button class="btn btn-small" onclick="getCurrentLocation('end')">获取当前位置</button>
                <button class="btn btn-small" onclick="validateCoordinates('end')">验证坐标</button>
            </div>
            <div id="endResult" class="result">等待设置终点坐标...</div>
        </div>
        
        <div class="test-section">
            <h3>🏙️ 快速填充城市坐标</h3>
            <p style="margin: 5px 0; color: #666; font-size: 14px;">选择填充目标和城市：</p>
            <div class="coordinate-actions" style="margin-bottom: 10px;">
                <button class="btn btn-small" onclick="fillTarget = 'start'; updateFillTargetDisplay()">填充到起点</button>
                <button class="btn btn-small" onclick="fillTarget = 'end'; updateFillTargetDisplay()">填充到终点</button>
                <span id="fillTargetDisplay" style="margin-left: 10px; font-size: 12px; color: #666;">当前目标: 起点</span>
            </div>
            <div class="city-buttons">
                <button class="btn-city" onclick="fillSampleCoordinates('beijing')">北京天安门</button>
                <button class="btn-city" onclick="fillSampleCoordinates('shanghai')">上海人民广场</button>
                <button class="btn-city" onclick="fillSampleCoordinates('guangzhou')">广州天河城</button>
                <button class="btn-city" onclick="fillSampleCoordinates('shenzhen')">深圳市民中心</button>
                <button class="btn-city" onclick="fillSampleCoordinates('hangzhou')">杭州西湖</button>
                <button class="btn-city" onclick="fillSampleCoordinates('nanjing')">南京夫子庙</button>
                <button class="btn-city" onclick="fillSampleCoordinates('chengdu')">成都天府广场</button>
                <button class="btn-city" onclick="fillSampleCoordinates('wuhan')">武汉黄鹤楼</button>
            </div>
        </div>

        <div class="test-section">
            <h3>📚 坐标历史记录测试</h3>
            <div class="coordinate-actions">
                <button class="btn" onclick="addTestHistory()">添加测试历史</button>
                <button class="btn" onclick="showHistory()">显示历史记录</button>
                <button class="btn" onclick="clearTestHistory()">清空历史</button>
            </div>
            <div id="historyDisplay" class="result">历史记录将在这里显示...</div>
        </div>

        <div class="test-section">
            <h3>⭐ 坐标收藏夹测试</h3>
            <div class="coordinate-actions">
                <button class="btn" onclick="addTestFavorite()">添加测试收藏</button>
                <button class="btn" onclick="showFavorites()">显示收藏夹</button>
                <button class="btn" onclick="exportFavorites()">导出收藏</button>
            </div>
            <div id="favoritesDisplay" class="result">收藏夹将在这里显示...</div>
        </div>
        
        <div class="test-section">
            <h3>🛣️ 算路测试</h3>
            <div class="coordinate-actions">
                <button class="btn" onclick="calculateRouteFromCoords()">开始算路</button>
                <button class="btn" onclick="clearCoordinateInputs()">清空输入</button>
                <button class="btn" onclick="fillTestRoute()">填充测试路线</button>
            </div>
            <div id="routeResult" class="result">等待算路...</div>
        </div>
        
        <div class="test-section">
            <h3>📊 坐标信息显示</h3>
            <div id="coordinateInfo" class="coordinate-display">
                当前坐标信息将在这里显示...
            </div>
        </div>
        
        <div id="status" class="status info">
            页面已加载，可以开始测试手动坐标输入功能
        </div>
    </div>

    <script>
        // 模拟全局变量
        let startPoint = null;
        let endPoint = null;
        let fillTarget = 'start'; // 'start' or 'end'
        let testHistory = [];
        let testFavorites = [];
        let fillTarget = 'start'; // 'start' or 'end'
        let testHistory = [];
        let testFavorites = [];

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function setStartPointFromInput() {
            const lng = parseFloat(document.getElementById('startLng').value);
            const lat = parseFloat(document.getElementById('startLat').value);
            
            if (isNaN(lng) || isNaN(lat)) {
                updateStatus('请输入有效的起点坐标', 'error');
                return;
            }
            
            if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
                updateStatus('坐标范围错误：经度(-180~180)，纬度(-90~90)', 'error');
                return;
            }
            
            startPoint = [lng, lat];
            const coordsText = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
            
            document.getElementById('startResult').innerHTML = `
                <h4>✅ 起点已设置</h4>
                <p><strong>坐标:</strong> ${coordsText}</p>
                <p><strong>经度:</strong> ${lng.toFixed(6)}</p>
                <p><strong>纬度:</strong> ${lat.toFixed(6)}</p>
                <p><strong>格式:</strong> WGS84坐标系</p>
            `;
            
            updateStatus(`起点已设置: ${coordsText}`, 'success');
            updateCoordinateInfo();
        }

        function setEndPointFromInput() {
            const lng = parseFloat(document.getElementById('endLng').value);
            const lat = parseFloat(document.getElementById('endLat').value);
            
            if (isNaN(lng) || isNaN(lat)) {
                updateStatus('请输入有效的终点坐标', 'error');
                return;
            }
            
            if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
                updateStatus('坐标范围错误：经度(-180~180)，纬度(-90~90)', 'error');
                return;
            }
            
            endPoint = [lng, lat];
            const coordsText = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
            
            document.getElementById('endResult').innerHTML = `
                <h4>✅ 终点已设置</h4>
                <p><strong>坐标:</strong> ${coordsText}</p>
                <p><strong>经度:</strong> ${lng.toFixed(6)}</p>
                <p><strong>纬度:</strong> ${lat.toFixed(6)}</p>
                <p><strong>格式:</strong> WGS84坐标系</p>
            `;
            
            updateStatus(`终点已设置: ${coordsText}`, 'success');
            updateCoordinateInfo();
        }

        function getCurrentLocation(type) {
            // 模拟获取当前位置（使用北京坐标作为示例）
            const lng = 116.4074;
            const lat = 39.9042;
            
            if (type === 'start') {
                document.getElementById('startLng').value = lng.toFixed(6);
                document.getElementById('startLat').value = lat.toFixed(6);
                updateStatus('已获取当前位置作为起点坐标（模拟：北京天安门）', 'info');
            } else if (type === 'end') {
                document.getElementById('endLng').value = lng.toFixed(6);
                document.getElementById('endLat').value = lat.toFixed(6);
                updateStatus('已获取当前位置作为终点坐标（模拟：北京天安门）', 'info');
            }
        }

        function validateCoordinates(type) {
            const lngId = type === 'start' ? 'startLng' : 'endLng';
            const latId = type === 'start' ? 'startLat' : 'endLat';
            
            const lng = parseFloat(document.getElementById(lngId).value);
            const lat = parseFloat(document.getElementById(latId).value);
            
            if (isNaN(lng) || isNaN(lat)) {
                updateStatus(`${type === 'start' ? '起点' : '终点'}坐标格式错误`, 'error');
                return;
            }
            
            if (lng < -180 || lng > 180) {
                updateStatus(`经度范围错误：${lng}，应在-180到180之间`, 'error');
                return;
            }
            
            if (lat < -90 || lat > 90) {
                updateStatus(`纬度范围错误：${lat}，应在-90到90之间`, 'error');
                return;
            }
            
            updateStatus(`${type === 'start' ? '起点' : '终点'}坐标验证通过`, 'success');
        }

        function fillSampleCoordinates(city) {
            const coordinates = {
                beijing: { lng: 116.4074, lat: 39.9042, name: '北京天安门' },
                shanghai: { lng: 121.4737, lat: 31.2304, name: '上海人民广场' },
                guangzhou: { lng: 113.2644, lat: 23.1291, name: '广州天河城' },
                shenzhen: { lng: 114.0579, lat: 22.5431, name: '深圳市民中心' },
                hangzhou: { lng: 120.1551, lat: 30.2741, name: '杭州西湖' },
                nanjing: { lng: 118.7969, lat: 32.0603, name: '南京夫子庙' },
                chengdu: { lng: 104.0665, lat: 30.5723, name: '成都天府广场' },
                wuhan: { lng: 114.3054, lat: 30.5931, name: '武汉黄鹤楼' }
            };

            const coord = coordinates[city];
            if (coord) {
                const targetPrefix = fillTarget === 'start' ? 'start' : 'end';
                const targetName = fillTarget === 'start' ? '起点' : '终点';

                document.getElementById(targetPrefix + 'Lng').value = coord.lng.toFixed(6);
                document.getElementById(targetPrefix + 'Lat').value = coord.lat.toFixed(6);
                updateStatus(`已填充${coord.name}坐标到${targetName}`, 'info');

                // 添加到测试历史
                addToTestHistory(coord.lng, coord.lat, coord.name, `城市坐标 - ${targetName}`);
            }
        }

        function updateFillTargetDisplay() {
            const display = document.getElementById('fillTargetDisplay');
            display.textContent = `当前目标: ${fillTarget === 'start' ? '起点' : '终点'}`;
        }

        function addToTestHistory(lng, lat, name, note) {
            const item = {
                id: Date.now(),
                lng: lng,
                lat: lat,
                name: name,
                note: note,
                time: new Date().toLocaleString('zh-CN')
            };
            testHistory.unshift(item);
            if (testHistory.length > 10) {
                testHistory = testHistory.slice(0, 10);
            }
        }

        function addTestHistory() {
            const testCoords = [
                { lng: 116.4074, lat: 39.9042, name: '北京天安门', note: '测试历史记录1' },
                { lng: 121.4737, lat: 31.2304, name: '上海人民广场', note: '测试历史记录2' },
                { lng: 113.2644, lat: 23.1291, name: '广州天河城', note: '测试历史记录3' }
            ];

            testCoords.forEach(coord => {
                addToTestHistory(coord.lng, coord.lat, coord.name, coord.note);
            });

            updateStatus('已添加3个测试历史记录', 'success');
            showHistory();
        }

        function showHistory() {
            const display = document.getElementById('historyDisplay');
            if (testHistory.length === 0) {
                display.innerHTML = '<p>暂无历史记录</p>';
                return;
            }

            let html = '<h4>📜 历史记录列表:</h4>';
            testHistory.forEach((item, index) => {
                html += `
                    <div style="border: 1px solid #e8e8e8; padding: 8px; margin: 5px 0; border-radius: 4px;">
                        <strong>${item.name}</strong><br>
                        <span style="font-family: monospace; font-size: 12px;">${item.lat.toFixed(6)}, ${item.lng.toFixed(6)}</span><br>
                        <span style="color: #666; font-size: 11px;">${item.note} - ${item.time}</span><br>
                        <button class="btn btn-small" onclick="useHistoryItem(${index})">使用此坐标</button>
                    </div>
                `;
            });
            display.innerHTML = html;
        }

        function useHistoryItem(index) {
            const item = testHistory[index];
            if (item) {
                document.getElementById('startLng').value = item.lng.toFixed(6);
                document.getElementById('startLat').value = item.lat.toFixed(6);
                updateStatus(`已使用历史记录: ${item.name}`, 'info');
            }
        }

        function clearTestHistory() {
            testHistory = [];
            showHistory();
            updateStatus('测试历史记录已清空', 'info');
        }

        function addTestFavorite() {
            const testFavs = [
                { lng: 116.4074, lat: 39.9042, name: '我的公司', note: '北京办公地点' },
                { lng: 121.4737, lat: 31.2304, name: '我的家', note: '上海住址' },
                { lng: 113.2644, lat: 23.1291, name: '常去餐厅', note: '广州美食' }
            ];

            testFavs.forEach(fav => {
                const item = {
                    id: Date.now() + Math.random(),
                    lng: fav.lng,
                    lat: fav.lat,
                    name: fav.name,
                    note: fav.note,
                    time: new Date().toLocaleString('zh-CN')
                };
                testFavorites.push(item);
            });

            updateStatus('已添加3个测试收藏', 'success');
            showFavorites();
        }

        function showFavorites() {
            const display = document.getElementById('favoritesDisplay');
            if (testFavorites.length === 0) {
                display.innerHTML = '<p>暂无收藏坐标</p>';
                return;
            }

            let html = '<h4>⭐ 收藏夹列表:</h4>';
            testFavorites.forEach((item, index) => {
                html += `
                    <div style="border: 1px solid #e8e8e8; padding: 8px; margin: 5px 0; border-radius: 4px;">
                        <strong>⭐ ${item.name}</strong><br>
                        <span style="font-family: monospace; font-size: 12px;">${item.lat.toFixed(6)}, ${item.lng.toFixed(6)}</span><br>
                        <span style="color: #666; font-size: 11px;">${item.note} - 收藏于 ${item.time}</span><br>
                        <button class="btn btn-small btn-success" onclick="useFavoriteItem(${index}, 'start')">设为起点</button>
                        <button class="btn btn-small btn-warning" onclick="useFavoriteItem(${index}, 'end')">设为终点</button>
                        <button class="btn btn-small" onclick="removeFavoriteItem(${index})">删除</button>
                    </div>
                `;
            });
            display.innerHTML = html;
        }

        function useFavoriteItem(index, target) {
            const item = testFavorites[index];
            if (item) {
                const targetPrefix = target === 'start' ? 'start' : 'end';
                const targetName = target === 'start' ? '起点' : '终点';

                document.getElementById(targetPrefix + 'Lng').value = item.lng.toFixed(6);
                document.getElementById(targetPrefix + 'Lat').value = item.lat.toFixed(6);
                updateStatus(`已设置${targetName}: ${item.name}`, 'success');
            }
        }

        function removeFavoriteItem(index) {
            const item = testFavorites[index];
            if (item && confirm(`确定要删除收藏"${item.name}"吗？`)) {
                testFavorites.splice(index, 1);
                showFavorites();
                updateStatus(`已删除收藏: ${item.name}`, 'info');
            }
        }

        function exportFavorites() {
            if (testFavorites.length === 0) {
                updateStatus('收藏夹为空，无法导出', 'error');
                return;
            }

            const data = {
                type: 'coordinate_favorites',
                version: '1.0',
                exportTime: new Date().toISOString(),
                data: testFavorites
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `test_favorites_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            updateStatus('收藏夹数据已导出', 'success');
        }

        function calculateRouteFromCoords() {
            if (!startPoint || !endPoint) {
                updateStatus('请先设置起点和终点坐标', 'error');
                return;
            }
            
            const startText = `${startPoint[1].toFixed(6)}, ${startPoint[0].toFixed(6)}`;
            const endText = `${endPoint[1].toFixed(6)}, ${endPoint[0].toFixed(6)}`;
            
            // 计算直线距离
            const distance = calculateDistance(startPoint[1], startPoint[0], endPoint[1], endPoint[0]);
            
            document.getElementById('routeResult').innerHTML = `
                <h4>🛣️ 算路信息</h4>
                <p><strong>起点:</strong> ${startText}</p>
                <p><strong>终点:</strong> ${endText}</p>
                <p><strong>直线距离:</strong> ${(distance / 1000).toFixed(2)} 公里</p>
                <p><strong>状态:</strong> 坐标验证通过，可以进行实际算路</p>
                <p style="color: #666; font-size: 12px;">注：这是测试页面，实际算路需要在主系统中进行</p>
            `;
            
            updateStatus(`算路准备完成: ${startText} → ${endText}`, 'success');
        }

        function clearCoordinateInputs() {
            document.getElementById('startLng').value = '';
            document.getElementById('startLat').value = '';
            document.getElementById('endLng').value = '';
            document.getElementById('endLat').value = '';
            
            document.getElementById('startResult').innerHTML = '等待设置起点坐标...';
            document.getElementById('endResult').innerHTML = '等待设置终点坐标...';
            document.getElementById('routeResult').innerHTML = '等待算路...';
            
            startPoint = null;
            endPoint = null;
            
            updateStatus('坐标输入已清空', 'info');
            updateCoordinateInfo();
        }

        function fillTestRoute() {
            // 填充北京到上海的测试路线
            document.getElementById('startLng').value = '116.4074';
            document.getElementById('startLat').value = '39.9042';
            document.getElementById('endLng').value = '121.4737';
            document.getElementById('endLat').value = '31.2304';
            
            updateStatus('已填充测试路线：北京天安门 → 上海人民广场', 'info');
        }

        function updateCoordinateInfo() {
            let info = '当前坐标信息:\n\n';
            
            if (startPoint) {
                info += `🟢 起点: ${startPoint[1].toFixed(6)}, ${startPoint[0].toFixed(6)}\n`;
                info += `   经度: ${startPoint[0].toFixed(6)}\n`;
                info += `   纬度: ${startPoint[1].toFixed(6)}\n\n`;
            } else {
                info += '🟢 起点: 未设置\n\n';
            }
            
            if (endPoint) {
                info += `🔴 终点: ${endPoint[1].toFixed(6)}, ${endPoint[0].toFixed(6)}\n`;
                info += `   经度: ${endPoint[0].toFixed(6)}\n`;
                info += `   纬度: ${endPoint[1].toFixed(6)}\n\n`;
            } else {
                info += '🔴 终点: 未设置\n\n';
            }
            
            if (startPoint && endPoint) {
                const distance = calculateDistance(startPoint[1], startPoint[0], endPoint[1], endPoint[0]);
                info += `📏 直线距离: ${(distance / 1000).toFixed(2)} 公里\n`;
            }
            
            info += '\n💡 坐标系: WGS84 (GPS标准)';
            
            document.getElementById('coordinateInfo').textContent = info;
        }

        function calculateDistance(lat1, lng1, lat2, lng2) {
            const R = 6371000; // 地球半径（米）
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                    Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        // 初始化
        updateCoordinateInfo();
    </script>
</body>
</html>
