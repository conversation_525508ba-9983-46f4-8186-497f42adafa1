<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路径规划调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0958d9;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
    </style>
</head>
<body>
    <h1>🔧 路径规划调试测试</h1>
    
    <div class="test-container">
        <h2>🧪 路径规划器初始化测试</h2>
        <button onclick="testRoutePlannerInit()">测试初始化</button>
        <div id="initTest" class="test-result info">点击按钮开始测试</div>
    </div>
    
    <div class="test-container">
        <h2>🛣️ 路径计算测试</h2>
        <button onclick="testRouteCalculation()">测试路径计算</button>
        <div id="routeTest" class="test-result info">点击按钮开始测试</div>
    </div>
    
    <div class="test-container">
        <h2>📊 API状态检查</h2>
        <button onclick="checkAPIStatus()">检查API状态</button>
        <div id="apiStatus" class="test-result info">点击按钮开始检查</div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=f7f401fd4ffb75720cba09ffb7b24a4c&plugin=AMap.Driving,AMap.Walking,AMap.Riding"></script>
    <script src="coordinate-converter.js"></script>
    <script src="route-planner.js"></script>
    
    <script>
        let testRoutePlanner = null;
        
        /**
         * 显示测试结果
         */
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `test-result ${type}`;
            element.textContent = message;
        }
        
        /**
         * 测试路径规划器初始化
         */
        async function testRoutePlannerInit() {
            showResult('initTest', '正在测试路径规划器初始化...', 'info');
            
            try {
                // 创建路径规划器实例
                testRoutePlanner = new RoutePlanner();
                
                // 检查初始状态
                const initialReady = testRoutePlanner.isReady();
                console.log('初始状态:', initialReady);
                
                // 等待初始化完成
                const startTime = Date.now();
                await testRoutePlanner.waitForInit();
                const endTime = Date.now();
                
                // 检查最终状态
                const finalReady = testRoutePlanner.isReady();
                
                const result = `✅ 路径规划器初始化测试通过
初始状态: ${initialReady ? '已就绪' : '未就绪'}
最终状态: ${finalReady ? '已就绪' : '未就绪'}
初始化耗时: ${endTime - startTime}ms
驾车服务: ${testRoutePlanner.driving ? '✓' : '✗'}
步行服务: ${testRoutePlanner.walking ? '✓' : '✗'}
骑行服务: ${testRoutePlanner.riding ? '✓' : '✗'}`;
                
                showResult('initTest', result, 'success');
                
            } catch (error) {
                showResult('initTest', `❌ 初始化测试失败: ${error.message}`, 'error');
                console.error('初始化测试失败:', error);
            }
        }
        
        /**
         * 测试路径计算
         */
        async function testRouteCalculation() {
            showResult('routeTest', '正在测试路径计算...', 'info');
            
            try {
                if (!testRoutePlanner) {
                    throw new Error('请先运行初始化测试');
                }
                
                // 测试坐标：北京天安门到故宫
                const startPoint = [116.4074, 39.9042]; // 天安门 (GCJ02)
                const endPoint = [116.3974, 39.9163];   // 故宫 (GCJ02)
                
                console.log('开始路径计算测试...');
                console.log('起点:', startPoint);
                console.log('终点:', endPoint);
                
                const startTime = Date.now();
                const route = await testRoutePlanner.calculateRoute(startPoint, endPoint, []);
                const endTime = Date.now();
                
                const result = `✅ 路径计算测试通过
起点: ${startPoint.join(', ')}
终点: ${endPoint.join(', ')}
计算耗时: ${endTime - startTime}ms
路径类型: ${route.type}
总距离: ${testRoutePlanner.formatDistance(route.totalDistance)}
预计时间: ${testRoutePlanner.formatTime(route.totalTime)}
路径点数: ${route.coordinates.length}
导航指令数: ${route.instructions.length}`;
                
                showResult('routeTest', result, 'success');
                
            } catch (error) {
                showResult('routeTest', `❌ 路径计算测试失败: ${error.message}`, 'error');
                console.error('路径计算测试失败:', error);
            }
        }
        
        /**
         * 检查API状态
         */
        function checkAPIStatus() {
            showResult('apiStatus', '正在检查API状态...', 'info');
            
            try {
                const status = {
                    AMap: typeof AMap !== 'undefined',
                    AMapVersion: typeof AMap !== 'undefined' ? (AMap.version || '未知') : '未加载',
                    Driving: typeof AMap !== 'undefined' && AMap.Driving,
                    Walking: typeof AMap !== 'undefined' && AMap.Walking,
                    Riding: typeof AMap !== 'undefined' && AMap.Riding,
                    LngLat: typeof AMap !== 'undefined' && AMap.LngLat,
                    Map: typeof AMap !== 'undefined' && AMap.Map
                };
                
                const result = `📊 API状态检查结果
高德地图API: ${status.AMap ? '✓ 已加载' : '✗ 未加载'}
API版本: ${status.AMapVersion}
驾车服务: ${status.Driving ? '✓ 可用' : '✗ 不可用'}
步行服务: ${status.Walking ? '✓ 可用' : '✗ 不可用'}
骑行服务: ${status.Riding ? '✓ 可用' : '✗ 不可用'}
坐标类: ${status.LngLat ? '✓ 可用' : '✗ 不可用'}
地图类: ${status.Map ? '✓ 可用' : '✗ 不可用'}

浏览器信息:
User Agent: ${navigator.userAgent}
当前时间: ${new Date().toLocaleString()}`;
                
                const allGood = Object.values(status).every(s => s === true || typeof s === 'string');
                showResult('apiStatus', result, allGood ? 'success' : 'error');
                
            } catch (error) {
                showResult('apiStatus', `❌ API状态检查失败: ${error.message}`, 'error');
                console.error('API状态检查失败:', error);
            }
        }
        
        // 页面加载完成后自动检查API状态
        window.addEventListener('load', function() {
            setTimeout(checkAPIStatus, 1000);
        });
    </script>
</body>
</html>
