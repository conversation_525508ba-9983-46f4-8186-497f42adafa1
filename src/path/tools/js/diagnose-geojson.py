#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GeoJSON数据诊断脚本
用于检查GeoJSON文件是否与QGIS样式兼容
"""

import json
import sys
from collections import Counter
from pathlib import Path

def diagnose_geojson(file_path):
    """诊断GeoJSON文件"""
    print(f"🔍 诊断GeoJSON文件: {file_path}")
    
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查基本结构
        if data.get('type') != 'FeatureCollection':
            print(f"❌ 根类型错误: {data.get('type')}, 期望: FeatureCollection")
            return False
        
        print("✅ GeoJSON格式正确")
        
        features = data.get('features', [])
        if not features:
            print("❌ 没有找到要素")
            return False
        
        print(f"📊 总要素数量: {len(features)}")
        
        # 检查type字段
        type_values = []
        geometry_types = []
        missing_type_count = 0
        
        for i, feature in enumerate(features):
            properties = feature.get('properties', {})
            geometry = feature.get('geometry', {})
            
            # 检查type字段
            feature_type = properties.get('type')
            if feature_type:
                type_values.append(feature_type)
            else:
                missing_type_count += 1
                print(f"⚠️  要素 {i+1} 缺少type字段")
            
            # 检查几何类型
            geom_type = geometry.get('type')
            if geom_type:
                geometry_types.append(geom_type)
        
        # 统计type字段值
        if type_values:
            type_counter = Counter(type_values)
            print("\n📋 type字段值统计:")
            for type_val, count in type_counter.items():
                print(f"  - {type_val}: {count}个")
        
        if missing_type_count > 0:
            print(f"⚠️  {missing_type_count}个要素缺少type字段")
        
        # 统计几何类型
        if geometry_types:
            geom_counter = Counter(geometry_types)
            print("\n🗺️  几何类型统计:")
            for geom_type, count in geom_counter.items():
                print(f"  - {geom_type}: {count}个")
        
        # 检查期望的type值
        expected_types = {
            'start_point', 'end_point', 'direct_line', 
            'amap_route', 'cpp_route', 'comparison_analysis'
        }
        found_types = set(type_values)
        
        print("\n🎯 样式兼容性检查:")
        for expected_type in expected_types:
            if expected_type in found_types:
                count = type_counter.get(expected_type, 0)
                print(f"  ✅ {expected_type}: {count}个")
            else:
                print(f"  ❌ {expected_type}: 未找到")
        
        # 检查额外的type值
        extra_types = found_types - expected_types
        if extra_types:
            print(f"\n⚠️  发现额外的type值: {extra_types}")
            print("   这些值在样式文件中没有定义，将显示为默认样式")
        
        # 检查关键字段
        print("\n🔍 关键字段检查:")
        key_fields = ['test_id', 'name', 'test_status', 'distance_diff_percent']
        
        for field in key_fields:
            field_count = 0
            for feature in features:
                if field in feature.get('properties', {}):
                    field_count += 1
            
            if field_count > 0:
                print(f"  ✅ {field}: {field_count}/{len(features)}个要素包含")
            else:
                print(f"  ❌ {field}: 未找到")
        
        # 检查distance_diff_percent字段的数据类型
        diff_percent_values = []
        for feature in features:
            props = feature.get('properties', {})
            if 'distance_diff_percent' in props:
                value = props['distance_diff_percent']
                if isinstance(value, (int, float)):
                    diff_percent_values.append(value)
                else:
                    print(f"⚠️  distance_diff_percent字段包含非数值: {value}")
        
        if diff_percent_values:
            min_val = min(diff_percent_values)
            max_val = max(diff_percent_values)
            avg_val = sum(diff_percent_values) / len(diff_percent_values)
            print(f"\n📈 distance_diff_percent统计:")
            print(f"  - 范围: {min_val:.1f}% ~ {max_val:.1f}%")
            print(f"  - 平均: {avg_val:.1f}%")
            
            # 分级统计
            low_count = sum(1 for v in diff_percent_values if v < 5)
            mid_count = sum(1 for v in diff_percent_values if 5 <= v <= 10)
            high_count = sum(1 for v in diff_percent_values if v > 10)
            
            print(f"  - 低差异(<5%): {low_count}个")
            print(f"  - 中差异(5-10%): {mid_count}个")
            print(f"  - 高差异(>10%): {high_count}个")
        
        print("\n🎉 诊断完成!")
        
        # 给出建议
        print("\n💡 建议:")
        if missing_type_count > 0:
            print("  1. 确保所有要素都有type字段")
        
        if extra_types:
            print("  2. 检查type字段值是否正确拼写")
        
        missing_expected = expected_types - found_types
        if missing_expected:
            print(f"  3. 缺少以下要素类型: {missing_expected}")
        
        if not diff_percent_values:
            print("  4. comparison_analysis要素需要distance_diff_percent字段用于颜色分级")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 诊断过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python3 diagnose-geojson.py <geojson文件路径>")
        print("示例: python3 diagnose-geojson.py sample_dual_route_test.geojson")
        return 1
    
    file_path = sys.argv[1]
    
    print("🧪 GeoJSON数据诊断工具")
    print("=" * 50)
    
    success = diagnose_geojson(file_path)
    
    if success:
        print("\n✅ 诊断完成，请根据建议调整数据")
    else:
        print("\n❌ 诊断失败，请检查文件格式")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
