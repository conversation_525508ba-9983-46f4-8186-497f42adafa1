#!/usr/bin/env python3
"""
简单的HTTP服务器，用于本地测试Leaflet地图应用
支持CORS，方便开发调试
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """支持CORS的HTTP请求处理器"""
    
    def end_headers(self):
        """添加CORS头"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()
    
    def do_OPTIONS(self):
        """处理OPTIONS请求"""
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{self.log_date_time_string()}] {format % args}")

def start_server(port=8000, directory=None):
    """启动HTTP服务器"""
    
    if directory:
        os.chdir(directory)
    
    # 显示当前目录内容
    print(f"服务器根目录: {os.getcwd()}")
    print("目录内容:")
    for item in os.listdir('.'):
        if os.path.isdir(item):
            print(f"  📁 {item}/")
        else:
            print(f"  📄 {item}")
    print()
    
    # 创建服务器
    with socketserver.TCPServer(("", port), CORSHTTPRequestHandler) as httpd:
        print(f"🚀 HTTP服务器已启动")
        print(f"📍 地址: http://localhost:{port}")
        print(f"🌐 网络地址: http://0.0.0.0:{port}")
        print(f"📂 根目录: {os.getcwd()}")
        print()
        print("💡 使用提示:")
        print(f"   - 在浏览器中访问: http://localhost:{port}")
        print(f"   - 查看地图应用: http://localhost:{port}/index.html")
        print("   - 按 Ctrl+C 停止服务器")
        print()
        print("=" * 50)
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 服务器已停止")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='启动本地HTTP服务器用于测试地图应用')
    parser.add_argument('-p', '--port', type=int, default=8000, 
                       help='服务器端口号 (默认: 8000)')
    parser.add_argument('-d', '--directory', type=str, 
                       help='服务器根目录 (默认: 当前目录)')
    
    args = parser.parse_args()
    
    # 检查端口是否可用
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', args.port))
    sock.close()
    
    if result == 0:
        print(f"❌ 端口 {args.port} 已被占用，请尝试其他端口")
        print(f"💡 建议使用: python3 server.py -p {args.port + 1}")
        sys.exit(1)
    
    start_server(args.port, args.directory)

if __name__ == '__main__':
    main()
