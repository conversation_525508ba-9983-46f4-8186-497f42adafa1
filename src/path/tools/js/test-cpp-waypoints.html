<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C++算路途经点功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .test-section {
            padding: 20px;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .test-section:last-child {
            border-bottom: none;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            border-left: 4px solid #1890ff;
            padding-left: 10px;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            align-items: center;
        }
        
        .input-group label {
            min-width: 80px;
            font-weight: 500;
            color: #333;
        }
        
        .input-group input {
            flex: 1;
            padding: 6px 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 12px;
            font-family: monospace;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
            margin: 5px;
        }
        
        .btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }
        
        .result {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: monospace;
        }
        
        .result.error {
            background: #fff2f0;
            border-color: #ffccc7;
        }
        
        .result.info {
            background: #e6f7ff;
            border-color: #91d5ff;
        }
        
        .waypoint-item {
            background: #f0f8ff;
            border: 1px solid #d6e4ff;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .waypoint-coords {
            font-family: monospace;
            font-size: 12px;
            color: #666;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛣️ C++算路途经点功能测试</h1>
            <p>测试C++算路服务器的途经点支持功能</p>
        </div>

        <div class="test-section">
            <h3>🎯 坐标设置</h3>
            <div class="input-group">
                <label>起点经度:</label>
                <input type="number" id="startLng" value="116.407400" step="0.000001">
                <label>起点纬度:</label>
                <input type="number" id="startLat" value="39.904200" step="0.000001">
            </div>
            <div class="input-group">
                <label>终点经度:</label>
                <input type="number" id="endLng" value="116.427400" step="0.000001">
                <label>终点纬度:</label>
                <input type="number" id="endLat" value="39.924200" step="0.000001">
            </div>
        </div>

        <div class="test-section">
            <h3>🔵 途经点管理</h3>
            <div class="input-group">
                <label>途经点经度:</label>
                <input type="number" id="waypointLng" value="116.417400" step="0.000001">
                <label>途经点纬度:</label>
                <input type="number" id="waypointLat" value="39.914200" step="0.000001">
                <button class="btn" onclick="addWaypoint()">添加途经点</button>
            </div>
            <div id="waypointsList">
                <p style="color: #999; text-align: center; margin: 20px 0;">暂无途经点</p>
            </div>
            <div style="margin-top: 10px;">
                <button class="btn" onclick="clearWaypoints()">清空途经点</button>
                <button class="btn" onclick="addSampleWaypoints()">添加示例途经点</button>
            </div>
        </div>

        <div class="test-section">
            <h3>⚙️ 算路策略</h3>
            <div class="input-group">
                <label>策略选择:</label>
                <select id="testStrategy" class="strategy-select" style="flex: 1;">
                    <option value="0">⏱️ 最短时间优先</option>
                    <option value="1">📏 最短距离优先</option>
                    <option value="2">🛣️ 高速优先</option>
                    <option value="3">💰 避开收费路段</option>
                </select>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 测试控制</h3>
            <div>
                <button class="btn btn-primary" onclick="testNoWaypoints()">测试无途经点算路</button>
                <button class="btn btn-primary" onclick="testWithWaypoints()">测试含途经点算路</button>
                <button class="btn btn-primary" onclick="testAllStrategies()">测试所有策略</button>
                <button class="btn" onclick="checkServerStatus()">检查服务器状态</button>
                <button class="btn" onclick="clearResults()">清空结果</button>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试结果</h3>
            <div id="testResults" class="result">
                等待测试...
            </div>
        </div>
    </div>

    <script src="cpp-route-client.js"></script>
    <script>
        // 全局变量
        let cppClient = new CppRouteClient('http://localhost:8080');
        let waypoints = [];

        // 添加途经点
        function addWaypoint() {
            const lng = parseFloat(document.getElementById('waypointLng').value);
            const lat = parseFloat(document.getElementById('waypointLat').value);
            
            if (isNaN(lng) || isNaN(lat)) {
                alert('请输入有效的途经点坐标');
                return;
            }
            
            waypoints.push({ lng, lat });
            updateWaypointsList();
            logResult(`添加途经点: (${lng}, ${lat})`);
        }

        // 删除途经点
        function removeWaypoint(index) {
            const removed = waypoints.splice(index, 1)[0];
            updateWaypointsList();
            logResult(`删除途经点: (${removed.lng}, ${removed.lat})`);
        }

        // 清空途经点
        function clearWaypoints() {
            waypoints = [];
            updateWaypointsList();
            logResult('已清空所有途经点');
        }

        // 添加示例途经点
        function addSampleWaypoints() {
            waypoints = [
                { lng: 116.412400, lat: 39.909200 },
                { lng: 116.417400, lat: 39.914200 },
                { lng: 116.422400, lat: 39.919200 }
            ];
            updateWaypointsList();
            logResult('已添加示例途经点');
        }

        // 更新途经点列表显示
        function updateWaypointsList() {
            const container = document.getElementById('waypointsList');
            
            if (waypoints.length === 0) {
                container.innerHTML = '<p style="color: #999; text-align: center; margin: 20px 0;">暂无途经点</p>';
                return;
            }
            
            let html = '';
            waypoints.forEach((wp, index) => {
                html += `
                    <div class="waypoint-item">
                        <div>
                            <strong>途经点 ${index + 1}</strong>
                            <div class="waypoint-coords">(${wp.lng.toFixed(6)}, ${wp.lat.toFixed(6)})</div>
                        </div>
                        <button class="btn btn-small" onclick="removeWaypoint(${index})">删除</button>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // 测试无途经点算路
        async function testNoWaypoints() {
            try {
                const strategy = parseInt(document.getElementById('testStrategy').value);
                const strategyNames = ['最短时间优先', '最短距离优先', '高速优先', '避开收费路段'];

                logResult(`开始测试无途经点算路 (策略: ${strategyNames[strategy]})...`);

                const startLng = parseFloat(document.getElementById('startLng').value);
                const startLat = parseFloat(document.getElementById('startLat').value);
                const endLng = parseFloat(document.getElementById('endLng').value);
                const endLat = parseFloat(document.getElementById('endLat').value);

                const startTime = Date.now();
                const result = await cppClient.calculateRoute(startLng, startLat, endLng, endLat, [], strategy);
                const endTime = Date.now();

                logResult(`✅ 无途经点算路成功 (耗时: ${endTime - startTime}ms)`);
                logResult(`策略: ${strategyNames[strategy]}`);
                logResult(`路径长度: ${result.totalDistance.toFixed(2)}米`);
                logResult(`预计时间: ${result.totalTime.toFixed(2)}秒`);
                logResult(`坐标点数: ${result.coordinates.length}`);
                logResult(`UUID: ${result.uuid}`);

            } catch (error) {
                logResult(`❌ 无途经点算路失败: ${error.message}`, 'error');
            }
        }

        // 测试含途经点算路
        async function testWithWaypoints() {
            try {
                if (waypoints.length === 0) {
                    logResult('❌ 请先添加途经点', 'error');
                    return;
                }

                const strategy = parseInt(document.getElementById('testStrategy').value);
                const strategyNames = ['最短时间优先', '最短距离优先', '高速优先', '避开收费路段'];

                logResult(`开始测试含${waypoints.length}个途经点的算路 (策略: ${strategyNames[strategy]})...`);

                const startLng = parseFloat(document.getElementById('startLng').value);
                const startLat = parseFloat(document.getElementById('startLat').value);
                const endLng = parseFloat(document.getElementById('endLng').value);
                const endLat = parseFloat(document.getElementById('endLat').value);

                logResult(`起点: (${startLng}, ${startLat})`);
                waypoints.forEach((wp, index) => {
                    logResult(`途经点${index + 1}: (${wp.lng}, ${wp.lat})`);
                });
                logResult(`终点: (${endLng}, ${endLat})`);

                const startTime = Date.now();
                const result = await cppClient.calculateRoute(startLng, startLat, endLng, endLat, waypoints, strategy);
                const endTime = Date.now();

                logResult(`✅ 含途经点算路成功 (耗时: ${endTime - startTime}ms)`);
                logResult(`策略: ${strategyNames[strategy]}`);
                logResult(`路径长度: ${result.totalDistance.toFixed(2)}米`);
                logResult(`预计时间: ${result.totalTime.toFixed(2)}秒`);
                logResult(`坐标点数: ${result.coordinates.length}`);
                logResult(`UUID: ${result.uuid}`);

            } catch (error) {
                logResult(`❌ 含途经点算路失败: ${error.message}`, 'error');
            }
        }

        // 测试所有策略
        async function testAllStrategies() {
            try {
                logResult('开始测试所有算路策略...');

                const startLng = parseFloat(document.getElementById('startLng').value);
                const startLat = parseFloat(document.getElementById('startLat').value);
                const endLng = parseFloat(document.getElementById('endLng').value);
                const endLat = parseFloat(document.getElementById('endLat').value);

                const strategyNames = ['最短时间优先', '最短距离优先', '高速优先', '避开收费路段'];
                const results = [];

                for (let strategy = 0; strategy < 4; strategy++) {
                    try {
                        logResult(`\n--- 测试策略 ${strategy}: ${strategyNames[strategy]} ---`);

                        const startTime = Date.now();
                        const result = await cppClient.calculateRoute(startLng, startLat, endLng, endLat, waypoints, strategy);
                        const endTime = Date.now();

                        const strategyResult = {
                            strategy: strategy,
                            name: strategyNames[strategy],
                            success: true,
                            time: endTime - startTime,
                            distance: result.totalDistance,
                            duration: result.totalTime,
                            points: result.coordinates.length,
                            uuid: result.uuid
                        };

                        results.push(strategyResult);

                        logResult(`✅ ${strategyNames[strategy]} 成功`);
                        logResult(`   耗时: ${strategyResult.time}ms`);
                        logResult(`   路径长度: ${strategyResult.distance.toFixed(2)}米`);
                        logResult(`   预计时间: ${strategyResult.duration.toFixed(2)}秒`);
                        logResult(`   坐标点数: ${strategyResult.points}`);

                    } catch (error) {
                        logResult(`❌ ${strategyNames[strategy]} 失败: ${error.message}`, 'error');
                        results.push({
                            strategy: strategy,
                            name: strategyNames[strategy],
                            success: false,
                            error: error.message
                        });
                    }

                    // 策略间间隔
                    await new Promise(resolve => setTimeout(resolve, 500));
                }

                // 输出对比结果
                logResult('\n=== 策略对比结果 ===');
                const successResults = results.filter(r => r.success);
                if (successResults.length > 0) {
                    successResults.forEach(r => {
                        logResult(`${r.name}: ${r.distance.toFixed(2)}m, ${r.duration.toFixed(2)}s, ${r.time}ms`);
                    });

                    // 找出最短距离和最短时间
                    const shortestDistance = Math.min(...successResults.map(r => r.distance));
                    const shortestTime = Math.min(...successResults.map(r => r.duration));

                    logResult(`\n最短距离: ${shortestDistance.toFixed(2)}m`);
                    logResult(`最短时间: ${shortestTime.toFixed(2)}s`);
                }

            } catch (error) {
                logResult(`❌ 策略测试失败: ${error.message}`, 'error');
            }
        }

        // 检查服务器状态
        async function checkServerStatus() {
            try {
                logResult('检查C++服务器状态...');
                const isAvailable = await cppClient.checkServerStatus();

                if (isAvailable) {
                    logResult('✅ C++服务器状态正常');
                } else {
                    logResult('❌ C++服务器不可用', 'error');
                }

            } catch (error) {
                logResult(`❌ 服务器状态检查失败: ${error.message}`, 'error');
            }
        }

        // 清空结果
        function clearResults() {
            document.getElementById('testResults').innerHTML = '结果已清空';
            document.getElementById('testResults').className = 'result';
        }

        // 记录结果
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const newMessage = `[${timestamp}] ${message}\n`;
            
            if (resultsDiv.innerHTML === '等待测试...' || resultsDiv.innerHTML === '结果已清空') {
                resultsDiv.innerHTML = newMessage;
            } else {
                resultsDiv.innerHTML += newMessage;
            }
            
            resultsDiv.className = `result ${type}`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            logResult('C++算路途经点功能测试页面已加载');
            logResult('服务器地址: http://localhost:8080');
            checkServerStatus();
        });
    </script>
</body>
</html>
