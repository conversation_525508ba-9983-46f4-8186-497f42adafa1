<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0958d9;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        pre {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 Leaflet + 高德地图应用功能测试</h1>
    
    <div class="test-container">
        <h2>📍 坐标转换测试</h2>
        <button onclick="testCoordinateConversion()">运行坐标转换测试</button>
        <div id="coordTest" class="test-result info">点击按钮开始测试</div>
    </div>
    
    <div class="test-container">
        <h2>🗺️ 地图配置测试</h2>
        <button onclick="testMapConfig()">运行地图配置测试</button>
        <div id="mapConfigTest" class="test-result info">点击按钮开始测试</div>
    </div>
    
    <div class="test-container">
        <h2>🛣️ 路径规划测试</h2>
        <button onclick="testRoutePlanner()">运行路径规划测试</button>
        <div id="routeTest" class="test-result info">点击按钮开始测试</div>
    </div>
    
    <div class="test-container">
        <h2>🌐 API连接测试</h2>
        <button onclick="testAPIConnection()">测试高德API连接</button>
        <div id="apiTest" class="test-result info">点击按钮开始测试</div>
    </div>
    
    <div class="test-container">
        <h2>📊 综合测试报告</h2>
        <button onclick="runAllTests()">运行所有测试</button>
        <div id="allTests" class="test-result info">点击按钮开始综合测试</div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=f7f401fd4ffb75720cba09ffb7b24a4c&plugin=AMap.Driving,AMap.Walking,AMap.Riding"></script>
    <script src="coordinate-converter.js"></script>
    <script src="map-config.js"></script>
    <script src="route-planner.js"></script>
    
    <script>
        // 测试结果记录
        let testResults = [];
        
        /**
         * 显示测试结果
         */
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `test-result ${type}`;
            element.innerHTML = message;
        }
        
        /**
         * 坐标转换测试
         */
        function testCoordinateConversion() {
            showResult('coordTest', '正在测试坐标转换...', 'info');
            
            try {
                // 测试数据：北京天安门
                const testLng = 116.4074;
                const testLat = 39.9042;
                
                // WGS84 转 GCJ02
                const gcj02 = CoordinateConverter.wgs84ToGcj02(testLng, testLat);
                
                // GCJ02 转 WGS84
                const wgs84 = CoordinateConverter.gcj02ToWgs84(gcj02[0], gcj02[1]);
                
                // GCJ02 转 BD09
                const bd09 = CoordinateConverter.gcj02ToBd09(gcj02[0], gcj02[1]);
                
                // BD09 转 GCJ02
                const gcj02_back = CoordinateConverter.bd09ToGcj02(bd09[0], bd09[1]);
                
                // 计算距离
                const distance = CoordinateConverter.getDistance(testLng, testLat, gcj02[0], gcj02[1]);
                
                const result = `
                    <h4>✅ 坐标转换测试通过</h4>
                    <pre>
原始坐标 (WGS84): ${testLat.toFixed(6)}, ${testLng.toFixed(6)}
转换结果 (GCJ02): ${gcj02[1].toFixed(6)}, ${gcj02[0].toFixed(6)}
逆向转换 (WGS84): ${wgs84[1].toFixed(6)}, ${wgs84[0].toFixed(6)}
百度坐标 (BD09):  ${bd09[1].toFixed(6)}, ${bd09[0].toFixed(6)}
逆向转换 (GCJ02): ${gcj02_back[1].toFixed(6)}, ${gcj02_back[0].toFixed(6)}
坐标偏移距离: ${distance.toFixed(2)} 米
                    </pre>
                `;
                
                showResult('coordTest', result, 'success');
                testResults.push({name: '坐标转换', status: 'success'});
                
            } catch (error) {
                showResult('coordTest', `❌ 坐标转换测试失败: ${error.message}`, 'error');
                testResults.push({name: '坐标转换', status: 'error', error: error.message});
            }
        }
        
        /**
         * 地图配置测试
         */
        function testMapConfig() {
            showResult('mapConfigTest', '正在测试地图配置...', 'info');
            
            try {
                // 检查配置对象
                if (typeof MapConfig === 'undefined') {
                    throw new Error('MapConfig 未定义');
                }
                
                // 检查必要的配置项
                const requiredConfigs = ['AMAP_CONFIG', 'MAP_LAYERS', 'MAP_STYLES'];
                const missingConfigs = requiredConfigs.filter(config => !MapConfig[config]);
                
                if (missingConfigs.length > 0) {
                    throw new Error(`缺少配置项: ${missingConfigs.join(', ')}`);
                }
                
                // 检查图层URL
                const layers = MapConfig.MAP_LAYERS;
                const layerTests = [];
                
                Object.keys(layers).forEach(layerName => {
                    const layer = layers[layerName];
                    if (layer.url) {
                        layerTests.push(`${layerName}: ${layer.url.substring(0, 50)}...`);
                    }
                });
                
                const result = `
                    <h4>✅ 地图配置测试通过</h4>
                    <pre>
高德API密钥: ${MapConfig.AMAP_CONFIG.key.substring(0, 10)}...
默认中心点: ${MapConfig.MAP_STYLES.default.center.join(', ')}
默认缩放级别: ${MapConfig.MAP_STYLES.default.zoom}
可用图层:
${layerTests.map(test => '  - ' + test).join('\n')}
                    </pre>
                `;
                
                showResult('mapConfigTest', result, 'success');
                testResults.push({name: '地图配置', status: 'success'});
                
            } catch (error) {
                showResult('mapConfigTest', `❌ 地图配置测试失败: ${error.message}`, 'error');
                testResults.push({name: '地图配置', status: 'error', error: error.message});
            }
        }
        
        /**
         * 路径规划测试
         */
        async function testRoutePlanner() {
            showResult('routeTest', '正在测试路径规划...', 'info');
            
            try {
                // 创建路径规划器实例
                const planner = new RoutePlanner();
                
                // 等待初始化
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 测试格式化函数
                const distanceTest = planner.formatDistance(1500);
                const timeTest = planner.formatTime(3600);
                
                const result = `
                    <h4>✅ 路径规划测试通过</h4>
                    <pre>
路径规划器已创建: ${planner.constructor.name}
当前路径类型: ${planner.routeType}
距离格式化测试: ${distanceTest}
时间格式化测试: ${timeTest}
支持的路径类型: 驾车, 步行, 骑行
                    </pre>
                    <p><small>注意: 实际路径计算需要在地图上进行点击操作</small></p>
                `;
                
                showResult('routeTest', result, 'success');
                testResults.push({name: '路径规划', status: 'success'});
                
            } catch (error) {
                showResult('routeTest', `❌ 路径规划测试失败: ${error.message}`, 'error');
                testResults.push({name: '路径规划', status: 'error', error: error.message});
            }
        }
        
        /**
         * API连接测试
         */
        function testAPIConnection() {
            showResult('apiTest', '正在测试API连接...', 'info');
            
            try {
                // 检查高德API是否加载
                if (typeof AMap === 'undefined') {
                    throw new Error('高德地图API未加载');
                }
                
                // 检查API版本和功能
                const apiInfo = {
                    version: AMap.version || '未知',
                    plugins: []
                };
                
                // 检查常用插件
                const commonPlugins = ['Driving', 'Walking', 'Riding', 'Geocoder'];
                commonPlugins.forEach(plugin => {
                    if (AMap[plugin]) {
                        apiInfo.plugins.push(plugin);
                    }
                });
                
                const result = `
                    <h4>✅ API连接测试通过</h4>
                    <pre>
高德地图API版本: ${apiInfo.version}
可用插件: ${apiInfo.plugins.join(', ')}
API对象类型: ${typeof AMap}
地图构造函数: ${typeof AMap.Map}
                    </pre>
                `;
                
                showResult('apiTest', result, 'success');
                testResults.push({name: 'API连接', status: 'success'});
                
            } catch (error) {
                showResult('apiTest', `❌ API连接测试失败: ${error.message}`, 'error');
                testResults.push({name: 'API连接', status: 'error', error: error.message});
            }
        }
        
        /**
         * 运行所有测试
         */
        async function runAllTests() {
            showResult('allTests', '正在运行综合测试...', 'info');
            
            testResults = [];
            
            // 依次运行所有测试
            testCoordinateConversion();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testMapConfig();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testRoutePlanner();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testAPIConnection();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 生成综合报告
            const successCount = testResults.filter(r => r.status === 'success').length;
            const totalCount = testResults.length;
            const successRate = ((successCount / totalCount) * 100).toFixed(1);
            
            let reportHtml = `
                <h4>📊 综合测试报告</h4>
                <p><strong>测试通过率: ${successRate}% (${successCount}/${totalCount})</strong></p>
                <ul>
            `;
            
            testResults.forEach(result => {
                const icon = result.status === 'success' ? '✅' : '❌';
                reportHtml += `<li>${icon} ${result.name}: ${result.status}`;
                if (result.error) {
                    reportHtml += ` (${result.error})`;
                }
                reportHtml += '</li>';
            });
            
            reportHtml += '</ul>';
            
            if (successCount === totalCount) {
                reportHtml += '<p style="color: #389e0d;"><strong>🎉 所有测试通过！应用可以正常使用。</strong></p>';
                showResult('allTests', reportHtml, 'success');
            } else {
                reportHtml += '<p style="color: #cf1322;"><strong>⚠️ 部分测试失败，请检查相关功能。</strong></p>';
                showResult('allTests', reportHtml, 'error');
            }
        }
    </script>
</body>
</html>
