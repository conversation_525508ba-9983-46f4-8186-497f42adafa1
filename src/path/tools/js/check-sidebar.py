#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的侧边栏结构检查脚本
"""

def check_sidebar_structure():
    """检查侧边栏结构"""
    try:
        with open('index.html', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("🔍 检查侧边栏结构")
        print("=" * 50)
        
        in_sidebar = False
        sidebar_start = 0
        sidebar_end = 0
        control_groups = []
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # 检查侧边栏开始
            if 'id="sidebar"' in line_stripped:
                in_sidebar = True
                sidebar_start = i
                print(f"📍 侧边栏开始: 第{i}行")
                continue
            
            # 如果在侧边栏内
            if in_sidebar:
                # 检查控制组
                if '<h3>' in line_stripped and '</h3>' in line_stripped:
                    # 提取h3标签内容
                    import re
                    h3_match = re.search(r'<h3>(.*?)</h3>', line_stripped)
                    if h3_match:
                        group_name = h3_match.group(1)
                        control_groups.append((group_name, i))
                        print(f"✅ 控制组: {group_name} (第{i}行)")
                
                # 检查侧边栏结束
                if line_stripped == '</div>' and i > sidebar_start + 10:
                    # 检查下一行是否是容器结束
                    if i < len(lines) and lines[i].strip() == '</div>':
                        sidebar_end = i
                        print(f"📍 侧边栏结束: 第{i}行")
                        break
        
        print(f"\n📊 统计结果:")
        print(f"侧边栏范围: 第{sidebar_start}行 - 第{sidebar_end}行")
        print(f"控制组数量: {len(control_groups)}")
        
        print(f"\n📋 控制组列表:")
        for group_name, line_num in control_groups:
            print(f"  {group_name} (第{line_num}行)")
        
        # 检查重要元素是否在侧边栏内
        print(f"\n🔍 检查重要元素:")
        important_elements = [
            'id="coordinates"',
            'id="routeInfo"', 
            'id="status"',
            'id="poiSearchInput"',
            'id="geocodeInput"',
            'id="startLng"',
            'id="coordinateHistory"'
        ]
        
        for element in important_elements:
            found_line = 0
            for i, line in enumerate(lines[sidebar_start-1:sidebar_end], sidebar_start):
                if element in line:
                    found_line = i
                    break
            
            if found_line > 0:
                print(f"✅ {element} (第{found_line}行)")
            else:
                print(f"❌ {element} 未找到")
        
        print(f"\n🎉 侧边栏结构检查完成!")
        
        if len(control_groups) >= 7:  # 至少应该有7个控制组
            print("✅ 侧边栏结构正常")
            return True
        else:
            print("❌ 侧边栏结构可能有问题")
            return False
            
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    check_sidebar_structure()
