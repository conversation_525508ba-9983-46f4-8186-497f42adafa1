<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leaflet + 高德地图 路径规划</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- 高德地图 JS SDK -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=f7f401fd4ffb75720cba09ffb7b24a4c&plugin=AMap.Driving,AMap.Walking,AMap.Riding,AMap.PlaceSearch,AMap.AutoComplete,AMap.Geocoder"></script>
    
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        #container {
            display: flex;
            height: 100vh;
        }
        
        #map {
            flex: 1;
            height: 100vh;
        }
        
        #sidebar {
            width: 300px;
            background: #f5f5f5;
            padding: 20px;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            overflow-y: auto;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px 5px 5px 0;
        }
        
        .btn:hover {
            background: #40a9ff;
        }
        
        .btn-danger {
            background: #ff4d4f;
        }
        
        .btn-danger:hover {
            background: #ff7875;
        }
        
        .coordinate-display {
            background: white;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .route-info {
            background: white;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        
        .status.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
        
        .status.info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0958d9;
        }

        /* 右键菜单样式 */
        .context-menu {
            position: absolute;
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 4px 0;
            min-width: 120px;
            z-index: 1000;
            display: none;
        }

        .context-menu-item {
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
        }

        .context-menu-item:hover {
            background: #f5f5f5;
        }

        .context-menu-item:disabled {
            color: #ccc;
            cursor: not-allowed;
        }

        .context-menu-item:disabled:hover {
            background: none;
        }

        .context-menu-divider {
            height: 1px;
            background: #e8e8e8;
            margin: 4px 0;
        }

        /* 搜索相关样式 */
        .search-container {
            position: relative;
            margin-bottom: 15px;
        }

        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .search-input:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d9d9d9;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .search-suggestion-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }

        .search-suggestion-item:hover {
            background: #f5f5f5;
        }

        .search-suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-name {
            font-weight: 500;
            color: #333;
        }

        .suggestion-address {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .search-results {
            background: white;
            border-radius: 4px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
        }

        .search-result-item {
            padding: 10px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
        }

        .search-result-item:hover {
            background: #f5f5f5;
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        .result-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .result-address {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .result-distance {
            font-size: 11px;
            color: #999;
        }

        .search-actions {
            display: flex;
            gap: 5px;
            margin-top: 8px;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 3px;
        }

        .btn-success {
            background: #52c41a;
        }

        .btn-success:hover {
            background: #73d13d;
        }

        .btn-warning {
            background: #faad14;
        }

        .btn-warning:hover {
            background: #ffc53d;
        }

        /* 手动坐标输入样式 */
        .coordinate-input-section {
            background: #fafafa;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #e8e8e8;
        }

        .coordinate-input-row {
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
        }

        .coordinate-input {
            flex: 1;
            padding: 6px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 3px;
            font-size: 12px;
            text-align: center;
        }

        .coordinate-input:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .coordinate-actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .coordinate-help {
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid #e8e8e8;
        }

        /* 算路策略选择样式 */
        .strategy-selection {
            margin: 8px 0;
        }

        .strategy-select {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 3px;
            font-size: 12px;
            background: white;
            cursor: pointer;
        }

        .strategy-select:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .strategy-help {
            margin-top: 5px;
        }

        .city-buttons {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
            margin-bottom: 8px;
        }

        .btn-city {
            padding: 3px 8px;
            font-size: 11px;
            background: #f0f0f0;
            border: 1px solid #d9d9d9;
            border-radius: 3px;
            cursor: pointer;
            color: #666;
        }

        .btn-city:hover {
            background: #e6f7ff;
            border-color: #91d5ff;
            color: #1890ff;
        }

        /* 坐标管理样式 */
        .coordinate-management {
            background: #fafafa;
            border-radius: 6px;
            border: 1px solid #e8e8e8;
            overflow: hidden;
        }

        .management-tabs {
            display: flex;
            background: #f0f0f0;
            border-bottom: 1px solid #e8e8e8;
        }

        .tab-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 12px;
            color: #666;
            border-bottom: 2px solid transparent;
        }

        .tab-btn.active {
            background: white;
            color: #1890ff;
            border-bottom-color: #1890ff;
        }

        .tab-btn:hover:not(.active) {
            background: #e6f7ff;
            color: #1890ff;
        }

        .tab-content {
            display: none;
            padding: 12px;
        }

        .tab-content.active {
            display: block;
        }

        .tab-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .coordinate-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .coordinate-item {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 6px;
            cursor: pointer;
        }

        .coordinate-item:hover {
            border-color: #1890ff;
            box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
        }

        .coordinate-item-name {
            font-weight: 500;
            color: #333;
            font-size: 12px;
            margin-bottom: 2px;
        }

        .coordinate-item-coords {
            font-family: monospace;
            font-size: 11px;
            color: #666;
            margin-bottom: 2px;
        }

        .coordinate-item-note {
            font-size: 10px;
            color: #999;
        }

        .coordinate-item-actions {
            display: flex;
            gap: 4px;
            margin-top: 4px;
        }

        .coordinate-item-actions button {
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 2px;
        }

        .empty-state {
            text-align: center;
            color: #999;
            font-size: 12px;
            padding: 20px;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #e8e8e8;
        }

        .modal-header h3 {
            margin: 0;
            color: #333;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            color: #333;
        }

        .modal-body {
            padding: 20px;
        }

        /* 城市网格样式 */
        .city-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }

        .city-item {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 12px;
            cursor: pointer;
            text-align: left;
            transition: all 0.2s;
        }

        .city-item:hover {
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
            transform: translateY(-1px);
        }

        .city-name {
            font-weight: 500;
            color: #333;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .city-coords {
            font-family: monospace;
            font-size: 11px;
            color: #666;
            margin-bottom: 2px;
        }

        .city-landmark {
            font-size: 10px;
            color: #999;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
            font-size: 12px;
        }

        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .form-group input:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .form-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="map"></div>
        <div id="sidebar">
            <h2>地图控制面板</h2>
            
            <div class="control-group">
                <h3>🔍 POI搜索</h3>
                <div class="search-container">
                    <input type="text" id="poiSearchInput" class="search-input" placeholder="搜索地点、商家、地址..." autocomplete="off">
                    <div id="searchSuggestions" class="search-suggestions"></div>
                </div>
                <button class="btn" onclick="searchNearby()">附近搜索</button>
                <button class="btn" onclick="clearSearchResults()">清除结果</button>
                <div id="searchResults" class="search-results"></div>
            </div>

            <div class="control-group">
                <h3>📍 地理编码</h3>
                <div class="search-container">
                    <input type="text" id="geocodeInput" class="search-input" placeholder="输入地址进行地理编码..." autocomplete="off">
                </div>
                <button class="btn" onclick="geocodeAddress()">地址转坐标</button>
                <button class="btn" onclick="reverseGeocode()">坐标转地址</button>
                <div id="geocodeResult" class="coordinate-display">
                    输入地址或点击地图获取地理编码信息
                </div>
            </div>

            <div class="control-group">
                <h3>📐 手动坐标输入</h3>
                <div class="coordinate-input-section">
                    <h4 style="margin: 10px 0 5px 0; font-size: 14px; color: #333;">🟢 起点坐标</h4>
                    <div class="coordinate-input-row">
                        <input type="number" id="startLng" class="coordinate-input" placeholder="经度" step="0.000001" min="-180" max="180">
                        <input type="number" id="startLat" class="coordinate-input" placeholder="纬度" step="0.000001" min="-90" max="90">
                    </div>
                    <div class="coordinate-actions">
                        <button class="btn btn-small btn-success" onclick="setStartPointFromInput()">设置起点</button>
                        <button class="btn btn-small" onclick="getCurrentLocation('start')">获取当前位置</button>
                    </div>

                    <h4 style="margin: 15px 0 5px 0; font-size: 14px; color: #333;">🔴 终点坐标</h4>
                    <div class="coordinate-input-row">
                        <input type="number" id="endLng" class="coordinate-input" placeholder="经度" step="0.000001" min="-180" max="180">
                        <input type="number" id="endLat" class="coordinate-input" placeholder="纬度" step="0.000001" min="-90" max="90">
                    </div>
                    <div class="coordinate-actions">
                        <button class="btn btn-small btn-warning" onclick="setEndPointFromInput()">设置终点</button>
                        <button class="btn btn-small" onclick="getCurrentLocation('end')">获取当前位置</button>
                    </div>

                    <h4 style="margin: 15px 0 5px 0; font-size: 14px; color: #333;">⚙️ 算路策略</h4>
                    <div class="strategy-selection">
                        <select id="routeStrategy" class="strategy-select">
                            <option value="0">⏱️ 最短时间优先</option>
                            <option value="1">📏 最短距离优先</option>
                            <option value="2">🛣️ 高速优先</option>
                            <option value="3">💰 避开收费路段</option>
                        </select>
                        <div class="strategy-help">
                            <p style="font-size: 11px; color: #999; margin: 5px 0 0 0;">
                                💡 策略仅适用于C++算路引擎
                            </p>
                        </div>
                    </div>

                    <div class="coordinate-actions" style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #e8e8e8;">
                        <button class="btn" onclick="calculateRouteFromCoords()">🛣️ 开始算路</button>
                        <button class="btn" onclick="clearCoordinateInputs()">清空输入</button>
                    </div>

                    <div class="coordinate-help">
                        <p style="font-size: 11px; color: #666; margin: 8px 0 5px 0; font-weight: 500;">
                            🏙️ 快速填充城市坐标:
                        </p>
                        <div class="city-buttons">
                            <button class="btn-city" onclick="showCitySelector('start')">填充起点</button>
                            <button class="btn-city" onclick="showCitySelector('end')">填充终点</button>
                        </div>
                        <p style="font-size: 11px; color: #999; margin: 8px 0 0 0;">
                            💡 支持WGS84坐标系 (GPS标准)<br>
                            📍 格式: 经度(-180~180), 纬度(-90~90)
                        </p>
                    </div>
                </div>
            </div>

            <div class="control-group">
                <h3>📚 坐标管理</h3>
                <div class="coordinate-management">
                    <div class="management-tabs">
                        <button class="tab-btn active" onclick="switchTab('history')">📜 历史记录</button>
                        <button class="tab-btn" onclick="switchTab('favorites')">⭐ 收藏夹</button>
                    </div>

                    <div id="historyTab" class="tab-content active">
                        <div class="tab-header">
                            <span style="font-size: 12px; color: #666;">最近使用的坐标</span>
                            <button class="btn-small" onclick="clearHistory()">清空历史</button>
                        </div>
                        <div id="coordinateHistory" class="coordinate-list">
                            <div class="empty-state">暂无历史记录</div>
                        </div>
                    </div>

                    <div id="favoritesTab" class="tab-content">
                        <div class="tab-header">
                            <span style="font-size: 12px; color: #666;">收藏的坐标点</span>
                            <button class="btn-small" onclick="showAddFavoriteDialog()">添加收藏</button>
                        </div>
                        <div id="coordinateFavorites" class="coordinate-list">
                            <div class="empty-state">暂无收藏坐标</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="control-group">
                <h3>地图图层</h3>
                <button class="btn" onclick="switchToSatellite()">卫星图</button>
                <button class="btn" onclick="switchToStreet()">街道图</button>
                <button class="btn" onclick="toggleWMS()">切换WMS</button>
            </div>
            
            <div class="control-group">
                <h3>🔄 双算路对比</h3>
                <button class="btn" onclick="startRouting(event)">开始规划</button>
                <button class="btn btn-danger" onclick="clearRoute()">清除路径</button>
                <button class="btn" onclick="clearMarkers()">清除标记</button>
                <p style="font-size: 12px; color: #666; margin-top: 10px;">
                    🚀 <strong>双算路对比功能:</strong><br>
                    • 同时使用高德地图和Path算路引擎<br>
                    • 红色虚线: 高德地图路径<br>
                    • 绿色虚线: Path算路路径<br>
                    • 自动显示距离、时间差异对比<br><br>
                    💡 <strong>使用方法:</strong><br>
                    • 右键设置起终点 → 右键"开始算路"<br>
                    • 右键设置起终点 → 点击"开始规划"<br>
                    • 点击"开始规划" → 左键点击起终点
                </p>
            </div>
            
            <div class="control-group">
                <h3>路径点信息</h3>
                <div id="coordinates" class="coordinate-display">
                    右键点击地图设置路径点
                </div>
            </div>
            
            <div class="control-group">
                <h3>路径信息</h3>
                <div id="routeInfo" class="route-info">
                    暂无路径信息
                </div>
            </div>
            
            <div id="status" class="status info">
                地图已加载，点击地图开始使用
            </div>
        </div>
    </div>

    <!-- 右键菜单 -->
    <div id="contextMenu" class="context-menu">
        <button class="context-menu-item" onclick="setStartPoint()">🟢 设为起点</button>
        <button class="context-menu-item" onclick="addWaypoint()">🔵 添加途经点</button>
        <button class="context-menu-item" onclick="setEndPoint()">🔴 设为终点</button>
        <div class="context-menu-divider"></div>
        <button class="context-menu-item" onclick="searchAroundPoint()">🔍 搜索周边</button>
        <button class="context-menu-item" onclick="reverseGeocodePoint()">📍 获取地址</button>
        <div class="context-menu-divider"></div>
        <button class="context-menu-item" onclick="calculateRoute()">🛣️ 开始算路</button>
        <button class="context-menu-item" onclick="clearAllPoints()">🗑️ 清除所有点</button>
    </div>

    <!-- 城市选择器 -->
    <div id="citySelector" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="citySelectorTitle">选择城市坐标</h3>
                <button class="modal-close" onclick="hideCitySelector()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="city-grid">
                    <button class="city-item" onclick="fillCityCoordinate('beijing')">
                        <div class="city-name">北京</div>
                        <div class="city-coords">116.4074, 39.9042</div>
                        <div class="city-landmark">天安门广场</div>
                    </button>
                    <button class="city-item" onclick="fillCityCoordinate('shanghai')">
                        <div class="city-name">上海</div>
                        <div class="city-coords">121.4737, 31.2304</div>
                        <div class="city-landmark">人民广场</div>
                    </button>
                    <button class="city-item" onclick="fillCityCoordinate('guangzhou')">
                        <div class="city-name">广州</div>
                        <div class="city-coords">113.2644, 23.1291</div>
                        <div class="city-landmark">天河城</div>
                    </button>
                    <button class="city-item" onclick="fillCityCoordinate('shenzhen')">
                        <div class="city-name">深圳</div>
                        <div class="city-coords">114.0579, 22.5431</div>
                        <div class="city-landmark">市民中心</div>
                    </button>
                    <button class="city-item" onclick="fillCityCoordinate('hangzhou')">
                        <div class="city-name">杭州</div>
                        <div class="city-coords">120.1551, 30.2741</div>
                        <div class="city-landmark">西湖</div>
                    </button>
                    <button class="city-item" onclick="fillCityCoordinate('nanjing')">
                        <div class="city-name">南京</div>
                        <div class="city-coords">118.7969, 32.0603</div>
                        <div class="city-landmark">夫子庙</div>
                    </button>
                    <button class="city-item" onclick="fillCityCoordinate('chengdu')">
                        <div class="city-name">成都</div>
                        <div class="city-coords">104.0665, 30.5723</div>
                        <div class="city-landmark">天府广场</div>
                    </button>
                    <button class="city-item" onclick="fillCityCoordinate('wuhan')">
                        <div class="city-name">武汉</div>
                        <div class="city-coords">114.3054, 30.5931</div>
                        <div class="city-landmark">黄鹤楼</div>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加收藏对话框 -->
    <div id="addFavoriteDialog" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加收藏坐标</h3>
                <button class="modal-close" onclick="hideAddFavoriteDialog()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>坐标名称:</label>
                    <input type="text" id="favoriteName" placeholder="例如: 公司、家、常去的地方" maxlength="20">
                </div>
                <div class="form-group">
                    <label>经度:</label>
                    <input type="number" id="favoriteLng" placeholder="经度" step="0.000001" min="-180" max="180">
                </div>
                <div class="form-group">
                    <label>纬度:</label>
                    <input type="number" id="favoriteLat" placeholder="纬度" step="0.000001" min="-90" max="90">
                </div>
                <div class="form-group">
                    <label>备注 (可选):</label>
                    <input type="text" id="favoriteNote" placeholder="地址或其他说明" maxlength="50">
                </div>
                <div class="form-actions">
                    <button class="btn" onclick="addToFavorites()">添加收藏</button>
                    <button class="btn" onclick="getCurrentLocationForFavorite()">使用当前位置</button>
                    <button class="btn" onclick="hideAddFavoriteDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="map-config.js"></script>
    <script src="coordinate-converter.js"></script>
    <script src="route-planner.js"></script>
    <script src="cpp-route-client.js"></script>
    <script src="poi-search.js"></script>
    <script src="geocoding.js"></script>
    <script src="coordinate-manager.js"></script>
    <script src="main.js"></script>
</body>
</html>
