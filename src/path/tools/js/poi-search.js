/**
 * POI搜索功能
 * 基于高德地图JS API 2.0的PlaceSearch和AutoComplete插件
 */

class POISearch {
    constructor() {
        this.placeSearch = null;
        this.autoComplete = null;
        this.searchMarkers = [];
        this.currentSearchResults = [];
        this.isInitialized = false;
        
        this.init();
    }

    /**
     * 初始化POI搜索服务
     */
    async init() {
        try {
            // 等待高德地图API加载完成
            if (typeof AMap === 'undefined') {
                await this.waitForAMap();
            }

            // 初始化PlaceSearch
            this.placeSearch = new AMap.PlaceSearch({
                pageSize: 10,
                pageIndex: 1,
                city: '全国',
                citylimit: false,
                map: null,
                panel: null
            });

            // 初始化AutoComplete
            this.autoComplete = new AMap.AutoComplete({
                city: '全国',
                citylimit: false
            });

            this.isInitialized = true;
            console.log('POI搜索服务初始化完成');
            
            // 绑定搜索输入框事件
            this.bindSearchEvents();
            
        } catch (error) {
            console.error('POI搜索服务初始化失败:', error);
        }
    }

    /**
     * 等待高德地图API加载
     */
    waitForAMap() {
        return new Promise((resolve) => {
            const checkAMap = () => {
                if (typeof AMap !== 'undefined') {
                    resolve();
                } else {
                    setTimeout(checkAMap, 100);
                }
            };
            checkAMap();
        });
    }

    /**
     * 绑定搜索相关事件
     */
    bindSearchEvents() {
        const searchInput = document.getElementById('poiSearchInput');
        const suggestionsDiv = document.getElementById('searchSuggestions');

        if (!searchInput) return;

        // 输入事件 - 自动补全
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            const keyword = e.target.value.trim();
            
            if (keyword.length < 2) {
                this.hideSuggestions();
                return;
            }

            searchTimeout = setTimeout(() => {
                this.getAutoComplete(keyword);
            }, 300);
        });

        // 回车搜索
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                const keyword = e.target.value.trim();
                if (keyword) {
                    this.searchPOI(keyword);
                    this.hideSuggestions();
                }
            }
        });

        // 失去焦点隐藏建议
        searchInput.addEventListener('blur', () => {
            setTimeout(() => this.hideSuggestions(), 200);
        });
    }

    /**
     * 获取自动补全建议
     */
    getAutoComplete(keyword) {
        if (!this.isInitialized) return;

        this.autoComplete.search(keyword, (status, result) => {
            if (status === 'complete' && result.tips) {
                this.showSuggestions(result.tips);
            } else {
                this.hideSuggestions();
            }
        });
    }

    /**
     * 显示搜索建议
     */
    showSuggestions(tips) {
        const suggestionsDiv = document.getElementById('searchSuggestions');
        if (!suggestionsDiv) return;

        suggestionsDiv.innerHTML = '';
        
        tips.slice(0, 8).forEach(tip => {
            const item = document.createElement('div');
            item.className = 'search-suggestion-item';
            item.innerHTML = `
                <div class="suggestion-name">${tip.name}</div>
                <div class="suggestion-address">${tip.district || ''}${tip.address || ''}</div>
            `;
            
            item.addEventListener('click', () => {
                document.getElementById('poiSearchInput').value = tip.name;
                this.searchPOI(tip.name);
                this.hideSuggestions();
            });
            
            suggestionsDiv.appendChild(item);
        });

        suggestionsDiv.style.display = 'block';
    }

    /**
     * 隐藏搜索建议
     */
    hideSuggestions() {
        const suggestionsDiv = document.getElementById('searchSuggestions');
        if (suggestionsDiv) {
            suggestionsDiv.style.display = 'none';
        }
    }

    /**
     * 搜索POI
     */
    searchPOI(keyword, center = null) {
        if (!this.isInitialized) {
            console.error('POI搜索服务未初始化');
            return;
        }

        // 设置搜索中心点
        if (center) {
            // center参数已经是GCJ02坐标，直接使用
            this.placeSearch.setCity(center);
        } else if (map) {
            // Leaflet地图中心是WGS84坐标，需要转换为GCJ02
            const mapCenter = map.getCenter();
            const gcj02Center = CoordinateConverter.gcj02Togcj02(mapCenter.lng, mapCenter.lat);
            this.placeSearch.setCity(gcj02Center);
        }

        // 执行搜索
        this.placeSearch.search(keyword, (status, result) => {
            if (status === 'complete' && result.poiList) {
                this.displaySearchResults(result.poiList.pois);
                this.showSearchMarkersOnMap(result.poiList.pois);
                updateStatus(`找到 ${result.poiList.pois.length} 个相关地点`, 'success');
            } else {
                this.displaySearchResults([]);
                updateStatus('未找到相关地点', 'info');
            }
        });
    }

    /**
     * 显示搜索结果
     */
    displaySearchResults(pois) {
        const resultsDiv = document.getElementById('searchResults');
        if (!resultsDiv) return;

        this.currentSearchResults = pois;
        resultsDiv.innerHTML = '';

        if (pois.length === 0) {
            resultsDiv.innerHTML = '<div style="padding: 10px; text-align: center; color: #999;">暂无搜索结果</div>';
            return;
        }

        pois.forEach((poi, index) => {
            const item = document.createElement('div');
            item.className = 'search-result-item';
            
            // 计算距离（如果有地图中心）
            let distanceText = '';
            if (map) {
                const mapCenter = map.getCenter();
                const distance = this.calculateDistance(
                    mapCenter.lat, mapCenter.lng,
                    poi.location.lat, poi.location.lng
                );
                distanceText = `<div class="result-distance">距离: ${this.formatDistance(distance)}</div>`;
            }

            item.innerHTML = `
                <div class="result-name">${poi.name}</div>
                <div class="result-address">${poi.pname || ''}${poi.cityname || ''}${poi.adname || ''}${poi.address || ''}</div>
                ${distanceText}
                <div class="search-actions">
                    <button class="btn btn-small btn-success" onclick="poiSearch.setAsStartPoint(${index})">设为起点</button>
                    <button class="btn btn-small btn-warning" onclick="poiSearch.setAsEndPoint(${index})">设为终点</button>
                    <button class="btn btn-small" onclick="poiSearch.showOnMap(${index})">查看位置</button>
                    <button class="btn btn-small" onclick="poiSearch.addToFavorites(${index})" style="background: #faad14;">⭐ 收藏</button>
                </div>
            `;
            
            resultsDiv.appendChild(item);
        });
    }

    /**
     * 在地图上显示搜索结果标记
     */
    showSearchMarkersOnMap(pois) {
        // 清除之前的搜索标记
        this.clearSearchMarkers();

        pois.forEach((poi, index) => {
            // POI坐标是GCJ02格式，需要转换为WGS84用于Leaflet地图显示
            const wgs84Coords = CoordinateConverter.gcj02Togcj02(poi.location.lng, poi.location.lat);

            // 创建标记
            const marker = L.circleMarker([wgs84Coords[1], wgs84Coords[0]], {
                color: '#1890ff',
                fillColor: '#40a9ff',
                fillOpacity: 0.8,
                radius: 6
            }).addTo(map);

            // 添加弹窗
            marker.bindPopup(`
                <div style="min-width: 200px;">
                    <h4 style="margin: 0 0 8px 0;">${poi.name}</h4>
                    <p style="margin: 0 0 4px 0; font-size: 12px; color: #666;">
                        ${poi.pname || ''}${poi.cityname || ''}${poi.adname || ''}
                    </p>
                    <p style="margin: 0 0 8px 0; font-size: 12px; color: #666;">
                        ${poi.address || ''}
                    </p>
                    <p style="margin: 0 0 8px 0; font-size: 12px; color: #666;">
                        GCJ02坐标: ${poi.location.lat.toFixed(6)}, ${poi.location.lng.toFixed(6)}
                    </p>
                    <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                        <button class="btn btn-small btn-success" onclick="poiSearch.setAsStartPoint(${index})">设为起点</button>
                        <button class="btn btn-small btn-warning" onclick="poiSearch.setAsEndPoint(${index})">设为终点</button>
                        <button class="btn btn-small" onclick="poiSearch.addToFavorites(${index})" style="background: #faad14; color: white;">⭐ 收藏</button>
                    </div>
                </div>
            `);

            this.searchMarkers.push(marker);
        });

        // 调整地图视野以显示所有搜索结果
        if (this.searchMarkers.length > 0) {
            const group = new L.featureGroup(this.searchMarkers);
            map.fitBounds(group.getBounds(), { padding: [20, 20] });
        }
    }

    /**
     * 清除搜索标记
     */
    clearSearchMarkers() {
        this.searchMarkers.forEach(marker => {
            map.removeLayer(marker);
        });
        this.searchMarkers = [];
    }

    /**
     * 设置POI为起点
     */
    setAsStartPoint(index) {
        if (index >= 0 && index < this.currentSearchResults.length) {
            const poi = this.currentSearchResults[index];
            // POI坐标是GCJ02格式，需要转换为WGS84用于填充输入框
            const wgs84Coords = CoordinateConverter.gcj02Togcj02(poi.location.lng, poi.location.lat);

            // 填充到起点输入框
            document.getElementById('startLng').value = wgs84Coords[0].toFixed(6);
            document.getElementById('startLat').value = wgs84Coords[1].toFixed(6);

            updateStatus(`已填充起点坐标: ${poi.name}，请点击"开始算路"按钮`, 'success');

            // 添加到历史记录
            if (coordinateManager) {
                coordinateManager.addToHistory(wgs84Coords[0], wgs84Coords[1], poi.name, 'POI搜索设置的起点');
            }
        }
    }

    /**
     * 设置POI为终点
     */
    setAsEndPoint(index) {
        if (index >= 0 && index < this.currentSearchResults.length) {
            const poi = this.currentSearchResults[index];
            // POI坐标是GCJ02格式，需要转换为WGS84用于填充输入框
            const wgs84Coords = CoordinateConverter.gcj02Togcj02(poi.location.lng, poi.location.lat);

            // 填充到终点输入框
            document.getElementById('endLng').value = wgs84Coords[0].toFixed(6);
            document.getElementById('endLat').value = wgs84Coords[1].toFixed(6);

            updateStatus(`已填充终点坐标: ${poi.name}，请点击"开始算路"按钮`, 'success');

            // 添加到历史记录
            if (coordinateManager) {
                coordinateManager.addToHistory(wgs84Coords[0], wgs84Coords[1], poi.name, 'POI搜索设置的终点');
            }
        }
    }

    /**
     * 在地图上显示POI位置
     */
    showOnMap(index) {
        if (index >= 0 && index < this.currentSearchResults.length) {
            const poi = this.currentSearchResults[index];
            // POI坐标是GCJ02格式，需要转换为WGS84用于Leaflet地图显示
            const wgs84Coords = CoordinateConverter.gcj02Togcj02(poi.location.lng, poi.location.lat);

            // 移动地图到该位置
            map.setView([wgs84Coords[1], wgs84Coords[0]], 16);

            // 打开对应标记的弹窗
            if (this.searchMarkers[index]) {
                this.searchMarkers[index].openPopup();
            }
        }
    }

    /**
     * 计算两点间距离（单位：米）
     */
    calculateDistance(lat1, lng1, lat2, lng2) {
        const R = 6371000; // 地球半径（米）
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    /**
     * 格式化距离显示
     */
    formatDistance(distance) {
        if (distance < 1000) {
            return Math.round(distance) + 'm';
        } else {
            return (distance / 1000).toFixed(1) + 'km';
        }
    }

    /**
     * 添加POI到收藏夹
     */
    addToFavorites(index) {
        if (index >= 0 && index < this.currentSearchResults.length) {
            const poi = this.currentSearchResults[index];

            // 构建收藏名称和备注
            const name = poi.name;
            const address = `${poi.pname || ''}${poi.cityname || ''}${poi.adname || ''}${poi.address || ''}`;
            const note = `POI搜索结果 - ${address}`;

            // POI坐标是GCJ02格式，需要转换为WGS84用于收藏
            const wgs84Coords = CoordinateConverter.gcj02Togcj02(poi.location.lng, poi.location.lat);

            // 添加到收藏夹
            if (coordinateManager) {
                if (coordinateManager.addToFavorites(wgs84Coords[0], wgs84Coords[1], name, note)) {
                    updateStatus(`已收藏POI: ${name}`, 'success');
                }
            } else {
                updateStatus('坐标管理器未初始化', 'error');
            }
        }
    }
}

// 全局POI搜索实例
let poiSearch = null;

// 初始化POI搜索
document.addEventListener('DOMContentLoaded', function() {
    poiSearch = new POISearch();
});

// 导出的全局函数
function searchNearby() {
    const keyword = document.getElementById('poiSearchInput').value.trim();
    if (!keyword) {
        updateStatus('请输入搜索关键词', 'error');
        return;
    }
    
    if (poiSearch) {
        poiSearch.searchPOI(keyword);
    }
}

function clearSearchResults() {
    if (poiSearch) {
        poiSearch.clearSearchMarkers();
        poiSearch.currentSearchResults = [];
        
        const resultsDiv = document.getElementById('searchResults');
        if (resultsDiv) {
            resultsDiv.innerHTML = '';
        }
        
        updateStatus('搜索结果已清除', 'info');
    }
}

function searchAroundPoint() {
    if (!rightClickPosition) return;

    const keyword = prompt('请输入要搜索的关键词（如：餐厅、加油站、银行等）:');
    if (keyword && poiSearch) {
        // rightClickPosition是WGS84坐标，需要转换为GCJ02用于POI搜索
        const gcj02Coords = CoordinateConverter.gcj02Togcj02(rightClickPosition[0], rightClickPosition[1]);
        poiSearch.searchPOI(keyword, gcj02Coords);
    }
    hideContextMenu();
}
