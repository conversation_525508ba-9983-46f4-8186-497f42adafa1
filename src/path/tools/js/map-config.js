/**
 * 地图配置和图层管理
 */

// 高德地图配置
const AMAP_CONFIG = {
    key: 'f7f401fd4ffb75720cba09ffb7b24a4c',
    version: '2.0',
    plugins: [
        'AMap.Driving',
        'AMap.Walking',
        'AMap.Riding',
        'AMap.PlaceSearch',
        'AMap.AutoComplete',
        'AMap.Geocoder'
    ]
};

// 地图图层配置
const MAP_LAYERS = {
    // 高德街道图
    amap_street: {
        url: 'https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
        options: {
            subdomains: '1234',
            attribution: '© 高德地图'
        }
    },
    
    // 高德卫星图
    amap_satellite: {
        url: 'https://webst0{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
        options: {
            subdomains: '1234',
            attribution: '© 高德地图'
        }
    },
    
    // 高德卫星标注图层
    amap_satellite_labels: {
        url: 'https://webst0{s}.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}',
        options: {
            subdomains: '1234',
            attribution: '© 高德地图'
        }
    },
    
    // WMS服务示例 (可以替换为实际的WMS服务)
    wms_layer: {
        url: 'https://demo.boundlessgeo.com/geoserver/ows',
        options: {
            layers: 'ne:ne_10m_admin_0_countries',
            format: 'image/png',
            transparent: true,
            attribution: 'WMS Layer'
        }
    }
};

// 地图样式配置
const MAP_STYLES = {
    default: {
        center: [31.185546, 121.321132], // 虹桥绿谷
        zoom: 13,
        minZoom: 3,
        maxZoom: 18
    },
    
    marker: {
        start: {
            color: 'green',
            fillColor: '#90EE90',
            fillOpacity: 0.8,
            radius: 8
        },
        end: {
            color: 'red',
            fillColor: '#FFB6C1',
            fillOpacity: 0.8,
            radius: 8
        },
        waypoint: {
            color: 'blue',
            fillColor: '#87CEEB',
            fillOpacity: 0.8,
            radius: 6
        }
    },
    
    route: {
        color: '#ff6b6b',
        weight: 5,
        opacity: 0.8,
        dashArray: '10, 5'
    }
};

// 创建地图图层
function createMapLayers() {
    const layers = {};
    
    // 创建高德街道图层
    layers.amapStreet = L.tileLayer(
        MAP_LAYERS.amap_street.url,
        MAP_LAYERS.amap_street.options
    );
    
    // 创建高德卫星图层
    layers.amapSatellite = L.tileLayer(
        MAP_LAYERS.amap_satellite.url,
        MAP_LAYERS.amap_satellite.options
    );
    
    // 创建高德卫星标注图层
    layers.amapSatelliteLabels = L.tileLayer(
        MAP_LAYERS.amap_satellite_labels.url,
        MAP_LAYERS.amap_satellite_labels.options
    );
    
    // 创建WMS图层
    layers.wmsLayer = L.tileLayer.wms(
        MAP_LAYERS.wms_layer.url,
        MAP_LAYERS.wms_layer.options
    );
    
    return layers;
}

// 创建图层控制器
function createLayerControl(layers) {
    const baseLayers = {
        "高德街道图": layers.amapStreet,
        "高德卫星图": L.layerGroup([layers.amapSatellite, layers.amapSatelliteLabels])
    };
    
    const overlayLayers = {
        "WMS图层": layers.wmsLayer
    };
    
    return L.control.layers(baseLayers, overlayLayers, {
        position: 'topright',
        collapsed: false
    });
}

// 地图初始化配置
const MapConfig = {
    AMAP_CONFIG,
    MAP_LAYERS,
    MAP_STYLES,
    createMapLayers,
    createLayerControl
};

// 导出配置（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MapConfig;
}
