# 使用指南

## 🚀 快速开始

### 1. 启动应用

```bash
# 方法一：使用启动脚本（推荐）
cd src/path/tools/js
./start.sh

# 方法二：手动启动
python3 server.py -p 8000
```

### 2. 访问应用

打开浏览器访问：
- **主应用**: http://localhost:8000/index.html
- **功能测试**: http://localhost:8000/test.html

## 📖 功能使用说明

### 基础地图操作

1. **查看坐标**
   - 点击地图任意位置
   - 右侧面板显示WGS84和GCJ02坐标
   - 支持实时坐标转换

2. **切换地图图层**
   - 点击"卫星图"按钮切换到卫星视图
   - 点击"街道图"按钮切换到街道视图
   - 使用右上角图层控制面板进行详细控制

3. **WMS图层叠加**
   - 点击"切换WMS"按钮开启/关闭WMS图层
   - 可在map-config.js中配置自定义WMS服务

### 路径规划操作

1. **开始路径规划**
   ```
   点击"开始规划"按钮 → 进入路径规划模式
   ```

2. **添加路径点**
   ```
   第1次点击 → 设置起点（绿色标记）
   第2次点击 → 设置终点（红色标记）
   第3+次点击 → 添加途经点（蓝色标记，仅驾车模式）
   ```

3. **查看路径信息**
   - 右侧面板显示路径详情
   - 包含总距离、预计时间、导航指令
   - 地图上显示路径线条

4. **清除操作**
   - "清除路径"：仅清除路径线条和信息
   - "清除标记"：清除所有标记点，退出规划模式

## 🛠️ 高级配置

### 修改地图中心点

编辑 `map-config.js`：
```javascript
MAP_STYLES: {
    default: {
        center: [39.9042, 116.4074], // [纬度, 经度]
        zoom: 13
    }
}
```

### 配置自定义WMS服务

编辑 `map-config.js` 中的 `wms_layer`：
```javascript
wms_layer: {
    url: 'https://your-wms-server.com/geoserver/ows',
    options: {
        layers: 'your:layer_name',
        format: 'image/png',
        transparent: true,
        attribution: '© Your WMS Service'
    }
}
```

### 更换高德API密钥

1. 编辑 `map-config.js`：
```javascript
AMAP_CONFIG: {
    key: 'your-new-api-key',
    version: '2.0'
}
```

2. 编辑 `index.html` 和 `test.html` 中的script标签：
```html
<script src="https://webapi.amap.com/maps?v=2.0&key=your-new-api-key"></script>
```

### 自定义标记样式

编辑 `map-config.js` 中的 `MAP_STYLES.marker`：
```javascript
marker: {
    start: {
        color: 'green',
        fillColor: '#90EE90',
        fillOpacity: 0.8,
        radius: 8
    }
    // ... 其他标记样式
}
```

## 🔧 故障排除

### 常见问题及解决方案

1. **地图不显示**
   ```
   问题：白屏或地图区域空白
   解决：检查网络连接，确认高德API密钥有效
   ```

2. **路径规划失败**
   ```
   问题：点击后无路径显示
   解决：确保起终点在中国境内，检查API配额
   ```

3. **坐标显示异常**
   ```
   问题：坐标值明显错误
   解决：检查点击位置，确认在有效坐标范围内
   ```

4. **服务器启动失败**
   ```
   问题：端口被占用
   解决：使用 python3 server.py -p 8001 更换端口
   ```

### 调试模式

1. **开启浏览器开发者工具**
   - 按F12或右键选择"检查"
   - 查看Console标签页的日志输出

2. **查看网络请求**
   - 切换到Network标签页
   - 检查API请求是否成功

3. **JavaScript错误排查**
   - Console中的红色错误信息
   - 检查相关JavaScript文件是否正确加载

## 📊 性能优化

### 地图性能

1. **合理设置缩放级别**
   ```javascript
   // 避免过高的最大缩放级别
   maxZoom: 18  // 推荐值
   ```

2. **限制同时显示的标记数量**
   ```javascript
   // 在添加新标记前清除旧标记
   if (markers.length > 100) {
       clearOldMarkers();
   }
   ```

### 网络优化

1. **使用CDN加速**
   - Leaflet和高德API已使用CDN
   - 考虑将应用部署到CDN

2. **启用浏览器缓存**
   - 服务器已配置适当的缓存头
   - 静态资源会被浏览器缓存

## 🚀 部署到生产环境

### 1. 静态文件部署

```bash
# 复制所有文件到Web服务器目录
cp -r src/path/tools/js/* /var/www/html/map-app/

# 配置Nginx（示例）
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html/map-app;
    index index.html;
    
    location / {
        try_files $uri $uri/ =404;
    }
}
```

### 2. Docker部署

创建 `Dockerfile`：
```dockerfile
FROM nginx:alpine
COPY src/path/tools/js /usr/share/nginx/html
EXPOSE 80
```

构建和运行：
```bash
docker build -t map-app .
docker run -p 80:80 map-app
```

### 3. 安全考虑

1. **API密钥安全**
   - 在高德控制台设置域名白名单
   - 定期轮换API密钥

2. **HTTPS部署**
   - 生产环境必须使用HTTPS
   - 配置SSL证书

3. **访问控制**
   - 根据需要设置IP白名单
   - 配置适当的CORS策略
