# QGIS双算路对比可视化使用说明

## 📋 概述

本文档介绍如何在QGIS中使用双算路测试系统导出的GeoJSON数据进行可视化分析。

## 🎯 功能特点

- **自动样式配置**: 预定义的QML样式文件，一键应用专业样式
- **分级可视化**: 根据差异程度自动分级显示对比分析点
- **多要素支持**: 支持起终点、路径线、对比分析等多种要素类型
- **中文界面**: 完全中文化的字段别名和标签

## 📁 文件说明

### 核心文件
- `dual-route-comparison.qml` - QGIS样式文件
- `qgis-style-guide.md` - 详细的样式指南和分析方法
- `batch_route_test_YYYY-MM-DD.geojson` - 测试结果数据文件

### 数据结构
GeoJSON文件包含以下要素类型：

#### 点要素 (Points)
- **start_point**: 测试起点 (绿色圆形)
- **end_point**: 测试终点 (红色圆形)  
- **comparison_analysis**: 对比分析中心点 (分级菱形)

#### 线要素 (LineStrings)
- **direct_line**: 起终点直线连接 (灰色虚线)
- **amap_route**: 高德地图路径 (红色实线)
- **cpp_route**: C++算路路径 (蓝色实线)

## 🚀 快速开始

### 1. 导入GeoJSON数据
1. 打开QGIS
2. 菜单: **图层** → **添加图层** → **添加矢量图层**
3. 选择导出的 `.geojson` 文件
4. 点击 **添加**

### 2. 应用预定义样式
1. 右键点击图层 → **属性**
2. 切换到 **符号系统** 标签
3. 点击 **样式** 按钮 → **加载样式**
4. 选择 `dual-route-comparison.qml` 文件
5. 点击 **确定** 应用样式

### 3. 验证样式效果
样式应用后，您应该看到：
- ✅ 绿色圆形起点
- ✅ 红色圆形终点
- ✅ 灰色虚线直线连接
- ✅ 红色实线高德路径
- ✅ 蓝色实线C++路径
- ✅ 分级颜色的菱形对比分析点

## 🎨 样式详解

### 对比分析点分级
对比分析点根据距离差异百分比自动分级：
- **绿色** (#00AA00): 差异 < 5% (算路结果相近)
- **黄色** (#FFAA00): 差异 5-10% (中等差异)
- **红色** (#AA0000): 差异 > 10% (显著差异)

### 路径样式
- **高德路径**: 红色实线，线宽3mm，透明度80%
- **C++路径**: 蓝色实线，线宽3mm，透明度80%
- **直线连接**: 灰色虚线，线宽0.5mm，透明度50%

## 📊 数据分析

### 属性字段说明
- `type`: 要素类型
- `test_id`: 测试序号
- `name`: 地点名称
- `test_status`: 测试状态 (如: success_success)
- `distance_diff_percent`: 距离差异百分比
- `amap_distance_km`: 高德路径距离
- `cpp_distance_km`: C++路径距离
- `cpp_query_time_ms`: C++查询耗时

### 常用筛选表达式

#### 按测试状态筛选
```sql
-- 双算路都成功
"test_status" = 'success_success'

-- 高德成功但C++失败
"test_status" = 'success_failed'

-- 存在失败的测试
"test_status" LIKE '%failed%'
```

#### 按差异程度筛选
```sql
-- 距离差异大于10%
"distance_diff_percent" > 10

-- 时间差异小于5%
"time_diff_percent" < 5

-- C++查询时间超过1秒
"cpp_query_time_ms" > 1000
```

#### 按距离范围筛选
```sql
-- 短距离测试 (< 10km)
"direct_distance_km" < 10

-- 长距离测试 (> 50km)
"direct_distance_km" > 50
```

## 🔧 高级功能

### 1. 设置标注
1. 图层属性 → **标注** 标签
2. 选择 **显示此图层的标注**
3. 标注字段选择 `test_id` 或 `name`
4. 调整字体大小和颜色

### 2. 创建热力图
1. 复制对比分析点图层
2. 符号系统 → **热力图**
3. 权重字段选择 `distance_diff_percent`
4. 调整半径和颜色渐变

### 3. 统计分析
1. 打开属性表
2. 选择要分析的要素
3. 使用 **统计面板** 查看汇总信息

## 📈 分析报告模板

### 基础统计
- 总测试数量: ___
- 双算路成功率: ___%
- 平均距离差异: ___%
- 平均C++查询时间: ___ms

### 差异分析
- 距离差异 < 5%: ___个 (__%)
- 距离差异 5-10%: ___个 (__%)
- 距离差异 > 10%: ___个 (__%)

### 性能分析
- C++查询 < 500ms: ___个 (__%)
- C++查询 500-1000ms: ___个 (__%)
- C++查询 > 1000ms: ___个 (__%)

## ❓ 常见问题

### Q: 样式没有正确应用？
A: 确保：
- GeoJSON文件包含 `type` 字段
- 字段值匹配样式定义 (start_point, end_point等)
- QGIS版本兼容 (建议3.28+)

### Q: 报错"Cannot apply style with symbology to layer with a different geometry type"？
A: 这是几何类型不匹配的问题，解决方法：
1. **推荐方法**: 使用最新的QML文件（已修复几何类型设置）
2. **手动修复**: 在QGIS中先导入数据，然后应用样式
3. **分层导入**: 分别导入点要素和线要素，分别应用对应样式

### Q: 对比分析点颜色不对？
A: 检查：
- `distance_diff_percent` 字段是否存在
- 字段值是否为数值类型
- 数据表达式是否正确

### Q: 路径显示异常？
A: 验证：
- 坐标系是否为WGS84 (EPSG:4326)
- 坐标顺序为 [经度, 纬度]
- 坐标范围是否合理

## 📞 技术支持

如遇到问题，请检查：
1. QGIS版本是否为3.28或更高
2. GeoJSON文件格式是否正确
3. 样式文件是否完整

更多详细信息请参考 `qgis-style-guide.md` 文档。

---

🗺️ **祝您分析愉快！通过QGIS的强大可视化功能，轻松发现双算路系统的性能差异和优化机会。**
