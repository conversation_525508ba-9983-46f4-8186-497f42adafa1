/**
 * 主应用程序
 * 整合地图显示、坐标转换、路径规划功能
 */

// 全局变量
let map;
let layers;
let layerControl;
let routePlanner;
let markers = [];
let routePolyline = null;
let clickedPoints = [];
let isRoutingMode = false;
let currentWMSLayer = null;

// 右键菜单相关变量
let contextMenu;
let rightClickPosition = null;
let startPoint = null;
let endPoint = null;
let waypoints = [];

// 双算路对比相关变量
let cppRouteClient = null;
let amapRoute = null;
let cppRoute = null;
let amapPolyline = null;
let cppPolyline = null;

// 应用程序初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * 初始化应用程序
 */
async function initializeApp() {
    try {
        updateStatus('正在初始化地图...', 'info');
        
        // 初始化地图
        initMap();
        
        // 初始化路径规划器
        routePlanner = new RoutePlanner();

        // 等待路径规划器初始化完成
        updateStatus('正在初始化路径规划服务...', 'info');
        await routePlanner.waitForInit();
        console.log('路径规划器初始化完成');

        // 初始化Path算路客户端
        cppRouteClient = new CppRouteClient('http://localhost:8080');

        // 检查C++服务器状态
        const cppServerAvailable = await cppRouteClient.checkServerStatus();
        if (cppServerAvailable) {
            console.log('Path算路服务器可用');
            updateStatus('地图和算路服务初始化完成，支持双算路对比', 'success');
        } else {
            console.warn('Path算路服务器不可用，仅支持高德算路');
            updateStatus('地图初始化完成，Path算路服务不可用', 'warning');
        }
        
    } catch (error) {
        console.error('应用初始化失败:', error);
        updateStatus('应用初始化失败: ' + error.message, 'error');
    }
}

/**
 * 初始化地图
 */
function initMap() {
    // 创建地图实例
    map = L.map('map', {
        center: MAP_STYLES.default.center,
        zoom: MAP_STYLES.default.zoom,
        minZoom: MAP_STYLES.default.minZoom,
        maxZoom: MAP_STYLES.default.maxZoom
    });
    
    // 创建图层
    layers = createMapLayers();
    
    // 添加默认图层（高德街道图）
    layers.amapStreet.addTo(map);
    
    // 创建图层控制器
    layerControl = createLayerControl(layers);
    layerControl.addTo(map);
    
    // 添加地图点击事件
    map.on('click', onMapClick);

    // 添加地图移动事件
    map.on('moveend', onMapMoveEnd);

    // 添加右键菜单事件
    map.on('contextmenu', onMapRightClick);

    // 初始化右键菜单
    initContextMenu();

    console.log('地图初始化完成');
}

/**
 * 地图点击事件处理
 * @param {Object} e - 点击事件对象
 */
function onMapClick(e) {
    const latlng = e.latlng;
    const coordinates = [latlng.lng, latlng.lat];
    
    // 坐标转换 (Leaflet使用WGS84，高德使用GCJ02)
    const gcj02Coords = CoordinateConverter.wgs84ToGcj02(coordinates[0], coordinates[1]);
    
    // 更新坐标显示
    updateCoordinateDisplay(coordinates, gcj02Coords);
    
    // 如果在路径规划模式，添加点击点
    if (isRoutingMode) {
        addRoutePoint(coordinates);
    } else {
        // 普通模式，添加标记
        addMarker(coordinates, 'click');
    }
}

/**
 * 地图移动结束事件
 */
function onMapMoveEnd() {
    const center = map.getCenter();
    const zoom = map.getZoom();
    console.log(`地图中心: ${center.lat.toFixed(6)}, ${center.lng.toFixed(6)}, 缩放级别: ${zoom}`);
}

/**
 * 更新坐标显示
 * @param {Array} wgs84 - WGS84坐标
 * @param {Array} gcj02 - GCJ02坐标
 */
function updateCoordinateDisplay(wgs84, gcj02) {
    const coordinatesDiv = document.getElementById('coordinates');
    coordinatesDiv.innerHTML = `
        <strong>点击坐标:</strong><br>
        WGS84: ${wgs84[1].toFixed(6)}, ${wgs84[0].toFixed(6)}<br>
        GCJ02: ${gcj02[1].toFixed(6)}, ${gcj02[0].toFixed(6)}<br>
        <small>纬度, 经度</small>
    `;
}

/**
 * 添加地图标记
 * @param {Array} coordinates - 坐标 [lng, lat]
 * @param {string} type - 标记类型
 */
function addMarker(coordinates, type = 'waypoint') {
    const markerStyle = MAP_STYLES.marker[type] || MAP_STYLES.marker.waypoint;
    
    const marker = L.circleMarker([coordinates[1], coordinates[0]], markerStyle)
        .addTo(map)
        .bindPopup(`
            <b>坐标点</b><br>
            经度: ${coordinates[0].toFixed(6)}<br>
            纬度: ${coordinates[1].toFixed(6)}
        `);
    
    markers.push(marker);
    return marker;
}

/**
 * 添加路径规划点
 * @param {Array} coordinates - 坐标 [lng, lat]
 */
function addRoutePoint(coordinates) {
    clickedPoints.push(coordinates);
    
    let markerType;
    if (clickedPoints.length === 1) {
        markerType = 'start';
    } else if (clickedPoints.length === 2) {
        markerType = 'end';
    } else {
        markerType = 'waypoint';
    }
    
    const marker = addMarker(coordinates, markerType);
    
    // 如果有起点和终点，自动计算路径
    if (clickedPoints.length >= 2) {
        calculateAndDisplayRoute();
    }
    
    updateStatus(`已添加${getPointTypeName(markerType)}点 (${clickedPoints.length}/${getMaxPoints()})`, 'info');
}

/**
 * 获取点类型名称
 */
function getPointTypeName(type) {
    const names = {
        'start': '起',
        'end': '终',
        'waypoint': '途经'
    };
    return names[type] || '标记';
}

/**
 * 获取最大点数限制
 */
function getMaxPoints() {
    return routePlanner.routeType === 'driving' ? 16 : 2; // 驾车支持途经点，步行和骑行只支持起终点
}

/**
 * 计算并显示路径
 */
async function calculateAndDisplayRoute() {
    if (clickedPoints.length < 2) {
        return;
    }

    try {
        updateStatus('正在计算路径...', 'info');

        // 确保路径规划器已初始化
        if (!routePlanner.isReady()) {
            updateStatus('路径规划服务正在初始化，请稍候...', 'info');
            await routePlanner.waitForInit();
        }

        const startPoint = clickedPoints[0];
        const endPoint = clickedPoints[clickedPoints.length - 1];
        const waypoints = clickedPoints.slice(1, -1);

        // 转换坐标到GCJ02 (高德地图坐标系)
        const startGCJ02 = CoordinateConverter.wgs84ToGcj02(startPoint[0], startPoint[1]);
        const endGCJ02 = CoordinateConverter.wgs84ToGcj02(endPoint[0], endPoint[1]);
        const waypointsGCJ02 = waypoints.map(point =>
            CoordinateConverter.wgs84ToGcj02(point[0], point[1])
        );

        // 计算路径
        const route = await routePlanner.calculateRoute(startPoint, endPoint, waypoints);

        // 显示路径
        displayRoute(route);

        // 更新路径信息
        updateRouteInfo(route);

        updateStatus('路径计算完成', 'success');

    } catch (error) {
        console.error('路径计算失败1:', error);
        updateStatus('路径计算失败2: ' + error.message, 'error');
    }
}

/**
 * 显示路径在地图上
 * @param {Object} route - 路径数据
 */
function displayRoute(route) {
    // 清除之前的路径
    if (routePolyline) {
        map.removeLayer(routePolyline);
    }

    // 转换坐标回WGS84用于Leaflet显示
    const wgs84Coordinates = route.coordinates.map(coord => {
        const wgs84 = CoordinateConverter.gcj02Togcj02(coord[0], coord[1]);
        return [wgs84[1], wgs84[0]]; // Leaflet使用 [lat, lng] 格式
    });

    // 创建路径线
    routePolyline = L.polyline(wgs84Coordinates, MAP_STYLES.route)
        .addTo(map)
        .bindPopup(`
            <b>路径信息</b><br>
            距离: ${routePlanner.formatDistance(route.totalDistance)}<br>
            时间: ${routePlanner.formatTime(route.totalTime)}<br>
            类型: ${getRouteTypeName(route.type)}
        `);

    // 调整地图视野以显示完整路径
    map.fitBounds(routePolyline.getBounds(), { padding: [20, 20] });
}

/**
 * 获取路径类型名称
 */
function getRouteTypeName(type) {
    const names = {
        'driving': '驾车',
        'walking': '步行',
        'riding': '骑行'
    };
    return names[type] || '未知';
}

/**
 * 更新路径信息显示
 * @param {Object} route - 路径数据
 */
function updateRouteInfo(route) {
    const routeInfoDiv = document.getElementById('routeInfo');

    let instructionsHtml = '';
    if (route.instructions && route.instructions.length > 0) {
        instructionsHtml = '<h4>导航指令:</h4>';
        instructionsHtml += '<div style="max-height: 300px; overflow-y: auto; border: 1px solid #e8e8e8; padding: 10px; border-radius: 4px;">';
        instructionsHtml += '<ol style="margin: 0; padding-left: 20px;">';

        route.instructions.forEach((instruction, index) => {
            const stepNumber = index + 1;
            instructionsHtml += `<li style="margin-bottom: 8px; line-height: 1.4;">
                <strong>第${stepNumber}步:</strong> ${instruction.instruction}<br>
                <small style="color: #666;">距离: ${routePlanner.formatDistance(instruction.distance)} | 时间: ${routePlanner.formatTime(instruction.time)}</small>
            </li>`;
        });

        instructionsHtml += '</ol>';
        instructionsHtml += `<div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #e8e8e8; font-size: 12px; color: #666;">
            共 ${route.instructions.length} 条导航指令
        </div>`;
        instructionsHtml += '</div>';
    }

    routeInfoDiv.innerHTML = `
        <h4>路径概况:</h4>
        <p><strong>总距离:</strong> ${routePlanner.formatDistance(route.totalDistance)}</p>
        <p><strong>预计时间:</strong> ${routePlanner.formatTime(route.totalTime)}</p>
        <p><strong>路径类型:</strong> ${getRouteTypeName(route.type)}</p>
        <p><strong>途经点数:</strong> ${clickedPoints.length}</p>
        ${instructionsHtml}
    `;
}

/**
 * 更新状态显示
 * @param {string} message - 状态消息
 * @param {string} type - 状态类型 (success, error, info)
 */
function updateStatus(message, type = 'info') {
    const statusDiv = document.getElementById('status');
    statusDiv.className = `status ${type}`;
    statusDiv.textContent = message;
    console.log(`[${type.toUpperCase()}] ${message}`);
}

// ==================== 控制函数 ====================

/**
 * 切换到卫星图
 */
function switchToSatellite() {
    // 移除当前基础图层
    map.eachLayer(layer => {
        if (layer === layers.amapStreet || layer === layers.amapSatellite || layer === layers.amapSatelliteLabels) {
            map.removeLayer(layer);
        }
    });

    // 添加卫星图层
    layers.amapSatellite.addTo(map);
    layers.amapSatelliteLabels.addTo(map);

    updateStatus('已切换到卫星图', 'success');
}

/**
 * 切换到街道图
 */
function switchToStreet() {
    // 移除当前基础图层
    map.eachLayer(layer => {
        if (layer === layers.amapStreet || layer === layers.amapSatellite || layer === layers.amapSatelliteLabels) {
            map.removeLayer(layer);
        }
    });

    // 添加街道图层
    layers.amapStreet.addTo(map);

    updateStatus('已切换到街道图', 'success');
}

/**
 * 切换WMS图层
 */
function toggleWMS() {
    if (currentWMSLayer) {
        map.removeLayer(currentWMSLayer);
        currentWMSLayer = null;
        updateStatus('WMS图层已关闭', 'info');
    } else {
        currentWMSLayer = layers.wmsLayer;
        currentWMSLayer.addTo(map);
        updateStatus('WMS图层已开启', 'success');
    }
}

/**
 * 开始路径规划模式
 */
function startRouting(event) {
    isRoutingMode = true;
    clearRoute();
    // clearMarkers();
    clickedPoints = [];

    calculateRoute();
    updateStatus('路径规划模式', 'info');

    // 更新按钮状态（如果有事件对象）
    if (event && event.target) {
        const btn = event.target;
        btn.textContent = '规划中...';
        btn.style.background = '#52c41a';

        // 3秒后恢复按钮
        setTimeout(() => {
            btn.textContent = '开始规划';
            btn.style.background = '#1890ff';
        }, 3000);
    }
}

/**
 * 清除路径
 */
function clearRoute() {
    if (routePolyline) {
        map.removeLayer(routePolyline);
        routePolyline = null;
    }

    // 清除路径信息
    document.getElementById('routeInfo').innerHTML = '暂无路径信息';

    // 清除路径规划器中的路径
    if (routePlanner) {
        routePlanner.clearRoute();
    }

    updateStatus('路径已清除', 'info');
}

/**
 * 清除所有标记
 */
function clearMarkers() {
    markers.forEach(marker => {
        map.removeLayer(marker);
    });
    markers = [];
    clickedPoints = [];
    isRoutingMode = false;

    // 清除坐标显示
    document.getElementById('coordinates').innerHTML = '点击地图查看坐标';

    updateStatus('所有标记已清除', 'info');
}

// ==================== POI搜索和地理编码辅助函数 ====================

/**
 * 通过坐标设置起点
 */
function setStartPointByCoords(coords, name = '') {
    // 清除之前的起点标记
    if (startPoint && startPoint.marker) {
        map.removeLayer(startPoint.marker);
    }

    // 清除markers数组中的起点标记
    markers.forEach((marker, index) => {
        if (marker._startPoint) {
            map.removeLayer(marker);
            markers.splice(index, 1);
        }
    });

    // 添加起点标记
    const marker = addMarker(coords, 'start');
    marker._startPoint = true;

    if (name) {
        marker.bindPopup(`<b>起点</b><br>${name}<br>坐标: ${coords[1].toFixed(6)}, ${coords[0].toFixed(6)}`);
    }

    // 设置起点对象，包含marker引用
    startPoint = {
        coordinates: coords,
        name: name,
        marker: marker
    };

    updateCoordinateDisplay(coords, CoordinateConverter.wgs84ToGcj02(coords[0], coords[1]));
    updateRoutePointsDisplay();
}

/**
 * 通过坐标设置终点
 */
function setEndPointByCoords(coords, name = '') {
    // 清除之前的终点标记
    if (endPoint && endPoint.marker) {
        map.removeLayer(endPoint.marker);
    }

    // 清除markers数组中的终点标记
    markers.forEach((marker, index) => {
        if (marker._endPoint) {
            map.removeLayer(marker);
            markers.splice(index, 1);
        }
    });

    // 添加终点标记
    const marker = addMarker(coords, 'end');
    marker._endPoint = true;

    if (name) {
        marker.bindPopup(`<b>终点</b><br>${name}<br>坐标: ${coords[1].toFixed(6)}, ${coords[0].toFixed(6)}`);
    }

    // 设置终点对象，包含marker引用
    endPoint = {
        coordinates: coords,
        name: name,
        marker: marker
    };

    updateCoordinateDisplay(coords, CoordinateConverter.wgs84ToGcj02(coords[0], coords[1]));
    updateRoutePointsDisplay();
}

// ==================== 手动坐标输入功能 ====================

/**
 * 从输入框设置起点
 */
function setStartPointFromInput() {
    const lng = parseFloat(document.getElementById('startLng').value);
    const lat = parseFloat(document.getElementById('startLat').value);

    if (isNaN(lng) || isNaN(lat)) {
        updateStatus('请输入有效的起点坐标', 'error');
        return;
    }

    if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
        updateStatus('坐标范围错误：经度(-180~180)，纬度(-90~90)', 'error');
        return;
    }

    const coords = [lng, lat];
    const coordsText = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    const name = `手动输入起点: ${coordsText}`;

    setStartPointByCoords(coords, name);
    updateStatus(`起点已设置: ${coordsText}`, 'success');

    // 添加到历史记录
    if (coordinateManager) {
        coordinateManager.addToHistory(lng, lat, name, '手动输入的起点坐标');
    }
}

/**
 * 从输入框设置终点
 */
function setEndPointFromInput() {
    const lng = parseFloat(document.getElementById('endLng').value);
    const lat = parseFloat(document.getElementById('endLat').value);

    if (isNaN(lng) || isNaN(lat)) {
        updateStatus('请输入有效的终点坐标', 'error');
        return;
    }

    if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
        updateStatus('坐标范围错误：经度(-180~180)，纬度(-90~90)', 'error');
        return;
    }

    const coords = [lng, lat];
    const coordsText = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    const name = `手动输入终点: ${coordsText}`;

    setEndPointByCoords(coords, name);
    updateStatus(`终点已设置: ${coordsText}`, 'success');

    // 添加到历史记录
    if (coordinateManager) {
        coordinateManager.addToHistory(lng, lat, name, '手动输入的终点坐标');
    }
}

/**
 * 获取当前地图中心位置作为坐标
 */
function getCurrentLocation(type) {
    if (!map) {
        updateStatus('地图未初始化', 'error');
        return;
    }

    const center = map.getCenter();
    const lng = center.lng.toFixed(6);
    const lat = center.lat.toFixed(6);

    if (type === 'start') {
        document.getElementById('startLng').value = lng;
        document.getElementById('startLat').value = lat;
        updateStatus('已获取当前地图中心作为起点坐标', 'info');
    } else if (type === 'end') {
        document.getElementById('endLng').value = lng;
        document.getElementById('endLat').value = lat;
        updateStatus('已获取当前地图中心作为终点坐标', 'info');
    }
}

/**
 * 使用输入的坐标进行算路
 */
function calculateRouteFromCoords() {
    const startLng = parseFloat(document.getElementById('startLng').value);
    const startLat = parseFloat(document.getElementById('startLat').value);
    const endLng = parseFloat(document.getElementById('endLng').value);
    const endLat = parseFloat(document.getElementById('endLat').value);

    // 验证起点坐标
    if (isNaN(startLng) || isNaN(startLat)) {
        updateStatus('请输入有效的起点坐标', 'error');
        return;
    }

    // 验证终点坐标
    if (isNaN(endLng) || isNaN(endLat)) {
        updateStatus('请输入有效的终点坐标', 'error');
        return;
    }

    // 验证坐标范围
    if (startLng < -180 || startLng > 180 || startLat < -90 || startLat > 90 ||
        endLng < -180 || endLng > 180 || endLat < -90 || endLat > 90) {
        updateStatus('坐标范围错误：经度(-180~180)，纬度(-90~90)', 'error');
        return;
    }

    // 设置起终点
    const startCoords = [startLng, startLat];
    const endCoords = [endLng, endLat];
    const startText = `${startLat.toFixed(6)}, ${startLng.toFixed(6)}`;
    const endText = `${endLat.toFixed(6)}, ${endLng.toFixed(6)}`;

    setStartPointByCoords(startCoords, `手动输入起点: ${startText}`);
    setEndPointByCoords(endCoords, `手动输入终点: ${endText}`);

    // 延迟执行算路，确保起终点设置完成
    setTimeout(() => {
        if (typeof calculateRoute === 'function') {
            calculateRoute();
        } else {
            updateStatus('算路功能未加载', 'error');
        }
    }, 100);

    updateStatus(`开始算路: ${startText} → ${endText}`, 'info');
}

/**
 * 清空坐标输入框
 */
function clearCoordinateInputs() {
    document.getElementById('startLng').value = '';
    document.getElementById('startLat').value = '';
    document.getElementById('endLng').value = '';
    document.getElementById('endLat').value = '';
    updateStatus('坐标输入已清空', 'info');
}

/**
 * 填充示例坐标
 */
function fillSampleCoordinates(city) {
    const coordinates = {
        beijing: { lng: 116.4074, lat: 39.9042, name: '北京天安门' },
        shanghai: { lng: 121.4737, lat: 31.2304, name: '上海人民广场' },
        guangzhou: { lng: 113.2644, lat: 23.1291, name: '广州天河城' },
        shenzhen: { lng: 114.0579, lat: 22.5431, name: '深圳市民中心' }
    };

    const coord = coordinates[city];
    if (coord) {
        document.getElementById('startLng').value = coord.lng;
        document.getElementById('startLat').value = coord.lat;
        updateStatus(`已填充${coord.name}坐标`, 'info');
    }
}

// ==================== 右键菜单功能 ====================

/**
 * 初始化右键菜单
 */
function initContextMenu() {
    contextMenu = document.getElementById('contextMenu');

    // 点击其他地方隐藏菜单
    document.addEventListener('click', hideContextMenu);

    // 阻止地图容器的默认右键菜单
    document.getElementById('map').addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });
}

/**
 * 地图右键点击事件
 * @param {Object} e - 右键点击事件对象
 */
function onMapRightClick(e) {
    e.originalEvent.preventDefault();

    // 记录右键点击位置
    rightClickPosition = [e.latlng.lng, e.latlng.lat];

    // 显示右键菜单
    showContextMenu(e.originalEvent.clientX, e.originalEvent.clientY);
}

/**
 * 显示右键菜单
 * @param {number} x - 屏幕X坐标
 * @param {number} y - 屏幕Y坐标
 */
function showContextMenu(x, y) {
    contextMenu.style.left = x + 'px';
    contextMenu.style.top = y + 'px';
    contextMenu.style.display = 'block';

    // 更新菜单项状态
    updateContextMenuItems();
}

/**
 * 隐藏右键菜单
 */
function hideContextMenu() {
    if (contextMenu) {
        contextMenu.style.display = 'none';
    }
}

/**
 * 更新右键菜单项状态
 */
function updateContextMenuItems() {
    const menuItems = contextMenu.querySelectorAll('.context-menu-item');

    // 根据当前状态启用/禁用菜单项
    menuItems.forEach(item => {
        const text = item.textContent;

        if (text.includes('开始算路')) {
            // 只有设置了起点和终点才能算路
            item.disabled = !startPoint || !endPoint;
        } else if (text.includes('清除所有点')) {
            // 只有有点时才能清除
            item.disabled = !startPoint && !endPoint && waypoints.length === 0;
        }
    });
}

/**
 * 设置起点
 */
function setStartPoint() {
    if (!rightClickPosition) return;

    const coords = rightClickPosition;
    const coordsText = `${coords[1].toFixed(6)}, ${coords[0].toFixed(6)}`;
    const name = `右键设置起点: ${coordsText}`;

    // 填充到手动坐标输入框
    document.getElementById('startLng').value = coords[0].toFixed(6);
    document.getElementById('startLat').value = coords[1].toFixed(6);

    // 在地图上创建起点标记
    if (startPoint && startPoint.marker) {
        map.removeLayer(startPoint.marker);
    }
    const marker = addMarker(coords, 'start');
    marker.bindPopup(`<b>起点</b><br>${name}<br>坐标: ${coordsText}`);

    startPoint = {
        coordinates: coords,
        name: name,
        marker: marker
    };

    // 添加到历史记录
    if (coordinateManager) {
        coordinateManager.addToHistory(coords[0], coords[1], name, '右键点击设置的起点');
    }

    hideContextMenu();
    updateStatus(`已设置起点并填充坐标: ${coordsText}，请点击"开始算路"按钮`, 'success');
    updateRoutePointsDisplay();
}

/**
 * 设置终点
 */
function setEndPoint() {
    if (!rightClickPosition) return;

    const coords = rightClickPosition;
    const coordsText = `${coords[1].toFixed(6)}, ${coords[0].toFixed(6)}`;
    const name = `右键设置终点: ${coordsText}`;

    // 填充到手动坐标输入框
    document.getElementById('endLng').value = coords[0].toFixed(6);
    document.getElementById('endLat').value = coords[1].toFixed(6);

    // 在地图上创建终点标记
    if (endPoint && endPoint.marker) {
        map.removeLayer(endPoint.marker);
    }
    const marker = addMarker(coords, 'end');
    marker.bindPopup(`<b>终点</b><br>${name}<br>坐标: ${coordsText}`);

    endPoint = {
        coordinates: coords,
        name: name,
        marker: marker
    };

    // 添加到历史记录
    if (coordinateManager) {
        coordinateManager.addToHistory(coords[0], coords[1], name, '右键点击设置的终点');
    }

    hideContextMenu();
    updateStatus(`已设置终点并填充坐标: ${coordsText}，请点击"开始算路"按钮`, 'success');
    updateRoutePointsDisplay();
}

/**
 * 添加途经点
 */
function addWaypoint() {
    if (!rightClickPosition) return;

    // 检查途经点数量限制
    if (waypoints.length >= 14) { // 高德API限制最多16个点（起点+终点+14个途经点）
        updateStatus('途经点数量已达上限（14个）', 'error');
        hideContextMenu();
        return;
    }

    // 创建途经点标记
    const marker = addMarker(rightClickPosition, 'waypoint');
    waypoints.push({
        coordinates: rightClickPosition,
        marker: marker
    });

    hideContextMenu();
    updateStatus(`途经点已添加 (${waypoints.length}/14)`, 'success');
    updateRoutePointsDisplay();
}

/**
 * 开始算路
 */
async function calculateRoute() {
    if (!startPoint || !endPoint) {
        updateStatus('请先设置起点和终点', 'error');
        hideContextMenu();
        return;
    }

    try {
        updateStatus('正在计算路径...', 'info');
        hideContextMenu();

        // 确保路径规划器已初始化
        if (!routePlanner.isReady()) {
            updateStatus('路径规划服务正在初始化，请稍候...', 'info');
            await routePlanner.waitForInit();
        }

        // 准备路径规划参数
        const startCoords = startPoint.coordinates;
        const endCoords = endPoint.coordinates;
        const waypointCoords = waypoints.map(wp => wp.coordinates);

        // 转换坐标到GCJ02
        const startGCJ02 = CoordinateConverter.wgs84ToGcj02(startCoords[0], startCoords[1]);
        const endGCJ02 = CoordinateConverter.wgs84ToGcj02(endCoords[0], endCoords[1]);
        const waypointsGCJ02 = waypointCoords.map(coord =>
            CoordinateConverter.wgs84ToGcj02(coord[0], coord[1])
        );

        // print coordinates of start and end
        console.log('start:', startGCJ02);
        console.log('end:', endGCJ02);
        console.log('waypoints:', waypointsGCJ02);

        // 并行执行双算路
        const promises = [];

        // 高德算路
        updateStatus('正在计算高德路径...', 'info');
        promises.push(
            routePlanner.calculateRoute(startCoords, endCoords, waypointCoords)
                .then(route => ({ source: 'amap', route }))
                .catch(error => ({ source: 'amap', error }))
        );

        // Path算路（如果服务可用）
        if (cppRouteClient) {
            const cppServerAvailable = await cppRouteClient.checkServerStatus();
            if (cppServerAvailable) {
                updateStatus('正在计算Path路径...', 'info');

                // 准备途经点数据（C++算路使用WGS84坐标）
                const cppWaypoints = waypointCoords.map(coord => ({
                    lng: coord[0],
                    lat: coord[1]
                }));

                // 获取算路策略
                const strategySelect = document.getElementById('routeStrategy');
                const strategy = strategySelect ? parseInt(strategySelect.value) : 0;

                promises.push(
                    cppRouteClient.calculateRoute(startCoords[0], startCoords[1], endCoords[0], endCoords[1], cppWaypoints, strategy)
                        .then(route => ({ source: 'cpp', route }))
                        .catch(error => ({ source: 'cpp', error }))
                );
            }
        }

        // 等待所有算路完成
        const results = await Promise.all(promises);

        // 处理结果
        let amapResult = null;
        let cppResult = null;

        results.forEach(result => {
            if (result.source === 'amap') {
                if (result.error) {
                    console.error('高德算路失败:', result.error);
                } else {
                    amapResult = result.route;
                }
            } else if (result.source === 'cpp') {
                if (result.error) {
                    console.error('Path算路失败:', result.error);
                } else {
                    cppResult = result.route;
                }
            }
        });

        // 显示对比结果
        displayRouteComparison(amapResult, cppResult);

        if (amapResult || cppResult) {
            updateStatus('算路对比完成', 'success');
        } else {
            updateStatus('所有算路都失败了', 'error');
        }

    } catch (error) {
        console.error('路径计算失败3:', error);
        updateStatus('路径计算失败4: ' + error.message, 'error');
    }
}

/**
 * 清除所有点
 */
function clearAllPoints() {
    // 清除起点
    if (startPoint && startPoint.marker) {
        map.removeLayer(startPoint.marker);
    }
    startPoint = null;

    // 清除终点
    if (endPoint && endPoint.marker) {
        map.removeLayer(endPoint.marker);
    }
    endPoint = null;

    // 清除途经点
    waypoints.forEach(wp => {
        if (wp.marker) {
            map.removeLayer(wp.marker);
        }
    });
    waypoints = [];

    // 清除路径
    clearRoute();

    hideContextMenu();
    updateStatus('所有点已清除', 'info');
    updateRoutePointsDisplay();
}

/**
 * 更新路径点显示信息
 */
function updateRoutePointsDisplay() {
    const coordinatesDiv = document.getElementById('coordinates');

    let displayHtml = '<strong>路径点信息:</strong><br>';

    if (startPoint) {
        const coords = startPoint.coordinates;
        displayHtml += `🟢 起点: ${coords[1].toFixed(6)}, ${coords[0].toFixed(6)}<br>`;
    }

    if (waypoints.length > 0) {
        waypoints.forEach((wp, index) => {
            const coords = wp.coordinates;
            displayHtml += `🔵 途经点${index + 1}: ${coords[1].toFixed(6)}, ${coords[0].toFixed(6)}<br>`;
        });
    }

    if (endPoint) {
        const coords = endPoint.coordinates;
        displayHtml += `🔴 终点: ${coords[1].toFixed(6)}, ${coords[0].toFixed(6)}<br>`;
    }

    if (!startPoint && !endPoint && waypoints.length === 0) {
        displayHtml = '右键点击地图设置路径点';
    }

    coordinatesDiv.innerHTML = displayHtml;
}

// ==================== 双算路对比功能 ====================

/**
 * 显示路径对比结果
 * @param {Object} amapRoute - 高德算路结果
 * @param {Object} cppRoute - Path算路结果
 */
function displayRouteComparison(amapRoute, cppRoute) {
    // 清除之前的路径
    clearRouteDisplay();

    // 存储路径结果
    amapRoute && (window.amapRoute = amapRoute);
    cppRoute && (window.cppRoute = cppRoute);

    // 显示高德路径
    if (amapRoute) {
        displaySingleRoute(amapRoute, 'amap');
    }

    // 显示C++路径
    if (cppRoute) {
        displaySingleRoute(cppRoute, 'cpp');
    }

    // 更新对比信息
    updateComparisonInfo(amapRoute, cppRoute);
}

/**
 * 显示单条路径
 * @param {Object} route - 路径数据
 * @param {string} source - 路径来源 ('amap' 或 'cpp')
 */
function displaySingleRoute(route, source) {
    let coordinates;
    let style;

    if (source === 'amap') {
        // 高德路径需要坐标转换
        coordinates = route.coordinates.map(coord => {
            const wgs84 = CoordinateConverter.gcj02Togcj02(coord[0], coord[1]);
            return [wgs84[1], wgs84[0]]; // Leaflet使用 [lat, lng] 格式
        });

        style = {
            color: '#ff6b6b',
            weight: 5,
            opacity: 0.8,
            dashArray: '10, 5'
        };

        amapPolyline = L.polyline(coordinates, style)
            .addTo(map)
            .bindPopup(`
                <b>高德地图路径</b><br>
                距离: ${routePlanner.formatDistance(route.totalDistance)}<br>
                时间: ${routePlanner.formatTime(route.totalTime)}<br>
                类型: ${getRouteTypeName(route.type)}
            `);

    } else if (source === 'cpp') {
        // C++路径坐标已经是WGS84
        coordinates = route.coordinates.map(coord => [coord[1], coord[0]]); // [lat, lng]

        style = {
            color: '#4CAF50',
            weight: 5,
            opacity: 0.8,
            dashArray: '5, 10'
        };

        cppPolyline = L.polyline(coordinates, style)
            .addTo(map)
            .bindPopup(`
                <b>Path算路结果</b><br>
                距离: ${cppRouteClient.formatDistance(route.totalDistance)}<br>
                时间: ${cppRouteClient.formatTime(route.totalTime)}<br>
                查询时间: ${route.queryTime}ms
            `);
    }

    // 调整地图视野以显示所有路径
    if (coordinates && coordinates.length > 0) {
        const bounds = L.latLngBounds(coordinates);
        if (amapPolyline && cppPolyline) {
            // 如果两条路径都存在，合并边界
            const allBounds = L.latLngBounds([]);
            allBounds.extend(amapPolyline.getBounds());
            allBounds.extend(cppPolyline.getBounds());
            map.fitBounds(allBounds, { padding: [20, 20] });
        } else {
            map.fitBounds(bounds, { padding: [20, 20] });
        }
    }
}

/**
 * 更新对比信息显示
 * @param {Object} amapRoute - 高德算路结果
 * @param {Object} cppRoute - Path算路结果
 */
function updateComparisonInfo(amapRoute, cppRoute) {
    const routeInfoDiv = document.getElementById('routeInfo');

    let comparisonHtml = '<h4>🔄 算路对比结果:</h4>';

    if (amapRoute && cppRoute) {
        // 双路径对比
        const distanceDiff = Math.abs(amapRoute.totalDistance - cppRoute.totalDistance);
        const timeDiff = Math.abs(amapRoute.totalTime - cppRoute.totalTime);
        const distanceDiffPercent = ((distanceDiff / Math.min(amapRoute.totalDistance, cppRoute.totalDistance)) * 100).toFixed(1);
        const timeDiffPercent = ((timeDiff / Math.min(amapRoute.totalTime, cppRoute.totalTime)) * 100).toFixed(1);

        comparisonHtml += `
            <div style="display: flex; gap: 20px; margin: 15px 0;">
                <div style="flex: 1; border: 1px solid #ff6b6b; border-radius: 4px; padding: 10px;">
                    <h5 style="margin: 0 0 10px 0; color: #ff6b6b;">🗺️ 高德地图</h5>
                    <p><strong>距离:</strong> ${routePlanner.formatDistance(amapRoute.totalDistance)}</p>
                    <p><strong>时间:</strong> ${routePlanner.formatTime(amapRoute.totalTime)}</p>
                    <p><strong>指令数:</strong> ${amapRoute.instructions.length}</p>
                </div>
                <div style="flex: 1; border: 1px solid #4CAF50; border-radius: 4px; padding: 10px;">
                    <h5 style="margin: 0 0 10px 0; color: #4CAF50;">⚡ Path算路</h5>
                    <p><strong>距离:</strong> ${cppRouteClient.formatDistance(cppRoute.totalDistance)}</p>
                    <p><strong>时间:</strong> ${cppRouteClient.formatTime(cppRoute.totalTime)}</p>
                    <p><strong>查询时间:</strong> ${cppRoute.queryTime}ms</p>
                </div>
            </div>

            <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0;">
                <h5 style="margin: 0 0 10px 0;">📊 差异分析:</h5>
                <p><strong>距离差异:</strong> ${cppRouteClient.formatDistance(distanceDiff)} (${distanceDiffPercent}%)</p>
                <p><strong>时间差异:</strong> ${cppRouteClient.formatTime(timeDiff)} (${timeDiffPercent}%)</p>
                <p><strong>更短路径:</strong> ${amapRoute.totalDistance < cppRoute.totalDistance ? '高德地图' : 'Path算路'}</p>
                <p><strong>更快路径:</strong> ${amapRoute.totalTime < cppRoute.totalTime ? '高德地图' : 'Path算路'}</p>
            </div>
        `;

    } else if (amapRoute) {
        // 仅高德路径
        comparisonHtml += `
            <div style="border: 1px solid #ff6b6b; border-radius: 4px; padding: 10px; margin: 10px 0;">
                <h5 style="margin: 0 0 10px 0; color: #ff6b6b;">🗺️ 高德地图路径</h5>
                <p><strong>距离:</strong> ${routePlanner.formatDistance(amapRoute.totalDistance)}</p>
                <p><strong>时间:</strong> ${routePlanner.formatTime(amapRoute.totalTime)}</p>
                <p><strong>指令数:</strong> ${amapRoute.instructions.length}</p>
            </div>
            <p style="color: #666;">⚠️ Path算路服务不可用</p>
        `;

    } else if (cppRoute) {
        // 仅C++路径
        comparisonHtml += `
            <div style="border: 1px solid #4CAF50; border-radius: 4px; padding: 10px; margin: 10px 0;">
                <h5 style="margin: 0 0 10px 0; color: #4CAF50;">⚡ Path算路结果</h5>
                <p><strong>距离:</strong> ${cppRouteClient.formatDistance(cppRoute.totalDistance)}</p>
                <p><strong>时间:</strong> ${cppRouteClient.formatTime(cppRoute.totalTime)}</p>
                <p><strong>查询时间:</strong> ${cppRoute.queryTime}ms</p>
            </div>
            <p style="color: #666;">⚠️ 高德算路失败</p>
        `;
    } else {
        comparisonHtml += '<p style="color: #ff4d4f;">❌ 所有算路都失败了</p>';
    }

    // 添加导航指令（如果有高德路径）
    if (amapRoute && amapRoute.instructions && amapRoute.instructions.length > 0) {
        comparisonHtml += '<h4>🧭 导航指令 (高德地图):</h4>';
        comparisonHtml += '<div style="max-height: 200px; overflow-y: auto; border: 1px solid #e8e8e8; padding: 10px; border-radius: 4px;">';
        comparisonHtml += '<ol style="margin: 0; padding-left: 20px;">';

        amapRoute.instructions.forEach((instruction, index) => {
            const stepNumber = index + 1;
            comparisonHtml += `<li style="margin-bottom: 8px; line-height: 1.4;">
                <strong>第${stepNumber}步:</strong> ${instruction.instruction}<br>
                <small style="color: #666;">距离: ${routePlanner.formatDistance(instruction.distance)} | 时间: ${routePlanner.formatTime(instruction.time)}</small>
            </li>`;
        });

        comparisonHtml += '</ol></div>';
    }

    routeInfoDiv.innerHTML = comparisonHtml;
}

/**
 * 清除路径显示
 */
function clearRouteDisplay() {
    if (amapPolyline) {
        map.removeLayer(amapPolyline);
        amapPolyline = null;
    }
    if (cppPolyline) {
        map.removeLayer(cppPolyline);
        cppPolyline = null;
    }
    if (routePolyline) {
        map.removeLayer(routePolyline);
        routePolyline = null;
    }
}
