# #!/bin/bash

# # 构建C++算路服务器脚本

# echo "🔨 构建C++算路服务器"
# echo "===================="

# # 获取脚本所在目录
# SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

# echo "📂 项目根目录: $PROJECT_ROOT"
# echo "📂 工具目录: $SCRIPT_DIR"

# # 检查必要文件是否存在
# if [ ! -f "$SCRIPT_DIR/simple_route_server.cpp" ]; then
#     echo "❌ 找不到 simple_route_server.cpp"
#     exit 1
# fi

# # 创建构建目录
# BUILD_DIR="$PROJECT_ROOT/build_route_server"
# mkdir -p "$BUILD_DIR"

# echo "📁 构建目录: $BUILD_DIR"

# # 进入构建目录
# cd "$BUILD_DIR"

# # 检查是否有CMakeLists.txt
# if [ ! -f "$PROJECT_ROOT/CMakeLists.txt" ]; then
#     echo "❌ 找不到项目根目录的 CMakeLists.txt"
#     echo "💡 尝试手动编译..."
    
#     # 手动编译
#     echo "🔧 手动编译 simple_route_server..."
    
#     # 设置编译参数
#     CXX_FLAGS="-std=c++17 -Wall -Wextra -O2"
#     INCLUDE_DIRS="-I$PROJECT_ROOT/src/path/include -I$PROJECT_ROOT/src/base/include -I$PROJECT_ROOT/include"
    
#     # 查找库文件
#     LIB_DIRS=""
#     if [ -d "$PROJECT_ROOT/build/lib" ]; then
#         LIB_DIRS="-L$PROJECT_ROOT/build/lib"
#     elif [ -d "$PROJECT_ROOT/lib" ]; then
#         LIB_DIRS="-L$PROJECT_ROOT/lib"
#     fi
    
#     LIBS="-lpath_module -lbase_module -lpthread"
    
#     echo "编译命令:"
#     echo "g++ $CXX_FLAGS $INCLUDE_DIRS $LIB_DIRS $SCRIPT_DIR/simple_route_server.cpp -o simple_route_server $LIBS"
    
#     # 执行编译
#     if g++ $CXX_FLAGS $INCLUDE_DIRS $LIB_DIRS "$SCRIPT_DIR/simple_route_server.cpp" -o simple_route_server $LIBS; then
#         echo "✅ 手动编译成功"
#         echo "📍 可执行文件: $BUILD_DIR/simple_route_server"
#     else
#         echo "❌ 手动编译失败"
#         echo ""
#         echo "🔍 可能的解决方案:"
#         echo "1. 检查是否已编译主项目 (运行 cmake 和 make)"
#         echo "2. 检查库文件路径是否正确"
#         echo "3. 检查头文件路径是否正确"
#         echo "4. 安装必要的依赖库"
#         exit 1
#     fi
# else
#     echo "🔧 使用CMake构建..."
    
#     # 使用CMake构建
#     if cmake "$PROJECT_ROOT" -DCMAKE_BUILD_TYPE=Release; then
#         echo "✅ CMake配置成功"
        
#         if make simple_route_server -j$(nproc); then
#             echo "✅ CMake编译成功"
#         else
#             echo "❌ CMake编译失败"
#             exit 1
#         fi
#     else
#         echo "❌ CMake配置失败"
#         exit 1
#     fi
# fi

# # 检查可执行文件
# if [ -f "$BUILD_DIR/simple_route_server" ]; then
#     echo ""
#     echo "🎉 构建完成!"
#     echo "📍 可执行文件: $BUILD_DIR/simple_route_server"
#     echo ""
#     echo "🚀 启动服务器:"
#     echo "cd $BUILD_DIR"
#     echo "./simple_route_server [config_file] [data_dir] [port]"
#     echo ""
#     echo "📋 默认参数:"
#     echo "  config_file: /home/<USER>/dlc/map_engine/src/path/config/path.yaml"
#     echo "  data_dir: /home/<USER>/dlc/map_engine/distribution/data/route"
#     echo "  port: 8080"
#     echo ""
#     echo "💡 示例启动命令:"
#     echo "./simple_route_server"
#     echo "或"
#     echo "./simple_route_server /path/to/config.yaml /path/to/data 8080"
    
#     # 创建启动脚本
#     cat > "$BUILD_DIR/start_server.sh" << 'EOF'
# #!/bin/bash

# # C++算路服务器启动脚本

# echo "🚀 启动C++算路服务器"
# echo "===================="

# # 获取脚本所在目录
# SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# # 默认参数
# CONFIG_FILE="/home/<USER>/dlc/map_engine/src/path/config/path.yaml"
# DATA_DIR="/home/<USER>/dlc/map_engine/distribution/data/route"
# PORT=8080

# # 检查可执行文件
# if [ ! -f "$SCRIPT_DIR/simple_route_server" ]; then
#     echo "❌ 找不到 simple_route_server 可执行文件"
#     echo "请先运行构建脚本: ./build_route_server.sh"
#     exit 1
# fi

# # 检查配置文件
# if [ ! -f "$CONFIG_FILE" ]; then
#     echo "⚠️  配置文件不存在: $CONFIG_FILE"
#     echo "请检查路径或提供正确的配置文件路径"
# fi

# # 检查数据目录
# if [ ! -d "$DATA_DIR" ]; then
#     echo "⚠️  数据目录不存在: $DATA_DIR"
#     echo "请检查路径或提供正确的数据目录路径"
# fi

# echo "📋 启动参数:"
# echo "  配置文件: $CONFIG_FILE"
# echo "  数据目录: $DATA_DIR"
# echo "  端口: $PORT"
# echo ""

# # 启动服务器
# echo "🚀 启动服务器..."
# cd "$SCRIPT_DIR"
# exec ./simple_route_server "$CONFIG_FILE" "$DATA_DIR" "$PORT"
# EOF

#     chmod +x "$BUILD_DIR/start_server.sh"
#     echo ""
#     echo "📜 已创建启动脚本: $BUILD_DIR/start_server.sh"
#     echo "可以直接运行: cd $BUILD_DIR && ./start_server.sh"
    
# else
#     echo "❌ 构建失败，找不到可执行文件"
#     exit 1
# fi

# echo ""
# echo "✅ 构建脚本执行完成"



cd /home/<USER>/dlc/map_engine/build && make -j22