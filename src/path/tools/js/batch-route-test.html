<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量双算路测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 1400px;
            margin: 0 auto;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }
        .section h3 {
            margin-top: 0;
            color: #1890ff;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #f6ffed; border: 1px solid #b7eb8f; color: #389e0d; }
        .error { background: #fff2f0; border: 1px solid #ffccc7; color: #cf1322; }
        .info { background: #e6f7ff; border: 1px solid #91d5ff; color: #0958d9; }
        .warning { background: #fffbe6; border: 1px solid #ffe58f; color: #d48806; }
        
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #40a9ff; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        
        .file-input {
            margin: 10px 0;
            padding: 10px;
            border: 2px dashed #d9d9d9;
            border-radius: 4px;
            text-align: center;
            cursor: pointer;
        }
        .file-input:hover { border-color: #1890ff; }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #1890ff, #40a9ff);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .test-config {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .config-item {
            display: flex;
            flex-direction: column;
        }
        .config-item label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .config-item input, .config-item select {
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 12px;
        }
        .results-table th, .results-table td {
            border: 1px solid #e8e8e8;
            padding: 6px;
            text-align: left;
        }
        .results-table th {
            background: #f5f5f5;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        .results-container {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-card {
            background: #fafafa;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .log-container {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .export-buttons {
            margin: 15px 0;
        }
        
        .route-success { background: #f6ffed; }
        .route-failed { background: #fff2f0; }
        .route-partial { background: #fffbe6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 批量双算路测试系统</h1>
        <p>基于CSV数据文件进行大规模路径规划对比测试</p>

        <!-- 文件上传区域 -->
        <div class="section">
            <h3>📁 数据文件上传</h3>
            <div class="file-input" onclick="document.getElementById('csvFile').click()">
                <input type="file" id="csvFile" accept=".csv,.txt" style="display: none;" onchange="loadCSVFile(event)">
                <p>点击选择CSV文件或拖拽文件到此处</p>
                <small>支持格式：CSV文件，包含经纬度坐标数据</small>
            </div>
            <div id="fileInfo" class="status info" style="display: none;"></div>
        </div>

        <!-- 测试配置 -->
        <div class="section">
            <h3>⚙️ 测试配置</h3>
            <div class="test-config">
                <div class="config-item">
                    <label>测试模式</label>
                    <select id="testMode">
                        <option value="random">随机配对</option>
                        <option value="sequential">顺序配对</option>
                        <option value="nearby">就近配对</option>
                        <option value="cross_city">跨城市测试</option>
                    </select>
                </div>
                <div class="config-item">
                    <label>测试数量</label>
                    <input type="number" id="testCount" value="10" min="1" max="100">
                </div>
                <div class="config-item">
                    <label>并发数量</label>
                    <select id="concurrency">
                        <option value="1">1 (串行)</option>
                        <option value="2">2</option>
                        <option value="3" selected>3</option>
                        <option value="5">5</option>
                    </select>
                </div>
                <div class="config-item">
                    <label>超时时间(秒)</label>
                    <input type="number" id="timeout" value="30" min="10" max="120">
                </div>
                <div class="config-item">
                    <label>最小距离(km)</label>
                    <input type="number" id="minDistance" value="1" min="0.1" step="0.1">
                </div>
                <div class="config-item">
                    <label>最大距离(km)</label>
                    <input type="number" id="maxDistance" value="50" min="1" step="1">
                </div>
            </div>
            <button onclick="startBatchTest()" id="startBtn">开始批量测试</button>
            <button onclick="stopBatchTest()" id="stopBtn" disabled>停止测试</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <!-- 测试进度 -->
        <div class="section">
            <h3>📊 测试进度</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText" class="status info">等待开始测试...</div>
            
            <!-- 实时统计 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="totalTests">0</div>
                    <div class="stat-label">总测试数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="completedTests">0</div>
                    <div class="stat-label">已完成</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="successRate">0%</div>
                    <div class="stat-label">成功率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="avgTime">0ms</div>
                    <div class="stat-label">平均耗时</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="amapSuccess">0</div>
                    <div class="stat-label">高德成功</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="cppSuccess">0</div>
                    <div class="stat-label">C++成功</div>
                </div>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="section">
            <h3>📋 测试结果</h3>
            <div class="export-buttons">
                <button onclick="exportResults('csv')">导出CSV</button>
                <button onclick="exportResults('geojson')">导出GeoJSON</button>
                <button onclick="generateReport()">生成报告</button>
            </div>
            <div class="results-container">
                <table class="results-table" id="resultsTable">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>起点</th>
                            <th>终点</th>
                            <th>距离(km)</th>
                            <th>高德状态</th>
                            <th>高德距离</th>
                            <th>高德时间</th>
                            <th>C++状态</th>
                            <th>C++距离</th>
                            <th>C++时间</th>
                            <th>C++查询时间</th>
                            <th>距离差异</th>
                            <th>时间差异</th>
                            <th>总耗时</th>
                        </tr>
                    </thead>
                    <tbody id="resultsBody">
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 详细日志 -->
        <div class="section">
            <h3>📝 测试日志</h3>
            <button onclick="clearLog()">清除日志</button>
            <div class="log-container" id="testLog">等待测试开始...</div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=f7f401fd4ffb75720cba09ffb7b24a4c&plugin=AMap.Driving,AMap.Walking,AMap.Riding"></script>
    <script src="coordinate-converter.js"></script>
    <script src="route-planner.js"></script>
    <script src="cpp-route-client.js"></script>
    <script src="csv-data-processor.js"></script>
    
    <script>
        // 全局变量
        let csvData = [];
        let testResults = [];
        let isTestRunning = false;
        let testAborted = false;
        let routePlanner = null;
        let cppRouteClient = null;
        let currentTestIndex = 0;
        let testPairs = [];
        let csvProcessor = null;
        
        // 统计数据
        let stats = {
            total: 0,
            completed: 0,
            amapSuccess: 0,
            cppSuccess: 0,
            bothSuccess: 0,
            totalTime: 0
        };
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            log('页面加载完成，开始初始化服务...');
            setTimeout(initializeServices, 1000);
            setupDragAndDrop();
        });
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            console.log(logMessage);
            
            const logElement = document.getElementById('testLog');
            if (logElement) {
                logElement.textContent += logMessage + '\n';
                logElement.scrollTop = logElement.scrollHeight;
            }
        }
        
        function clearLog() {
            const logElement = document.getElementById('testLog');
            if (logElement) {
                logElement.textContent = '日志已清除\n';
            }
        }
        
        async function initializeServices() {
            try {
                log('初始化高德路径规划器...');
                routePlanner = new RoutePlanner();
                await routePlanner.waitForInit();
                log('✅ 高德路径规划器初始化完成');
                
                log('初始化C++算路客户端...');
                cppRouteClient = new CppRouteClient('http://localhost:8080');
                log('✅ C++算路客户端初始化完成');
                
                log('✅ 所有服务初始化完成，可以开始测试');
                
            } catch (error) {
                log(`❌ 服务初始化失败: ${error.message}`);
            }
        }
        
        function setupDragAndDrop() {
            const fileInput = document.querySelector('.file-input');
            
            fileInput.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileInput.style.borderColor = '#1890ff';
            });
            
            fileInput.addEventListener('dragleave', (e) => {
                e.preventDefault();
                fileInput.style.borderColor = '#d9d9d9';
            });
            
            fileInput.addEventListener('drop', (e) => {
                e.preventDefault();
                fileInput.style.borderColor = '#d9d9d9';
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    loadCSVFile({ target: { files: files } });
                }
            });
        }
        
        function loadCSVFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            log(`开始加载文件: ${file.name}`);

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const text = e.target.result;

                    // 使用CSV数据处理器解析文件
                    csvProcessor = new CSVDataProcessor();
                    const result = csvProcessor.parseCSV(text);

                    if (result.success) {
                        csvData = result.data;

                        const fileInfo = document.getElementById('fileInfo');
                        fileInfo.style.display = 'block';
                        fileInfo.className = 'status success';
                        fileInfo.textContent = `✅ 文件加载成功: ${file.name}, 共 ${result.stats.validRecords} 条有效记录 (${result.stats.provinces}个省份, ${result.stats.cities}个城市)`;

                        log(`✅ CSV文件解析完成:`);
                        log(`  - 总记录数: ${result.stats.totalRecords}`);
                        log(`  - 有效记录数: ${result.stats.validRecords}`);
                        log(`  - 省份数: ${result.stats.provinces}`);
                        log(`  - 城市数: ${result.stats.cities}`);
                        log(`  - 区县数: ${result.stats.districts}`);

                    } else {
                        throw new Error(result.error);
                    }

                } catch (error) {
                    log(`❌ 文件解析失败: ${error.message}`);

                    const fileInfo = document.getElementById('fileInfo');
                    fileInfo.style.display = 'block';
                    fileInfo.className = 'status error';
                    fileInfo.textContent = `❌ 文件解析失败: ${error.message}`;
                }
            };

            reader.readAsText(file, 'UTF-8');
        }
        
        function parseCSVData(text) {
            const lines = text.trim().split('\n');
            const headers = lines[0].split(',');

            csvData = [];

            for (let i = 1; i < lines.length; i++) {
                const values = lines[i].split(',');
                if (values.length >= headers.length) {
                    const record = {};
                    headers.forEach((header, index) => {
                        record[header.trim()] = values[index].trim();
                    });

                    // 验证必要字段
                    if (record.CityLon && record.CityLat && record.DistLon && record.DistLat) {
                        csvData.push(record);
                    }
                }
            }

            if (csvData.length === 0) {
                throw new Error('未找到有效的坐标数据');
            }
        }

        function generateTestPairs() {
            const testMode = document.getElementById('testMode').value;
            const testCount = parseInt(document.getElementById('testCount').value);
            const minDistance = parseFloat(document.getElementById('minDistance').value);
            const maxDistance = parseFloat(document.getElementById('maxDistance').value);

            testPairs = [];

            log(`生成测试配对，模式: ${testMode}, 数量: ${testCount}`);

            switch (testMode) {
                case 'random':
                    generateRandomPairs(testCount, minDistance, maxDistance);
                    break;
                case 'sequential':
                    generateSequentialPairs(testCount, minDistance, maxDistance);
                    break;
                case 'nearby':
                    generateNearbyPairs(testCount, minDistance, maxDistance);
                    break;
                case 'cross_city':
                    generateCrossCityPairs(testCount, minDistance, maxDistance);
                    break;
            }

            log(`✅ 生成了 ${testPairs.length} 个测试配对`);
            return testPairs.length > 0;
        }

        function generateRandomPairs(count, minDist, maxDist) {
            const attempts = count * 10; // 最多尝试10倍数量
            let generated = 0;

            for (let i = 0; i < attempts && generated < count; i++) {
                const start = csvData[Math.floor(Math.random() * csvData.length)];
                const end = csvData[Math.floor(Math.random() * csvData.length)];

                if (start === end) continue;

                const distance = calculateDistance(
                    parseFloat(start.DistLon), parseFloat(start.DistLat),
                    parseFloat(end.DistLon), parseFloat(end.DistLat)
                );

                if (distance >= minDist && distance <= maxDist) {
                    testPairs.push({
                        start: {
                            lng: parseFloat(start.DistLon),
                            lat: parseFloat(start.DistLat),
                            name: `${start.ProvName}-${start.CityName}-${start.DistName}`
                        },
                        end: {
                            lng: parseFloat(end.DistLon),
                            lat: parseFloat(end.DistLat),
                            name: `${end.ProvName}-${end.CityName}-${end.DistName}`
                        },
                        distance: distance
                    });
                    generated++;
                }
            }
        }

        function generateSequentialPairs(count, minDist, maxDist) {
            for (let i = 0; i < csvData.length - 1 && testPairs.length < count; i++) {
                const start = csvData[i];
                const end = csvData[i + 1];

                const distance = calculateDistance(
                    parseFloat(start.DistLon), parseFloat(start.DistLat),
                    parseFloat(end.DistLon), parseFloat(end.DistLat)
                );

                if (distance >= minDist && distance <= maxDist) {
                    testPairs.push({
                        start: {
                            lng: parseFloat(start.DistLon),
                            lat: parseFloat(start.DistLat),
                            name: `${start.ProvName}-${start.CityName}-${start.DistName}`
                        },
                        end: {
                            lng: parseFloat(end.DistLon),
                            lat: parseFloat(end.DistLat),
                            name: `${end.ProvName}-${end.CityName}-${end.DistName}`
                        },
                        distance: distance
                    });
                }
            }
        }

        function generateNearbyPairs(count, minDist, maxDist) {
            // 按省市分组
            const groups = {};
            csvData.forEach(record => {
                const key = `${record.ProvName}-${record.CityName}`;
                if (!groups[key]) groups[key] = [];
                groups[key].push(record);
            });

            // 在同一城市内生成配对
            Object.values(groups).forEach(group => {
                if (group.length < 2 || testPairs.length >= count) return;

                for (let i = 0; i < group.length - 1 && testPairs.length < count; i++) {
                    for (let j = i + 1; j < group.length && testPairs.length < count; j++) {
                        const start = group[i];
                        const end = group[j];

                        const distance = calculateDistance(
                            parseFloat(start.DistLon), parseFloat(start.DistLat),
                            parseFloat(end.DistLon), parseFloat(end.DistLat)
                        );

                        if (distance >= minDist && distance <= maxDist) {
                            testPairs.push({
                                start: {
                                    lng: parseFloat(start.DistLon),
                                    lat: parseFloat(start.DistLat),
                                    name: `${start.ProvName}-${start.CityName}-${start.DistName}`
                                },
                                end: {
                                    lng: parseFloat(end.DistLon),
                                    lat: parseFloat(end.DistLat),
                                    name: `${end.ProvName}-${end.CityName}-${end.DistName}`
                                },
                                distance: distance
                            });
                        }
                    }
                }
            });
        }

        function generateCrossCityPairs(count, minDist, maxDist) {
            const attempts = count * 10;
            let generated = 0;

            for (let i = 0; i < attempts && generated < count; i++) {
                const start = csvData[Math.floor(Math.random() * csvData.length)];
                const end = csvData[Math.floor(Math.random() * csvData.length)];

                // 确保是不同城市
                if (start.CityName === end.CityName) continue;

                const distance = calculateDistance(
                    parseFloat(start.DistLon), parseFloat(start.DistLat),
                    parseFloat(end.DistLon), parseFloat(end.DistLat)
                );

                if (distance >= minDist && distance <= maxDist) {
                    testPairs.push({
                        start: {
                            lng: parseFloat(start.DistLon),
                            lat: parseFloat(start.DistLat),
                            name: `${start.ProvName}-${start.CityName}-${start.DistName}`
                        },
                        end: {
                            lng: parseFloat(end.DistLon),
                            lat: parseFloat(end.DistLat),
                            name: `${end.ProvName}-${end.CityName}-${end.DistName}`
                        },
                        distance: distance
                    });
                    generated++;
                }
            }
        }

        function calculateDistance(lng1, lat1, lng2, lat2) {
            const R = 6371; // 地球半径(km)
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        async function startBatchTest() {
            if (csvData.length === 0) {
                alert('请先上传CSV数据文件');
                return;
            }

            if (!routePlanner || !cppRouteClient) {
                alert('服务未初始化完成，请稍后再试');
                return;
            }

            // 生成测试配对
            if (!generateTestPairs()) {
                alert('无法生成有效的测试配对，请调整测试参数');
                return;
            }

            // 重置状态
            isTestRunning = true;
            testAborted = false;
            currentTestIndex = 0;
            testResults = [];
            stats = {
                total: testPairs.length,
                completed: 0,
                amapSuccess: 0,
                cppSuccess: 0,
                bothSuccess: 0,
                totalTime: 0
            };

            // 更新UI
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            updateProgress(0, `开始批量测试，共 ${testPairs.length} 个测试用例`);
            clearResultsTable();

            log(`🚀 开始批量测试，共 ${testPairs.length} 个测试用例`);

            // 获取并发数
            const concurrency = parseInt(document.getElementById('concurrency').value);

            try {
                await runBatchTestWithConcurrency(concurrency);

                if (!testAborted) {
                    log('🎉 批量测试完成！');
                    updateProgress(100, '批量测试完成');
                    generateSummaryReport();
                }
            } catch (error) {
                log(`❌ 批量测试失败: ${error.message}`);
                updateProgress(0, `测试失败: ${error.message}`);
            } finally {
                isTestRunning = false;
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
            }
        }

        async function runBatchTestWithConcurrency(concurrency) {
            const chunks = [];
            for (let i = 0; i < testPairs.length; i += concurrency) {
                chunks.push(testPairs.slice(i, i + concurrency));
            }

            for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
                if (testAborted) break;

                const chunk = chunks[chunkIndex];
                const promises = chunk.map((pair, index) =>
                    runSingleTest(chunkIndex * concurrency + index, pair)
                );

                await Promise.allSettled(promises);

                // 更新进度
                const progress = ((chunkIndex + 1) / chunks.length) * 100;
                updateProgress(progress, `已完成 ${stats.completed}/${stats.total} 个测试`);
            }
        }

        async function runSingleTest(index, pair) {
            if (testAborted) return;

            const startTime = Date.now();
            const timeout = parseInt(document.getElementById('timeout').value) * 1000;

            log(`测试 ${index + 1}/${testPairs.length}: ${pair.start.name} → ${pair.end.name}`);

            const result = {
                index: index + 1,
                startPoint: pair.start,
                endPoint: pair.end,
                directDistance: pair.distance,
                amapStatus: 'pending',
                amapRoute: null,
                cppStatus: 'pending',
                cppRoute: null,
                totalTime: 0,
                timestamp: new Date().toISOString()
            };

            try {
                // 坐标转换
                // const startGCJ02 = CoordinateConverter.wgs84ToGcj02(pair.start.lng, pair.start.lat);
                // const endGCJ02 = CoordinateConverter.wgs84ToGcj02(pair.end.lng, pair.end.lat);

                // 已经是Gcj， 不需要坐标转换。
                const startGCJ02 = CoordinateConverter.gcj02Togcj02(pair.start.lng, pair.start.lat);
                const endGCJ02 = CoordinateConverter.gcj02Togcj02(pair.end.lng, pair.end.lat);

                // 并行执行双算路
                const promises = [];

                // 高德算路
                promises.push(
                    Promise.race([
                        routePlanner.calculateRoute(startGCJ02, endGCJ02, [])
                            .then(route => ({ source: 'amap', route, success: true }))
                            .catch(error => ({ source: 'amap', error, success: false })),
                        new Promise((_, reject) =>
                            setTimeout(() => reject(new Error('timeout')), timeout)
                        )
                    ])
                );

                // C++算路
                promises.push(
                    Promise.race([
                        cppRouteClient.calculateRoute(startGCJ02[0], startGCJ02[1], endGCJ02[0], endGCJ02[1])
                            .then(route => ({ source: 'cpp', route, success: true }))
                            .catch(error => ({ source: 'cpp', error, success: false })),
                        new Promise((_, reject) =>
                            setTimeout(() => reject(new Error('timeout')), timeout)
                        )
                    ])
                );

                const results = await Promise.allSettled(promises);

                // 处理结果
                results.forEach(promiseResult => {
                    if (promiseResult.status === 'fulfilled') {
                        const { source, route, success, error } = promiseResult.value;

                        if (source === 'amap') {
                            if (success) {
                                result.amapStatus = 'success';
                                result.amapRoute = route;
                                stats.amapSuccess++;
                            } else {
                                result.amapStatus = 'failed';
                                result.amapError = error.message;
                            }
                        } else if (source === 'cpp') {
                            if (success) {
                                result.cppStatus = 'success';
                                result.cppRoute = route;
                                stats.cppSuccess++;
                            } else {
                                result.cppStatus = 'failed';
                                result.cppError = error.message;
                            }
                        }
                    } else {
                        // Promise rejected (timeout)
                        const source = promiseResult.reason.message.includes('amap') ? 'amap' : 'cpp';
                        if (source === 'amap') {
                            result.amapStatus = 'timeout';
                        } else {
                            result.cppStatus = 'timeout';
                        }
                    }
                });

                // 统计成功率
                if (result.amapStatus === 'success' && result.cppStatus === 'success') {
                    stats.bothSuccess++;
                }

            } catch (error) {
                log(`测试 ${index + 1} 执行失败: ${error.message}`);
                result.amapStatus = 'error';
                result.cppStatus = 'error';
                result.error = error.message;
            }

            result.totalTime = Date.now() - startTime;
            stats.totalTime += result.totalTime;
            stats.completed++;

            testResults.push(result);
            addResultToTable(result);
            updateStats();

            log(`测试 ${index + 1} 完成: 高德=${result.amapStatus}, C++=${result.cppStatus}, 耗时=${result.totalTime}ms`);
        }

        function stopBatchTest() {
            testAborted = true;
            log('⏹️ 用户停止了批量测试');
            updateProgress(0, '测试已停止');
        }

        function updateProgress(percent, message) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = message;
            document.getElementById('progressText').className = 'status ' + (percent === 100 ? 'success' : 'info');
        }

        function updateStats() {
            document.getElementById('totalTests').textContent = stats.total;
            document.getElementById('completedTests').textContent = stats.completed;

            const successRate = stats.completed > 0 ?
                ((stats.bothSuccess / stats.completed) * 100).toFixed(1) : 0;
            document.getElementById('successRate').textContent = successRate + '%';

            const avgTime = stats.completed > 0 ?
                Math.round(stats.totalTime / stats.completed) : 0;
            document.getElementById('avgTime').textContent = avgTime + 'ms';

            document.getElementById('amapSuccess').textContent = stats.amapSuccess;
            document.getElementById('cppSuccess').textContent = stats.cppSuccess;
        }

        function clearResultsTable() {
            document.getElementById('resultsBody').innerHTML = '';
        }

        function addResultToTable(result) {
            const tbody = document.getElementById('resultsBody');
            const row = document.createElement('tr');

            // 根据结果状态设置行样式
            if (result.amapStatus === 'success' && result.cppStatus === 'success') {
                row.className = 'route-success';
            } else if (result.amapStatus === 'failed' || result.cppStatus === 'failed') {
                row.className = 'route-failed';
            } else {
                row.className = 'route-partial';
            }

            // 计算差异
            let distanceDiff = '-';
            let timeDiff = '-';

            if (result.amapRoute && result.cppRoute) {
                const amapDist = result.amapRoute.totalDistance / 1000; // 转换为km
                const cppDist = result.cppRoute.totalDistance / 1000;
                const amapTime = result.amapRoute.totalTime / 60; // 转换为分钟
                const cppTime = result.cppRoute.totalTime / 60;

                distanceDiff = ((Math.abs(amapDist - cppDist) / Math.min(amapDist, cppDist)) * 100).toFixed(1) + '%';
                timeDiff = ((Math.abs(amapTime - cppTime) / Math.min(amapTime, cppTime)) * 100).toFixed(1) + '%';
            }

            row.innerHTML = `
                <td>${result.index}</td>
                <td title="${result.startPoint.name}">${result.startPoint.name.substring(0, 20)}...</td>
                <td title="${result.endPoint.name}">${result.endPoint.name.substring(0, 20)}...</td>
                <td>${result.directDistance.toFixed(2)}</td>
                <td class="${result.amapStatus}">${getStatusText(result.amapStatus)}</td>
                <td>${result.amapRoute ? (result.amapRoute.totalDistance / 1000).toFixed(2) + 'km' : '-'}</td>
                <td>${result.amapRoute ? (result.amapRoute.totalTime / 60).toFixed(1) + 'min' : '-'}</td>
                <td class="${result.cppStatus}">${getStatusText(result.cppStatus)}</td>
                <td>${result.cppRoute ? (result.cppRoute.totalDistance / 1000).toFixed(2) + 'km' : '-'}</td>
                <td>${result.cppRoute ? (result.cppRoute.totalTime / 60).toFixed(1) + 'min' : '-'}</td>
                <td>${result.cppRoute ? result.cppRoute.queryTime + 'ms' : '-'}</td>
                <td>${distanceDiff}</td>
                <td>${timeDiff}</td>
                <td>${result.totalTime}ms</td>
            `;

            tbody.appendChild(row);

            // 滚动到最新行
            row.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        function getStatusText(status) {
            const statusMap = {
                'success': '✅ 成功',
                'failed': '❌ 失败',
                'timeout': '⏰ 超时',
                'error': '💥 错误',
                'pending': '⏳ 等待'
            };
            return statusMap[status] || status;
        }

        function clearResults() {
            testResults = [];
            clearResultsTable();
            stats = {
                total: 0,
                completed: 0,
                amapSuccess: 0,
                cppSuccess: 0,
                bothSuccess: 0,
                totalTime: 0
            };
            updateStats();
            updateProgress(0, '结果已清除');
            log('🗑️ 测试结果已清除');
        }

        function exportResults(format) {
            if (testResults.length === 0) {
                alert('没有测试结果可导出');
                return;
            }

            let content = '';
            let filename = '';
            let mimeType = '';

            if (format === 'csv') {
                content = generateCSVContent();
                filename = `batch_route_test_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
                mimeType = 'text/csv';
            } else if (format === 'geojson') {
                content = generateGeoJSONContent();
                filename = `batch_route_test_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.geojson`;
                mimeType = 'application/geo+json';
            }

            downloadFile(content, filename, mimeType);
            log(`📥 导出${format.toUpperCase()}文件: ${filename}`);
        }

        function generateCSVContent() {
            const headers = [
                '序号', '起点名称', '起点经度', '起点纬度', '终点名称', '终点经度', '终点纬度',
                '直线距离(km)', '高德状态', '高德距离(km)', '高德时间(min)', '高德路径点数',
                'C++状态', 'C++距离(km)', 'C++时间(min)', 'C++查询时间(ms)', 'C++路径点数',
                '距离差异(%)', '时间差异(%)', '总耗时(ms)', '测试时间'
            ];

            let csv = headers.join(',') + '\n';

            testResults.forEach(result => {
                const row = [
                    result.index,
                    `"${result.startPoint.name}"`,
                    result.startPoint.lng,
                    result.startPoint.lat,
                    `"${result.endPoint.name}"`,
                    result.endPoint.lng,
                    result.endPoint.lat,
                    result.directDistance.toFixed(2),
                    result.amapStatus,
                    result.amapRoute ? (result.amapRoute.totalDistance / 1000).toFixed(2) : '',
                    result.amapRoute ? (result.amapRoute.totalTime / 60).toFixed(1) : '',
                    result.amapRoute ? result.amapRoute.coordinates.length : '',
                    result.cppStatus,
                    result.cppRoute ? (result.cppRoute.totalDistance / 1000).toFixed(2) : '',
                    result.cppRoute ? (result.cppRoute.totalTime / 60).toFixed(1) : '',
                    result.cppRoute ? result.cppRoute.queryTime : '',
                    result.cppRoute ? result.cppRoute.coordinates.length : '',
                    calculateDifference(result, 'distance'),
                    calculateDifference(result, 'time'),
                    result.totalTime,
                    result.timestamp
                ];
                csv += row.join(',') + '\n';
            });

            return csv;
        }

        function generateGeoJSONContent() {
            const geojson = {
                type: "FeatureCollection",
                name: "batch_route_test_results",
                crs: {
                    type: "name",
                    properties: {
                        name: "urn:ogc:def:crs:OGC:1.3:CRS84"
                    }
                },
                features: []
            };

            testResults.forEach(result => {
                // 添加起点
                geojson.features.push({
                    type: "Feature",
                    properties: {
                        type: "start_point",
                        test_id: result.index,
                        name: result.startPoint.name,
                        test_status: `${result.amapStatus}_${result.cppStatus}`,
                        direct_distance_km: parseFloat(result.directDistance.toFixed(2)),
                        amap_status: result.amapStatus,
                        cpp_status: result.cppStatus,
                        total_time_ms: result.totalTime,
                        timestamp: result.timestamp
                    },
                    geometry: {
                        type: "Point",
                        coordinates: [result.startPoint.lng, result.startPoint.lat]
                    }
                });

                // 添加终点
                geojson.features.push({
                    type: "Feature",
                    properties: {
                        type: "end_point",
                        test_id: result.index,
                        name: result.endPoint.name,
                        test_status: `${result.amapStatus}_${result.cppStatus}`,
                        direct_distance_km: parseFloat(result.directDistance.toFixed(2)),
                        amap_status: result.amapStatus,
                        cpp_status: result.cppStatus,
                        total_time_ms: result.totalTime,
                        timestamp: result.timestamp
                    },
                    geometry: {
                        type: "Point",
                        coordinates: [result.endPoint.lng, result.endPoint.lat]
                    }
                });

                // 添加直线连接
                geojson.features.push({
                    type: "Feature",
                    properties: {
                        type: "direct_line",
                        test_id: result.index,
                        start_name: result.startPoint.name,
                        end_name: result.endPoint.name,
                        distance_km: parseFloat(result.directDistance.toFixed(2)),
                        test_status: `${result.amapStatus}_${result.cppStatus}`,
                        amap_status: result.amapStatus,
                        cpp_status: result.cppStatus
                    },
                    geometry: {
                        type: "LineString",
                        coordinates: [
                            [result.startPoint.lng, result.startPoint.lat],
                            [result.endPoint.lng, result.endPoint.lat]
                        ]
                    }
                });

                // 添加高德路径
                if (result.amapRoute && result.amapRoute.coordinates && result.amapRoute.coordinates.length > 0) {
                    geojson.features.push({
                        type: "Feature",
                        properties: {
                            type: "amap_route",
                            test_id: result.index,
                            start_name: result.startPoint.name,
                            end_name: result.endPoint.name,
                            route_source: "amap",
                            distance_km: parseFloat((result.amapRoute.totalDistance / 1000).toFixed(2)),
                            time_min: parseFloat((result.amapRoute.totalTime / 60).toFixed(1)),
                            point_count: result.amapRoute.coordinates.length,
                            status: result.amapStatus,
                            route_type: result.amapRoute.type || "driving"
                        },
                        geometry: {
                            type: "LineString",
                            coordinates: result.amapRoute.coordinates
                        }
                    });
                }

                // 添加C++路径
                if (result.cppRoute && result.cppRoute.coordinates && result.cppRoute.coordinates.length > 0) {
                    geojson.features.push({
                        type: "Feature",
                        properties: {
                            type: "cpp_route",
                            test_id: result.index,
                            start_name: result.startPoint.name,
                            end_name: result.endPoint.name,
                            route_source: "cpp",
                            distance_km: parseFloat((result.cppRoute.totalDistance / 1000).toFixed(2)),
                            time_min: parseFloat((result.cppRoute.totalTime / 60).toFixed(1)),
                            query_time_ms: result.cppRoute.queryTime,
                            point_count: result.cppRoute.coordinates.length,
                            status: result.cppStatus,
                            route_type: "driving"
                        },
                        geometry: {
                            type: "LineString",
                            coordinates: result.cppRoute.coordinates
                        }
                    });
                }

                // 添加对比分析（如果两个路径都存在）
                if (result.amapRoute && result.cppRoute) {
                    const distanceDiff = calculateDifference(result, 'distance');
                    const timeDiff = calculateDifference(result, 'time');

                    geojson.features.push({
                        type: "Feature",
                        properties: {
                            type: "comparison_analysis",
                            test_id: result.index,
                            start_name: result.startPoint.name,
                            end_name: result.endPoint.name,
                            amap_distance_km: parseFloat((result.amapRoute.totalDistance / 1000).toFixed(2)),
                            cpp_distance_km: parseFloat((result.cppRoute.totalDistance / 1000).toFixed(2)),
                            distance_diff_percent: parseFloat(distanceDiff) || 0,
                            amap_time_min: parseFloat((result.amapRoute.totalTime / 60).toFixed(1)),
                            cpp_time_min: parseFloat((result.cppRoute.totalTime / 60).toFixed(1)),
                            time_diff_percent: parseFloat(timeDiff) || 0,
                            cpp_query_time_ms: result.cppRoute.queryTime,
                            amap_points: result.amapRoute.coordinates.length,
                            cpp_points: result.cppRoute.coordinates.length,
                            both_success: true
                        },
                        geometry: {
                            type: "Point",
                            coordinates: [
                                (result.startPoint.lng + result.endPoint.lng) / 2,
                                (result.startPoint.lat + result.endPoint.lat) / 2
                            ]
                        }
                    });
                }
            });

            return JSON.stringify(geojson, null, 2);
        }

        function calculateDifference(result, type) {
            if (!result.amapRoute || !result.cppRoute) return '';

            if (type === 'distance') {
                const amapDist = result.amapRoute.totalDistance / 1000;
                const cppDist = result.cppRoute.totalDistance / 1000;
                return ((Math.abs(amapDist - cppDist) / Math.min(amapDist, cppDist)) * 100).toFixed(1);
            } else if (type === 'time') {
                const amapTime = result.amapRoute.totalTime / 60;
                const cppTime = result.cppRoute.totalTime / 60;
                return ((Math.abs(amapTime - cppTime) / Math.min(amapTime, cppTime)) * 100).toFixed(1);
            }

            return '';
        }

        function downloadFile(content, filename, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function generateReport() {
            if (testResults.length === 0) {
                alert('没有测试结果可生成报告');
                return;
            }

            const report = generateSummaryReport();
            const filename = `batch_route_report_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.html`;
            downloadFile(report, filename, 'text/html');
            log(`📊 生成测试报告: ${filename}`);
        }

        function generateSummaryReport() {
            const successfulTests = testResults.filter(r => r.amapStatus === 'success' && r.cppStatus === 'success');

            let avgDistanceDiff = 0;
            let avgTimeDiff = 0;
            let avgAmapTime = 0;
            let avgCppTime = 0;
            let avgCppQueryTime = 0;

            if (successfulTests.length > 0) {
                successfulTests.forEach(result => {
                    const amapDist = result.amapRoute.totalDistance / 1000;
                    const cppDist = result.cppRoute.totalDistance / 1000;
                    const amapTime = result.amapRoute.totalTime / 60;
                    const cppTime = result.cppRoute.totalTime / 60;

                    avgDistanceDiff += Math.abs(amapDist - cppDist) / Math.min(amapDist, cppDist) * 100;
                    avgTimeDiff += Math.abs(amapTime - cppTime) / Math.min(amapTime, cppTime) * 100;
                    avgAmapTime += amapTime;
                    avgCppTime += cppTime;
                    avgCppQueryTime += result.cppRoute.queryTime;
                });

                avgDistanceDiff /= successfulTests.length;
                avgTimeDiff /= successfulTests.length;
                avgAmapTime /= successfulTests.length;
                avgCppTime /= successfulTests.length;
                avgCppQueryTime /= successfulTests.length;
            }

            const reportHTML = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>批量双算路测试报告</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 40px; }
                        .header { text-align: center; margin-bottom: 40px; }
                        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
                        .stat-card { background: #f5f5f5; padding: 20px; border-radius: 8px; text-align: center; }
                        .stat-value { font-size: 32px; font-weight: bold; color: #1890ff; }
                        .stat-label { font-size: 14px; color: #666; margin-top: 10px; }
                        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
                        th { background: #f5f5f5; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>批量双算路测试报告</h1>
                        <p>生成时间: ${new Date().toLocaleString()}</p>
                    </div>

                    <div class="stats">
                        <div class="stat-card">
                            <div class="stat-value">${stats.total}</div>
                            <div class="stat-label">总测试数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${stats.completed}</div>
                            <div class="stat-label">已完成</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${((stats.bothSuccess / stats.completed) * 100).toFixed(1)}%</div>
                            <div class="stat-label">双算路成功率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${avgDistanceDiff.toFixed(1)}%</div>
                            <div class="stat-label">平均距离差异</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${avgTimeDiff.toFixed(1)}%</div>
                            <div class="stat-label">平均时间差异</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${avgCppQueryTime.toFixed(0)}ms</div>
                            <div class="stat-label">C++平均查询时间</div>
                        </div>
                    </div>

                    <h2>详细统计</h2>
                    <table>
                        <tr><th>项目</th><th>高德地图</th><th>C++算路</th><th>对比</th></tr>
                        <tr><td>成功次数</td><td>${stats.amapSuccess}</td><td>${stats.cppSuccess}</td><td>-</td></tr>
                        <tr><td>成功率</td><td>${((stats.amapSuccess / stats.completed) * 100).toFixed(1)}%</td><td>${((stats.cppSuccess / stats.completed) * 100).toFixed(1)}%</td><td>-</td></tr>
                        <tr><td>平均规划时间</td><td>${avgAmapTime.toFixed(1)}分钟</td><td>${avgCppTime.toFixed(1)}分钟</td><td>${avgTimeDiff.toFixed(1)}%差异</td></tr>
                        <tr><td>平均查询时间</td><td>-</td><td>${avgCppQueryTime.toFixed(0)}ms</td><td>-</td></tr>
                    </table>
                </body>
                </html>
            `;

            return reportHTML;
        }
    </script>
</body>
</html>
