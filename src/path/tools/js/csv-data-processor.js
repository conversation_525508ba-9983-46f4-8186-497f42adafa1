/**
 * CSV数据处理器
 * 专门用于处理route_test_input.txt等地理坐标数据文件
 */
class CSVDataProcessor {
    constructor() {
        this.data = [];
        this.headers = [];
        this.stats = {
            totalRecords: 0,
            validRecords: 0,
            provinces: new Set(),
            cities: new Set(),
            districts: new Set()
        };
    }

    /**
     * 解析CSV文件内容
     * @param {string} csvText - CSV文件文本内容
     * @returns {Object} 解析结果
     */
    parseCSV(csvText) {
        try {
            const lines = csvText.trim().split('\n');
            if (lines.length < 2) {
                throw new Error('CSV文件格式错误：至少需要标题行和一行数据');
            }

            // 解析标题行
            this.headers = lines[0].split(',').map(header => header.trim());
            console.log('CSV标题行:', this.headers);

            // 验证必要的列
            const requiredColumns = ['ProvName', 'CityName', 'DistName', 'CityLon', 'CityLat', 'DistLon', 'DistLat'];
            const missingColumns = requiredColumns.filter(col => !this.headers.includes(col));
            
            if (missingColumns.length > 0) {
                throw new Error(`缺少必要的列: ${missingColumns.join(', ')}`);
            }

            // 解析数据行
            this.data = [];
            this.stats = {
                totalRecords: 0,
                validRecords: 0,
                provinces: new Set(),
                cities: new Set(),
                districts: new Set()
            };

            for (let i = 1; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue; // 跳过空行

                const values = this.parseCSVLine(line);
                if (values.length !== this.headers.length) {
                    console.warn(`第${i + 1}行数据列数不匹配，跳过: ${line}`);
                    continue;
                }

                const record = {};
                this.headers.forEach((header, index) => {
                    record[header] = values[index];
                });

                this.stats.totalRecords++;

                // 验证数据有效性
                if (this.validateRecord(record)) {
                    this.data.push(record);
                    this.stats.validRecords++;
                    
                    // 收集统计信息
                    this.stats.provinces.add(record.ProvName);
                    this.stats.cities.add(`${record.ProvName}-${record.CityName}`);
                    this.stats.districts.add(`${record.ProvName}-${record.CityName}-${record.DistName}`);
                }
            }

            console.log('CSV解析完成:', {
                总记录数: this.stats.totalRecords,
                有效记录数: this.stats.validRecords,
                省份数: this.stats.provinces.size,
                城市数: this.stats.cities.size,
                区县数: this.stats.districts.size
            });

            return {
                success: true,
                data: this.data,
                stats: {
                    totalRecords: this.stats.totalRecords,
                    validRecords: this.stats.validRecords,
                    provinces: this.stats.provinces.size,
                    cities: this.stats.cities.size,
                    districts: this.stats.districts.size
                }
            };

        } catch (error) {
            console.error('CSV解析失败:', error);
            return {
                success: false,
                error: error.message,
                data: [],
                stats: null
            };
        }
    }

    /**
     * 解析CSV行，处理引号和逗号
     * @param {string} line - CSV行
     * @returns {Array} 解析后的值数组
     */
    parseCSVLine(line) {
        const values = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"') {
                if (inQuotes && line[i + 1] === '"') {
                    // 转义的引号
                    current += '"';
                    i++; // 跳过下一个引号
                } else {
                    // 切换引号状态
                    inQuotes = !inQuotes;
                }
            } else if (char === ',' && !inQuotes) {
                // 字段分隔符
                values.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        
        // 添加最后一个字段
        values.push(current.trim());
        
        return values;
    }

    /**
     * 验证记录的有效性
     * @param {Object} record - 数据记录
     * @returns {boolean} 是否有效
     */
    validateRecord(record) {
        // 检查必要字段是否存在
        const requiredFields = ['ProvName', 'CityName', 'DistName', 'CityLon', 'CityLat', 'DistLon', 'DistLat'];
        for (const field of requiredFields) {
            if (!record[field] || record[field].trim() === '') {
                return false;
            }
        }

        // 验证坐标格式
        const cityLon = parseFloat(record.CityLon);
        const cityLat = parseFloat(record.CityLat);
        const distLon = parseFloat(record.DistLon);
        const distLat = parseFloat(record.DistLat);

        // 检查坐标是否为有效数字
        if (isNaN(cityLon) || isNaN(cityLat) || isNaN(distLon) || isNaN(distLat)) {
            return false;
        }

        // 检查坐标范围（中国境内）
        if (cityLon < 70 || cityLon > 140 || cityLat < 10 || cityLat > 55 ||
            distLon < 70 || distLon > 140 || distLat < 10 || distLat > 55) {
            return false;
        }

        return true;
    }

    /**
     * 获取所有数据
     * @returns {Array} 数据数组
     */
    getData() {
        return this.data;
    }

    /**
     * 按省份分组数据
     * @returns {Object} 按省份分组的数据
     */
    getDataByProvince() {
        const grouped = {};
        this.data.forEach(record => {
            const province = record.ProvName;
            if (!grouped[province]) {
                grouped[province] = [];
            }
            grouped[province].push(record);
        });
        return grouped;
    }

    /**
     * 按城市分组数据
     * @returns {Object} 按城市分组的数据
     */
    getDataByCity() {
        const grouped = {};
        this.data.forEach(record => {
            const city = `${record.ProvName}-${record.CityName}`;
            if (!grouped[city]) {
                grouped[city] = [];
            }
            grouped[city].push(record);
        });
        return grouped;
    }

    /**
     * 获取指定省份的数据
     * @param {string} provinceName - 省份名称
     * @returns {Array} 该省份的数据
     */
    getProvinceData(provinceName) {
        return this.data.filter(record => record.ProvName === provinceName);
    }

    /**
     * 获取指定城市的数据
     * @param {string} provinceName - 省份名称
     * @param {string} cityName - 城市名称
     * @returns {Array} 该城市的数据
     */
    getCityData(provinceName, cityName) {
        return this.data.filter(record => 
            record.ProvName === provinceName && record.CityName === cityName
        );
    }

    /**
     * 随机获取指定数量的记录
     * @param {number} count - 需要的记录数量
     * @returns {Array} 随机记录数组
     */
    getRandomRecords(count) {
        if (count >= this.data.length) {
            return [...this.data];
        }

        const shuffled = [...this.data];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }

        return shuffled.slice(0, count);
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            totalRecords: this.stats.totalRecords,
            validRecords: this.stats.validRecords,
            provinces: Array.from(this.stats.provinces),
            cities: Array.from(this.stats.cities),
            districts: Array.from(this.stats.districts)
        };
    }

    /**
     * 计算两点间距离
     * @param {number} lng1 - 起点经度
     * @param {number} lat1 - 起点纬度
     * @param {number} lng2 - 终点经度
     * @param {number} lat2 - 终点纬度
     * @returns {number} 距离(公里)
     */
    calculateDistance(lng1, lat1, lng2, lat2) {
        const R = 6371; // 地球半径(km)
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    /**
     * 查找指定距离范围内的记录对
     * @param {number} minDistance - 最小距离(km)
     * @param {number} maxDistance - 最大距离(km)
     * @param {number} maxPairs - 最大配对数量
     * @returns {Array} 符合条件的记录对
     */
    findRecordPairsInRange(minDistance, maxDistance, maxPairs = 100) {
        const pairs = [];
        const dataLength = this.data.length;
        
        for (let i = 0; i < dataLength && pairs.length < maxPairs; i++) {
            for (let j = i + 1; j < dataLength && pairs.length < maxPairs; j++) {
                const record1 = this.data[i];
                const record2 = this.data[j];
                
                const distance = this.calculateDistance(
                    parseFloat(record1.DistLon), parseFloat(record1.DistLat),
                    parseFloat(record2.DistLon), parseFloat(record2.DistLat)
                );
                
                if (distance >= minDistance && distance <= maxDistance) {
                    pairs.push({
                        start: record1,
                        end: record2,
                        distance: distance
                    });
                }
            }
        }
        
        return pairs;
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CSVDataProcessor;
}
