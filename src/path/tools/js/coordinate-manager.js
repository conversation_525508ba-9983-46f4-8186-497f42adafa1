/**
 * 坐标管理功能
 * 包括历史记录、收藏夹、城市坐标等功能
 */

class CoordinateManager {
    constructor() {
        this.history = [];
        this.favorites = [];
        this.maxHistorySize = 20;
        this.currentCityTarget = 'start'; // 'start' or 'end'
        
        this.loadFromStorage();
        this.initializeUI();
    }

    /**
     * 从本地存储加载数据
     */
    loadFromStorage() {
        try {
            const historyData = localStorage.getItem('coordinate_history');
            if (historyData) {
                this.history = JSON.parse(historyData);
            }

            const favoritesData = localStorage.getItem('coordinate_favorites');
            if (favoritesData) {
                this.favorites = JSON.parse(favoritesData);
            }
        } catch (error) {
            console.error('加载坐标数据失败:', error);
        }
    }

    /**
     * 保存到本地存储
     */
    saveToStorage() {
        try {
            localStorage.setItem('coordinate_history', JSON.stringify(this.history));
            localStorage.setItem('coordinate_favorites', JSON.stringify(this.favorites));
        } catch (error) {
            console.error('保存坐标数据失败:', error);
        }
    }

    /**
     * 初始化UI
     */
    initializeUI() {
        this.updateHistoryDisplay();
        this.updateFavoritesDisplay();
    }

    /**
     * 添加坐标到历史记录
     */
    addToHistory(lng, lat, name = '', note = '') {
        const coordinate = {
            id: Date.now(),
            lng: parseFloat(lng),
            lat: parseFloat(lat),
            name: name || `坐标点 ${this.history.length + 1}`,
            note: note,
            timestamp: new Date().toISOString(),
            displayTime: new Date().toLocaleString('zh-CN')
        };

        // 检查是否已存在相同坐标
        const exists = this.history.find(item => 
            Math.abs(item.lng - coordinate.lng) < 0.000001 && 
            Math.abs(item.lat - coordinate.lat) < 0.000001
        );

        if (!exists) {
            this.history.unshift(coordinate);
            
            // 限制历史记录数量
            if (this.history.length > this.maxHistorySize) {
                this.history = this.history.slice(0, this.maxHistorySize);
            }
            
            this.saveToStorage();
            this.updateHistoryDisplay();
        }
    }

    /**
     * 添加坐标到收藏夹
     */
    addToFavorites(lng, lat, name, note = '') {
        if (!name || !name.trim()) {
            updateStatus('请输入坐标名称', 'error');
            return false;
        }

        const coordinate = {
            id: Date.now(),
            lng: parseFloat(lng),
            lat: parseFloat(lat),
            name: name.trim(),
            note: note.trim(),
            timestamp: new Date().toISOString(),
            displayTime: new Date().toLocaleString('zh-CN')
        };

        // 检查是否已存在相同名称
        const nameExists = this.favorites.find(item => item.name === coordinate.name);
        if (nameExists) {
            if (!confirm(`收藏夹中已存在名称"${coordinate.name}"，是否覆盖？`)) {
                return false;
            }
            this.removeFromFavorites(nameExists.id);
        }

        this.favorites.unshift(coordinate);
        this.saveToStorage();
        this.updateFavoritesDisplay();
        
        updateStatus(`已添加收藏: ${coordinate.name}`, 'success');
        return true;
    }

    /**
     * 从收藏夹删除坐标
     */
    removeFromFavorites(id) {
        this.favorites = this.favorites.filter(item => item.id !== id);
        this.saveToStorage();
        this.updateFavoritesDisplay();
    }

    /**
     * 清空历史记录
     */
    clearHistory() {
        if (this.history.length === 0) {
            updateStatus('历史记录已为空', 'info');
            return;
        }

        if (confirm('确定要清空所有历史记录吗？')) {
            this.history = [];
            this.saveToStorage();
            this.updateHistoryDisplay();
            updateStatus('历史记录已清空', 'info');
        }
    }

    /**
     * 更新历史记录显示
     */
    updateHistoryDisplay() {
        const container = document.getElementById('coordinateHistory');
        if (!container) return;

        if (this.history.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无历史记录</div>';
            return;
        }

        container.innerHTML = this.history.map(item => `
            <div class="coordinate-item" onclick="coordinateManager.useCoordinate(${item.lng}, ${item.lat}, '${item.name}')">
                <div class="coordinate-item-name">${item.name}</div>
                <div class="coordinate-item-coords">${item.lat.toFixed(6)}, ${item.lng.toFixed(6)}</div>
                ${item.note ? `<div class="coordinate-item-note">${item.note}</div>` : ''}
                <div class="coordinate-item-note">${item.displayTime}</div>
                <div class="coordinate-item-actions">
                    <button class="btn btn-small btn-success" onclick="event.stopPropagation(); coordinateManager.setAsStart(${item.lng}, ${item.lat}, '${item.name}')">起点</button>
                    <button class="btn btn-small btn-warning" onclick="event.stopPropagation(); coordinateManager.setAsEnd(${item.lng}, ${item.lat}, '${item.name}')">终点</button>
                    <button class="btn btn-small" onclick="event.stopPropagation(); coordinateManager.addToFavoritesFromHistory(${item.id})">收藏</button>
                </div>
            </div>
        `).join('');
    }

    /**
     * 更新收藏夹显示
     */
    updateFavoritesDisplay() {
        const container = document.getElementById('coordinateFavorites');
        if (!container) return;

        if (this.favorites.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无收藏坐标</div>';
            return;
        }

        container.innerHTML = this.favorites.map(item => `
            <div class="coordinate-item" onclick="coordinateManager.useCoordinate(${item.lng}, ${item.lat}, '${item.name}')">
                <div class="coordinate-item-name">⭐ ${item.name}</div>
                <div class="coordinate-item-coords">${item.lat.toFixed(6)}, ${item.lng.toFixed(6)}</div>
                ${item.note ? `<div class="coordinate-item-note">${item.note}</div>` : ''}
                <div class="coordinate-item-note">收藏于 ${item.displayTime}</div>
                <div class="coordinate-item-actions">
                    <button class="btn btn-small btn-success" onclick="event.stopPropagation(); coordinateManager.setAsStart(${item.lng}, ${item.lat}, '${item.name}')">起点</button>
                    <button class="btn btn-small btn-warning" onclick="event.stopPropagation(); coordinateManager.setAsEnd(${item.lng}, ${item.lat}, '${item.name}')">终点</button>
                    <button class="btn btn-small" onclick="event.stopPropagation(); coordinateManager.removeFromFavorites(${item.id})">删除</button>
                </div>
            </div>
        `).join('');
    }

    /**
     * 使用坐标（填充到输入框）
     */
    useCoordinate(lng, lat, name) {
        // 检查当前输入框状态，智能决定填充位置
        const startLngInput = document.getElementById('startLng');
        const startLatInput = document.getElementById('startLat');
        const endLngInput = document.getElementById('endLng');
        const endLatInput = document.getElementById('endLat');

        const hasStartInput = startLngInput.value.trim() !== '' && startLatInput.value.trim() !== '';
        const hasEndInput = endLngInput.value.trim() !== '' && endLatInput.value.trim() !== '';

        if (!hasStartInput) {
            // 如果起点输入框为空，填充到起点
            startLngInput.value = lng.toFixed(6);
            startLatInput.value = lat.toFixed(6);
            updateStatus(`已填充坐标到起点输入框: ${name}`, 'info');
        } else if (!hasEndInput) {
            // 如果起点有值但终点为空，填充到终点
            endLngInput.value = lng.toFixed(6);
            endLatInput.value = lat.toFixed(6);
            updateStatus(`已填充坐标到终点输入框: ${name}`, 'info');
        } else {
            // 如果起终点输入框都有值，替换起点
            startLngInput.value = lng.toFixed(6);
            startLatInput.value = lat.toFixed(6);
            updateStatus(`已替换起点坐标: ${name}`, 'info');
        }

        // 添加到历史记录
        this.addToHistory(lng, lat, name, '点击使用的坐标');
    }

    /**
     * 设置为起点
     */
    setAsStart(lng, lat, name) {
        // 填充到手动坐标输入框的起点
        document.getElementById('startLng').value = lng.toFixed(6);
        document.getElementById('startLat').value = lat.toFixed(6);

        // 添加到历史记录
        this.addToHistory(lng, lat, name, '收藏夹设置的起点');

        updateStatus(`已填充起点坐标: ${name}，请点击"开始算路"按钮`, 'success');
    }

    /**
     * 设置为终点
     */
    setAsEnd(lng, lat, name) {
        // 填充到手动坐标输入框的终点
        document.getElementById('endLng').value = lng.toFixed(6);
        document.getElementById('endLat').value = lat.toFixed(6);

        // 添加到历史记录
        this.addToHistory(lng, lat, name, '收藏夹设置的终点');

        updateStatus(`已填充终点坐标: ${name}，请点击"开始算路"按钮`, 'success');
    }

    /**
     * 从历史记录添加到收藏夹
     */
    addToFavoritesFromHistory(historyId) {
        const historyItem = this.history.find(item => item.id === historyId);
        if (historyItem) {
            const name = prompt('请输入收藏名称:', historyItem.name);
            if (name && name.trim()) {
                this.addToFavorites(historyItem.lng, historyItem.lat, name.trim(), historyItem.note);
            }
        }
    }

    /**
     * 获取城市坐标数据
     */
    getCityCoordinates() {
        return {
            beijing: { lng: 116.4074, lat: 39.9042, name: '北京天安门', landmark: '天安门广场' },
            shanghai: { lng: 121.4737, lat: 31.2304, name: '上海人民广场', landmark: '人民广场' },
            guangzhou: { lng: 113.2644, lat: 23.1291, name: '广州天河城', landmark: '天河城' },
            shenzhen: { lng: 114.0579, lat: 22.5431, name: '深圳市民中心', landmark: '市民中心' },
            hangzhou: { lng: 120.1551, lat: 30.2741, name: '杭州西湖', landmark: '西湖' },
            nanjing: { lng: 118.7969, lat: 32.0603, name: '南京夫子庙', landmark: '夫子庙' },
            chengdu: { lng: 104.0665, lat: 30.5723, name: '成都天府广场', landmark: '天府广场' },
            wuhan: { lng: 114.3054, lat: 30.5931, name: '武汉黄鹤楼', landmark: '黄鹤楼' }
        };
    }

    /**
     * 导出收藏夹数据
     */
    exportFavorites() {
        if (this.favorites.length === 0) {
            updateStatus('收藏夹为空，无法导出', 'error');
            return;
        }

        const data = {
            type: 'coordinate_favorites',
            version: '1.0',
            exportTime: new Date().toISOString(),
            data: this.favorites
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `coordinate_favorites_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        updateStatus('收藏夹数据已导出', 'success');
    }

    /**
     * 导入收藏夹数据
     */
    importFavorites(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                if (data.type === 'coordinate_favorites' && data.data) {
                    const importCount = data.data.length;
                    this.favorites = [...this.favorites, ...data.data];
                    this.saveToStorage();
                    this.updateFavoritesDisplay();
                    updateStatus(`成功导入 ${importCount} 个收藏坐标`, 'success');
                } else {
                    updateStatus('文件格式不正确', 'error');
                }
            } catch (error) {
                updateStatus('文件解析失败', 'error');
            }
        };
        reader.readAsText(file);
    }
}

// 全局坐标管理器实例
let coordinateManager = null;

// 初始化坐标管理器
document.addEventListener('DOMContentLoaded', function() {
    coordinateManager = new CoordinateManager();
});

// 导出的全局函数
function switchTab(tabName) {
    // 切换标签按钮状态
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');
    
    // 切换内容显示
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    document.getElementById(tabName + 'Tab').classList.add('active');
}

function clearHistory() {
    if (coordinateManager) {
        coordinateManager.clearHistory();
    }
}

function showAddFavoriteDialog() {
    document.getElementById('addFavoriteDialog').style.display = 'block';
    
    // 清空输入框
    document.getElementById('favoriteName').value = '';
    document.getElementById('favoriteLng').value = '';
    document.getElementById('favoriteLat').value = '';
    document.getElementById('favoriteNote').value = '';
}

function hideAddFavoriteDialog() {
    document.getElementById('addFavoriteDialog').style.display = 'none';
}

function addToFavorites() {
    const name = document.getElementById('favoriteName').value.trim();
    const lng = parseFloat(document.getElementById('favoriteLng').value);
    const lat = parseFloat(document.getElementById('favoriteLat').value);
    const note = document.getElementById('favoriteNote').value.trim();
    
    if (!name) {
        updateStatus('请输入坐标名称', 'error');
        return;
    }
    
    if (isNaN(lng) || isNaN(lat)) {
        updateStatus('请输入有效的坐标', 'error');
        return;
    }
    
    if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
        updateStatus('坐标范围错误', 'error');
        return;
    }
    
    if (coordinateManager && coordinateManager.addToFavorites(lng, lat, name, note)) {
        hideAddFavoriteDialog();
    }
}

function getCurrentLocationForFavorite() {
    if (!map) {
        updateStatus('地图未初始化', 'error');
        return;
    }
    
    const center = map.getCenter();
    document.getElementById('favoriteLng').value = center.lng.toFixed(6);
    document.getElementById('favoriteLat').value = center.lat.toFixed(6);
    updateStatus('已获取当前地图中心坐标', 'info');
}

function showCitySelector(target) {
    coordinateManager.currentCityTarget = target;
    const title = target === 'start' ? '选择起点城市坐标' : '选择终点城市坐标';
    document.getElementById('citySelectorTitle').textContent = title;
    document.getElementById('citySelector').style.display = 'block';
}

function hideCitySelector() {
    document.getElementById('citySelector').style.display = 'none';
}

function fillCityCoordinate(cityKey) {
    const cities = coordinateManager.getCityCoordinates();
    const city = cities[cityKey];
    
    if (city) {
        const target = coordinateManager.currentCityTarget;
        const lngInput = target === 'start' ? 'startLng' : 'endLng';
        const latInput = target === 'start' ? 'startLat' : 'endLat';
        
        document.getElementById(lngInput).value = city.lng.toFixed(6);
        document.getElementById(latInput).value = city.lat.toFixed(6);
        
        updateStatus(`已填充${city.name}坐标到${target === 'start' ? '起点' : '终点'}`, 'info');
        hideCitySelector();
    }
}
