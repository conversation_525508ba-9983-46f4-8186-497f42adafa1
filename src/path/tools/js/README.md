# Leaflet + 高德地图 路径规划应用

这是一个基于Leaflet和高德地图API的Web应用，提供地图显示、坐标转换和路径规划功能。

## 功能特性

### 🗺️ 地图功能
- **多图层支持**: 高德街道图、卫星图
- **WMS服务**: 支持自定义WMS图层叠加
- **图层控制**: 可视化图层切换控制器
- **地图交互**: 点击、缩放、平移等基础操作

### 📍 坐标系统
- **坐标转换**: 支持WGS84、GCJ02、BD09坐标系互转
- **实时显示**: 点击地图实时显示多种坐标系坐标
- **精度保证**: 高精度坐标转换算法

### 🛣️ 路径规划
- **多种出行方式**: 驾车、步行、骑行路径规划
- **途经点支持**: 驾车模式支持多个途经点
- **路径可视化**: 路径线条显示和详细信息
- **导航指令**: 提供详细的导航指令

## 文件结构

```
src/path/tools/js/
├── index.html              # 主页面
├── main.js                 # 主应用逻辑
├── map-config.js           # 地图配置和图层管理
├── coordinate-converter.js # 坐标转换工具
├── route-planner.js        # 路径规划模块
├── server.py              # 本地开发服务器
├── start.sh               # 快速启动脚本
└── README.md              # 说明文档
```

## 快速开始

### 方法一：使用启动脚本（推荐）

```bash
# 进入项目目录
cd src/path/tools/js

# 运行启动脚本
./start.sh
```

### 方法二：手动启动

```bash
# 进入项目目录
cd src/path/tools/js

# 启动Python服务器
python3 server.py

# 或指定端口
python3 server.py -p 8080
```

### 方法三：使用其他HTTP服务器

```bash
# 使用Node.js http-server
npx http-server -p 8000 --cors

# 使用Python内置服务器
python3 -m http.server 8000
```

## 使用说明

### 基础操作
1. **查看坐标**: 点击地图任意位置查看WGS84和GCJ02坐标
2. **切换图层**: 使用右上角图层控制器或侧边栏按钮
3. **添加标记**: 在非路径规划模式下点击地图添加标记

### 路径规划
1. 点击"开始规划"按钮进入路径规划模式
2. 在地图上依次点击起点、终点（和途经点）
3. 系统自动计算并显示路径
4. 查看右侧面板的路径详细信息

### 图层控制
- **街道图/卫星图**: 使用侧边栏按钮快速切换
- **WMS图层**: 点击"切换WMS"按钮开启/关闭WMS叠加层
- **图层面板**: 使用右上角图层控制面板进行详细控制

## 技术配置

### 高德地图API
- **API Key**: `f7f401fd4ffb75720cba09ffb7b24a4c`
- **版本**: 2.0
- **服务**: 路径规划、地理编码、逆地理编码

### 坐标系说明
- **WGS84**: GPS原始坐标系，国际标准
- **GCJ02**: 火星坐标系，中国地图标准
- **BD09**: 百度坐标系，百度地图专用

### 支持的浏览器
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开发说明

### 项目依赖
- **Leaflet**: 1.9.4 (地图库)
- **高德地图API**: 2.0 (路径规划服务)
- **Python**: 3.6+ (开发服务器)

### 自定义配置
可以在 `map-config.js` 中修改：
- 地图默认中心点和缩放级别
- 图层URL和样式
- 标记和路径样式

### WMS服务配置
在 `map-config.js` 的 `MAP_LAYERS.wms_layer` 中配置自定义WMS服务：
```javascript
wms_layer: {
    url: 'your-wms-server-url',
    options: {
        layers: 'your-layer-name',
        format: 'image/png',
        transparent: true
    }
}
```

## 故障排除

### 常见问题
1. **地图不显示**: 检查网络连接和API密钥
2. **路径规划失败**: 确认起终点在中国境内
3. **坐标转换异常**: 检查输入坐标格式和范围
4. **服务器启动失败**: 检查端口占用情况

### 调试模式
打开浏览器开发者工具查看控制台日志，所有操作都有详细日志输出。
