<!DOCTYPE qgis PUBLIC 'http://mrcc.com/qgis.dtd' 'SYSTEM'>
<qgis version="3.28.0" styleCategories="AllStyleCategories">
  <flags>
    <Identifiable>1</Identifiable>
    <Removable>1</Removable>
    <Searchable>1</Searchable>
    <Private>0</Private>
  </flags>
  <temporal>
    <fixedRange>
      <start></start>
      <end></end>
    </fixedRange>
  </temporal>
  <customproperties>
    <Option type="Map">
      <Option type="QString" name="embeddedWidgets/count" value="0"/>
      <Option type="QString" name="variableNames"/>
      <Option type="QString" name="variableValues"/>
    </Option>
  </customproperties>
  <pipe-data-defined-properties>
    <Option type="Map">
      <Option type="QString" name="name" value=""/>
      <Option type="Map" name="properties"/>
      <Option type="QString" name="type" value="collection"/>
    </Option>
  </pipe-data-defined-properties>
  <pipe>
    <provider>
      <resampling enabled="false" maxOversampling="2" zoomedInResamplingMethod="nearestNeighbour" zoomedOutResamplingMethod="nearestNeighbour"/>
    </provider>
    <renderer-v2 type="categorizedSymbol" attr="type" symbollevels="0" enableorderby="0" forceraster="0">
      <categories>
        <!-- 起点样式 -->
        <category value="start_point" symbol="0" label="起点" render="true"/>
        <!-- 终点样式 -->
        <category value="end_point" symbol="1" label="终点" render="true"/>
        <!-- 直线连接样式 -->
        <category value="direct_line" symbol="2" label="直线连接" render="true"/>
        <!-- 高德路径样式 -->
        <category value="amap_route" symbol="3" label="高德路径" render="true"/>
        <!-- C++路径样式 -->
        <category value="cpp_route" symbol="4" label="C++路径" render="true"/>
        <!-- 对比分析点样式 -->
        <category value="comparison_analysis" symbol="5" label="对比分析" render="true"/>
      </categories>
      <symbols>
        <!-- 起点符号 (绿色圆形) -->
        <symbol type="marker" name="0" alpha="1" clip_to_extent="1" force_rhr="0">
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option type="Map" name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
          <layer enabled="1" class="SimpleMarker" locked="0" pass="0">
            <Option type="Map">
              <Option type="QString" name="angle" value="0"/>
              <Option type="QString" name="cap_style" value="square"/>
              <Option type="QString" name="color" value="0,255,0,255"/>
              <Option type="QString" name="horizontal_anchor_point" value="1"/>
              <Option type="QString" name="joinstyle" value="bevel"/>
              <Option type="QString" name="name" value="circle"/>
              <Option type="QString" name="offset" value="0,0"/>
              <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="offset_unit" value="MM"/>
              <Option type="QString" name="outline_color" value="0,0,0,255"/>
              <Option type="QString" name="outline_style" value="solid"/>
              <Option type="QString" name="outline_width" value="0.4"/>
              <Option type="QString" name="outline_width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="outline_width_unit" value="MM"/>
              <Option type="QString" name="scale_method" value="diameter"/>
              <Option type="QString" name="size" value="4"/>
              <Option type="QString" name="size_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="size_unit" value="MM"/>
              <Option type="QString" name="vertical_anchor_point" value="1"/>
            </Option>
          </layer>
        </symbol>

        <!-- 终点符号 (红色圆形) -->
        <symbol type="marker" name="1" alpha="1" clip_to_extent="1" force_rhr="0">
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option type="Map" name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
          <layer enabled="1" class="SimpleMarker" locked="0" pass="0">
            <Option type="Map">
              <Option type="QString" name="angle" value="0"/>
              <Option type="QString" name="cap_style" value="square"/>
              <Option type="QString" name="color" value="255,0,0,255"/>
              <Option type="QString" name="horizontal_anchor_point" value="1"/>
              <Option type="QString" name="joinstyle" value="bevel"/>
              <Option type="QString" name="name" value="circle"/>
              <Option type="QString" name="offset" value="0,0"/>
              <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="offset_unit" value="MM"/>
              <Option type="QString" name="outline_color" value="0,0,0,255"/>
              <Option type="QString" name="outline_style" value="solid"/>
              <Option type="QString" name="outline_width" value="0.4"/>
              <Option type="QString" name="outline_width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="outline_width_unit" value="MM"/>
              <Option type="QString" name="scale_method" value="diameter"/>
              <Option type="QString" name="size" value="4"/>
              <Option type="QString" name="size_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="size_unit" value="MM"/>
              <Option type="QString" name="vertical_anchor_point" value="1"/>
            </Option>
          </layer>
        </symbol>

        <!-- 直线连接 (灰色虚线) -->
        <symbol type="line" name="2" alpha="0.5" clip_to_extent="1" force_rhr="0">
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option type="Map" name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
          <layer enabled="1" class="SimpleLine" locked="0" pass="0">
            <Option type="Map">
              <Option type="QString" name="align_dash_pattern" value="0"/>
              <Option type="QString" name="capstyle" value="square"/>
              <Option type="QString" name="customdash" value="5;2"/>
              <Option type="QString" name="customdash_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="customdash_unit" value="MM"/>
              <Option type="QString" name="dash_pattern_offset" value="0"/>
              <Option type="QString" name="dash_pattern_offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="dash_pattern_offset_unit" value="MM"/>
              <Option type="QString" name="draw_inside_polygon" value="0"/>
              <Option type="QString" name="joinstyle" value="bevel"/>
              <Option type="QString" name="line_color" value="128,128,128,255"/>
              <Option type="QString" name="line_style" value="dash"/>
              <Option type="QString" name="line_width" value="0.5"/>
              <Option type="QString" name="line_width_unit" value="MM"/>
              <Option type="QString" name="offset" value="0"/>
              <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="offset_unit" value="MM"/>
              <Option type="QString" name="ring_filter" value="0"/>
              <Option type="QString" name="trim_distance_end" value="0"/>
              <Option type="QString" name="trim_distance_end_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="trim_distance_end_unit" value="MM"/>
              <Option type="QString" name="trim_distance_start" value="0"/>
              <Option type="QString" name="trim_distance_start_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="trim_distance_start_unit" value="MM"/>
              <Option type="QString" name="use_custom_dash" value="0"/>
              <Option type="QString" name="width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            </Option>
          </layer>
        </symbol>

        <!-- 高德路径 (红色实线) -->
        <symbol type="line" name="3" alpha="0.8" clip_to_extent="1" force_rhr="0">
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option type="Map" name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
          <layer enabled="1" class="SimpleLine" locked="0" pass="0">
            <Option type="Map">
              <Option type="QString" name="align_dash_pattern" value="0"/>
              <Option type="QString" name="capstyle" value="round"/>
              <Option type="QString" name="customdash" value="5;2"/>
              <Option type="QString" name="customdash_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="customdash_unit" value="MM"/>
              <Option type="QString" name="dash_pattern_offset" value="0"/>
              <Option type="QString" name="dash_pattern_offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="dash_pattern_offset_unit" value="MM"/>
              <Option type="QString" name="draw_inside_polygon" value="0"/>
              <Option type="QString" name="joinstyle" value="round"/>
              <Option type="QString" name="line_color" value="255,68,68,255"/>
              <Option type="QString" name="line_style" value="solid"/>
              <Option type="QString" name="line_width" value="3"/>
              <Option type="QString" name="line_width_unit" value="MM"/>
              <Option type="QString" name="offset" value="0"/>
              <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="offset_unit" value="MM"/>
              <Option type="QString" name="ring_filter" value="0"/>
              <Option type="QString" name="trim_distance_end" value="0"/>
              <Option type="QString" name="trim_distance_end_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="trim_distance_end_unit" value="MM"/>
              <Option type="QString" name="trim_distance_start" value="0"/>
              <Option type="QString" name="trim_distance_start_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="trim_distance_start_unit" value="MM"/>
              <Option type="QString" name="use_custom_dash" value="0"/>
              <Option type="QString" name="width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            </Option>
          </layer>
        </symbol>

        <!-- C++路径 (蓝色实线) -->
        <symbol type="line" name="4" alpha="0.8" clip_to_extent="1" force_rhr="0">
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option type="Map" name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
          <layer enabled="1" class="SimpleLine" locked="0" pass="0">
            <Option type="Map">
              <Option type="QString" name="align_dash_pattern" value="0"/>
              <Option type="QString" name="capstyle" value="round"/>
              <Option type="QString" name="customdash" value="5;2"/>
              <Option type="QString" name="customdash_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="customdash_unit" value="MM"/>
              <Option type="QString" name="dash_pattern_offset" value="0"/>
              <Option type="QString" name="dash_pattern_offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="dash_pattern_offset_unit" value="MM"/>
              <Option type="QString" name="draw_inside_polygon" value="0"/>
              <Option type="QString" name="joinstyle" value="round"/>
              <Option type="QString" name="line_color" value="68,68,255,255"/>
              <Option type="QString" name="line_style" value="solid"/>
              <Option type="QString" name="line_width" value="3"/>
              <Option type="QString" name="line_width_unit" value="MM"/>
              <Option type="QString" name="offset" value="0"/>
              <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="offset_unit" value="MM"/>
              <Option type="QString" name="ring_filter" value="0"/>
              <Option type="QString" name="trim_distance_end" value="0"/>
              <Option type="QString" name="trim_distance_end_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="trim_distance_end_unit" value="MM"/>
              <Option type="QString" name="trim_distance_start" value="0"/>
              <Option type="QString" name="trim_distance_start_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="trim_distance_start_unit" value="MM"/>
              <Option type="QString" name="use_custom_dash" value="0"/>
              <Option type="QString" name="width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            </Option>
          </layer>
        </symbol>

        <!-- 对比分析点 (分级菱形) -->
        <symbol type="marker" name="5" alpha="1" clip_to_extent="1" force_rhr="0">
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option type="Map" name="properties">
                <Option type="Map" name="fillColor">
                  <Option type="bool" name="active" value="true"/>
                  <Option type="QString" name="expression" value="CASE &#xd;&#xa;WHEN &quot;distance_diff_percent&quot; &lt; 5 THEN '#00AA00'&#xd;&#xa;WHEN &quot;distance_diff_percent&quot; &lt;= 10 THEN '#FFAA00'&#xd;&#xa;ELSE '#AA0000'&#xd;&#xa;END"/>
                  <Option type="int" name="type" value="3"/>
                </Option>
              </Option>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
          <layer enabled="1" class="SimpleMarker" locked="0" pass="0">
            <Option type="Map">
              <Option type="QString" name="angle" value="45"/>
              <Option type="QString" name="cap_style" value="square"/>
              <Option type="QString" name="color" value="0,170,0,255"/>
              <Option type="QString" name="horizontal_anchor_point" value="1"/>
              <Option type="QString" name="joinstyle" value="bevel"/>
              <Option type="QString" name="name" value="square"/>
              <Option type="QString" name="offset" value="0,0"/>
              <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="offset_unit" value="MM"/>
              <Option type="QString" name="outline_color" value="0,0,0,255"/>
              <Option type="QString" name="outline_style" value="solid"/>
              <Option type="QString" name="outline_width" value="0.4"/>
              <Option type="QString" name="outline_width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="outline_width_unit" value="MM"/>
              <Option type="QString" name="scale_method" value="diameter"/>
              <Option type="QString" name="size" value="5"/>
              <Option type="QString" name="size_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="size_unit" value="MM"/>
              <Option type="QString" name="vertical_anchor_point" value="1"/>
            </Option>
          </layer>
        </symbol>
      </symbols>
      <source-symbol>
        <symbol type="marker" name="0" alpha="1" clip_to_extent="1" force_rhr="0">
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option type="Map" name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
          <layer enabled="1" class="SimpleMarker" locked="0" pass="0">
            <Option type="Map">
              <Option type="QString" name="angle" value="0"/>
              <Option type="QString" name="cap_style" value="square"/>
              <Option type="QString" name="color" value="255,0,0,255"/>
              <Option type="QString" name="horizontal_anchor_point" value="1"/>
              <Option type="QString" name="joinstyle" value="bevel"/>
              <Option type="QString" name="name" value="circle"/>
              <Option type="QString" name="offset" value="0,0"/>
              <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="offset_unit" value="MM"/>
              <Option type="QString" name="outline_color" value="35,35,35,255"/>
              <Option type="QString" name="outline_style" value="solid"/>
              <Option type="QString" name="outline_width" value="0"/>
              <Option type="QString" name="outline_width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="outline_width_unit" value="MM"/>
              <Option type="QString" name="scale_method" value="diameter"/>
              <Option type="QString" name="size" value="2"/>
              <Option type="QString" name="size_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="size_unit" value="MM"/>
              <Option type="QString" name="vertical_anchor_point" value="1"/>
            </Option>
          </layer>
        </symbol>
      </source-symbol>
      <colorramp type="gradient" name="[source]">
        <Option type="Map">
          <Option type="QString" name="color1" value="247,251,255,255"/>
          <Option type="QString" name="color2" value="8,48,107,255"/>
          <Option type="QString" name="discrete" value="0"/>
          <Option type="QString" name="rampType" value="gradient"/>
          <Option type="QString" name="stops" value="0.13;222,235,247,255:0.26;198,219,239,255:0.39;158,202,225,255:0.52;107,174,214,255:0.65;66,146,198,255:0.78;33,113,181,255:0.9;8,81,156,255"/>
        </Option>
        <prop v="247,251,255,255" k="color1"/>
        <prop v="8,48,107,255" k="color2"/>
        <prop v="0" k="discrete"/>
        <prop v="gradient" k="rampType"/>
        <prop v="0.13;222,235,247,255:0.26;198,219,239,255:0.39;158,202,225,255:0.52;107,174,214,255:0.65;66,146,198,255:0.78;33,113,181,255:0.9;8,81,156,255" k="stops"/>
      </colorramp>
      <rotation/>
      <sizescale/>
    </renderer-v2>
    <customproperties>
      <Option type="Map">
        <Option type="List" name="dualview/previewExpressions">
          <Option type="QString" value="&quot;type&quot;"/>
        </Option>
        <Option type="QString" name="embeddedWidgets/count" value="0"/>
        <Option type="QString" name="variableNames"/>
        <Option type="QString" name="variableValues"/>
      </Option>
    </customproperties>
    <blendMode>0</blendMode>
    <featureBlendMode>0</featureBlendMode>
    <layerOpacity>1</layerOpacity>
    <SingleCategoryDiagramRenderer diagramType="Histogram" attributeLegend="1">
      <DiagramCategory height="15" labelPlacementMethod="XHeight" rotationOffset="270" sizeScale="3x:0,0,0,0,0,0" barWidth="5" diagramOrientation="Up" enabled="0" penColor="#000000" maxScaleDenominator="1e+08" backgroundColor="#ffffff" width="15" minScaleDenominator="0" penWidth="0" scaleBasedVisibility="0" backgroundAlpha="255" opacity="1" lineSizeScale="3x:0,0,0,0,0,0" minimumSize="0" penAlpha="255" lineSizeType="MM" scaleDependency="Area" spacing="5" direction="0" spacingUnit="MM" showAxis="1" spacingUnitScale="3x:0,0,0,0,0,0" sizeType="MM">
        <fontProperties description="MS Shell Dlg 2,8.25,-1,5,50,0,0,0,0,0" style=""/>
        <attribute field="" color="#000000" label=""/>
        <axisSymbol>
          <symbol type="line" name="" alpha="1" clip_to_extent="1" force_rhr="0">
            <data_defined_properties>
              <Option type="Map">
                <Option type="QString" name="name" value=""/>
                <Option type="Map" name="properties"/>
                <Option type="QString" name="type" value="collection"/>
              </Option>
            </data_defined_properties>
            <layer enabled="1" class="SimpleLine" locked="0" pass="0">
              <Option type="Map">
                <Option type="QString" name="align_dash_pattern" value="0"/>
                <Option type="QString" name="capstyle" value="square"/>
                <Option type="QString" name="customdash" value="5;2"/>
                <Option type="QString" name="customdash_map_unit_scale" value="3x:0,0,0,0,0,0"/>
                <Option type="QString" name="customdash_unit" value="MM"/>
                <Option type="QString" name="dash_pattern_offset" value="0"/>
                <Option type="QString" name="dash_pattern_offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
                <Option type="QString" name="dash_pattern_offset_unit" value="MM"/>
                <Option type="QString" name="draw_inside_polygon" value="0"/>
                <Option type="QString" name="joinstyle" value="bevel"/>
                <Option type="QString" name="line_color" value="35,35,35,255"/>
                <Option type="QString" name="line_style" value="solid"/>
                <Option type="QString" name="line_width" value="0.26"/>
                <Option type="QString" name="line_width_unit" value="MM"/>
                <Option type="QString" name="offset" value="0"/>
                <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
                <Option type="QString" name="offset_unit" value="MM"/>
                <Option type="QString" name="ring_filter" value="0"/>
                <Option type="QString" name="trim_distance_end" value="0"/>
                <Option type="QString" name="trim_distance_end_map_unit_scale" value="3x:0,0,0,0,0,0"/>
                <Option type="QString" name="trim_distance_end_unit" value="MM"/>
                <Option type="QString" name="trim_distance_start" value="0"/>
                <Option type="QString" name="trim_distance_start_map_unit_scale" value="3x:0,0,0,0,0,0"/>
                <Option type="QString" name="trim_distance_start_unit" value="MM"/>
                <Option type="QString" name="use_custom_dash" value="0"/>
                <Option type="QString" name="width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              </Option>
            </layer>
          </symbol>
        </axisSymbol>
      </DiagramCategory>
    </SingleCategoryDiagramRenderer>
    <DiagramLayerSettings priority="0" linePlacementFlags="18" showAll="1" zIndex="0" placement="0" dist="0" obstacle="0">
      <properties>
        <Option type="Map">
          <Option type="QString" name="name" value=""/>
          <Option type="Map" name="properties"/>
          <Option type="QString" name="type" value="collection"/>
        </Option>
      </properties>
    </DiagramLayerSettings>
    <geometryOptions removeDuplicateNodes="0" geometryPrecision="0">
      <activeChecks/>
      <checkConfiguration/>
    </geometryOptions>
    <legend type="default-vector" showLabelLegend="0"/>
    <referencedLayers/>
    <fieldConfiguration>
      <field configurationFlags="None" name="type">
        <editWidget type="TextEdit">
          <config>
            <Option/>
          </config>
        </editWidget>
      </field>
      <field configurationFlags="None" name="test_id">
        <editWidget type="Range">
          <config>
            <Option/>
          </config>
        </editWidget>
      </field>
      <field configurationFlags="None" name="name">
        <editWidget type="TextEdit">
          <config>
            <Option/>
          </config>
        </editWidget>
      </field>
      <field configurationFlags="None" name="test_status">
        <editWidget type="TextEdit">
          <config>
            <Option/>
          </config>
        </editWidget>
      </field>
      <field configurationFlags="None" name="distance_diff_percent">
        <editWidget type="TextEdit">
          <config>
            <Option/>
          </config>
        </editWidget>
      </field>
    </fieldConfiguration>
    <aliases>
      <alias field="type" name="要素类型" index="0"/>
      <alias field="test_id" name="测试ID" index="1"/>
      <alias field="name" name="地点名称" index="2"/>
      <alias field="test_status" name="测试状态" index="3"/>
      <alias field="distance_diff_percent" name="距离差异%" index="4"/>
    </aliases>
    <defaults>
      <default field="type" expression="" applyOnUpdate="0"/>
      <default field="test_id" expression="" applyOnUpdate="0"/>
      <default field="name" expression="" applyOnUpdate="0"/>
      <default field="test_status" expression="" applyOnUpdate="0"/>
      <default field="distance_diff_percent" expression="" applyOnUpdate="0"/>
    </defaults>
    <constraints>
      <constraint field="type" constraints="0" unique_strength="0" notnull_strength="0" exp_strength="0"/>
      <constraint field="test_id" constraints="0" unique_strength="0" notnull_strength="0" exp_strength="0"/>
      <constraint field="name" constraints="0" unique_strength="0" notnull_strength="0" exp_strength="0"/>
      <constraint field="test_status" constraints="0" unique_strength="0" notnull_strength="0" exp_strength="0"/>
      <constraint field="distance_diff_percent" constraints="0" unique_strength="0" notnull_strength="0" exp_strength="0"/>
    </constraints>
    <constraintExpressions>
      <constraint field="type" desc="" exp=""/>
      <constraint field="test_id" desc="" exp=""/>
      <constraint field="name" desc="" exp=""/>
      <constraint field="test_status" desc="" exp=""/>
      <constraint field="distance_diff_percent" desc="" exp=""/>
    </constraintExpressions>
    <expressionfields/>
    <attributeactions>
      <defaultAction key="Canvas" value="{00000000-0000-0000-0000-000000000000}"/>
    </attributeactions>
    <attributetableconfig sortExpression="" actionWidgetStyle="dropDown" sortOrder="0">
      <columns>
        <column type="field" name="type" width="-1" hidden="0"/>
        <column type="field" name="test_id" width="-1" hidden="0"/>
        <column type="field" name="name" width="-1" hidden="0"/>
        <column type="field" name="test_status" width="-1" hidden="0"/>
        <column type="field" name="distance_diff_percent" width="-1" hidden="0"/>
        <column type="actions" width="-1" hidden="1"/>
      </columns>
    </attributetableconfig>
    <conditionalstyles>
      <rowstyles/>
      <fieldstyles/>
    </conditionalstyles>
    <storedexpressions/>
    <editform tolerant="1"></editform>
    <editforminit/>
    <editforminitcodesource>0</editforminitcodesource>
    <editforminitfilepath></editforminitfilepath>
    <editforminitcode><![CDATA[# -*- coding: utf-8 -*-
"""
QGIS forms can have a Python function that is called when the form is
opened.

Use this function to add extra logic to your forms.

Enter the name of the function in the "Python Init function"
field.
An example follows:
"""
from qgis.PyQt.QtWidgets import QWidget

def my_form_open(dialog, layer, feature):
    geom = feature.geometry()
    control = dialog.findChild(QWidget, "MyLineEdit")
]]></editforminitcode>
    <featformsuppress>0</featformsuppress>
    <editorlayout>generatedlayout</editorlayout>
    <editable>
      <field editable="1" name="type"/>
      <field editable="1" name="test_id"/>
      <field editable="1" name="name"/>
      <field editable="1" name="test_status"/>
      <field editable="1" name="distance_diff_percent"/>
    </editable>
    <labelOnTop>
      <field labelOnTop="0" name="type"/>
      <field labelOnTop="0" name="test_id"/>
      <field labelOnTop="0" name="name"/>
      <field labelOnTop="0" name="test_status"/>
      <field labelOnTop="0" name="distance_diff_percent"/>
    </labelOnTop>
    <reuseLastValue>
      <field reuseLastValue="0" name="type"/>
      <field reuseLastValue="0" name="test_id"/>
      <field reuseLastValue="0" name="name"/>
      <field reuseLastValue="0" name="test_status"/>
      <field reuseLastValue="0" name="distance_diff_percent"/>
    </reuseLastValue>
    <dataDefinedFieldProperties/>
    <widgets/>
    <previewExpression>"type"</previewExpression>
    <mapTip></mapTip>
  </pipe>
  <layerGeometryType>3</layerGeometryType>
</qgis>