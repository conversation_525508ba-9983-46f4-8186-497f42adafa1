# QGIS双算路对比可视化指南

## 📊 GeoJSON数据结构说明

导出的GeoJSON文件包含以下要素类型：

### 1. 点要素 (Points)
- **start_point**: 测试起点
- **end_point**: 测试终点  
- **comparison_analysis**: 对比分析中心点（仅双算路成功时）

### 2. 线要素 (LineStrings)
- **direct_line**: 起终点直线连接
- **amap_route**: 高德地图路径
- **cpp_route**: C++算路路径

## 🎨 QGIS样式配置建议

### 起终点样式
```
起点 (start_point):
- 符号: 圆形
- 颜色: 绿色 (#00FF00)
- 大小: 8px
- 边框: 黑色 2px

终点 (end_point):
- 符号: 圆形
- 颜色: 红色 (#FF0000)
- 大小: 8px
- 边框: 黑色 2px
```

### 路径线样式
```
直线连接 (direct_line):
- 线型: 虚线
- 颜色: 灰色 (#808080)
- 宽度: 1px
- 透明度: 50%

高德路径 (amap_route):
- 线型: 实线
- 颜色: 红色 (#FF4444)
- 宽度: 3px
- 透明度: 80%

C++路径 (cpp_route):
- 线型: 实线
- 颜色: 蓝色 (#4444FF)
- 宽度: 3px
- 透明度: 80%
```

### 对比分析点样式
```
对比分析 (comparison_analysis):
- 符号: 菱形
- 颜色: 根据差异程度分级
  - 差异 < 5%: 绿色 (#00AA00)
  - 差异 5-10%: 黄色 (#FFAA00)
  - 差异 > 10%: 红色 (#AA0000)
- 大小: 10px
```

## 🔧 QGIS导入步骤

### 1. 导入GeoJSON文件
1. 打开QGIS
2. 图层 → 添加图层 → 添加矢量图层
3. 选择导出的.geojson文件
4. 点击"添加"

### 2. 按要素类型分层
1. 右键图层 → 属性
2. 符号系统 → 分类 → 按"type"字段分类
3. 点击"分类"按钮自动生成各类型

### 3. 设置样式
按照上述样式建议配置每个要素类型的显示样式

### 4. 设置标注
1. 图层属性 → 标注
2. 选择"显示此图层的标注"
3. 标注字段选择"name"或"test_id"

## 📈 数据分析功能

### 属性表字段说明

#### 点要素属性
- `test_id`: 测试序号
- `name`: 地点名称
- `test_status`: 测试状态组合 (如: success_success)
- `direct_distance_km`: 直线距离
- `amap_status`: 高德算路状态
- `cpp_status`: C++算路状态
- `total_time_ms`: 总测试耗时

#### 路径要素属性
- `route_source`: 路径来源 (amap/cpp)
- `distance_km`: 路径距离
- `time_min`: 预计行驶时间
- `point_count`: 路径点数量
- `query_time_ms`: 查询耗时 (仅C++路径)

#### 对比分析属性
- `distance_diff_percent`: 距离差异百分比
- `time_diff_percent`: 时间差异百分比
- `amap_distance_km`: 高德路径距离
- `cpp_distance_km`: C++路径距离
- `amap_time_min`: 高德预计时间
- `cpp_time_min`: C++预计时间

### 筛选和查询

#### 1. 按测试状态筛选
```sql
-- 查看双算路都成功的测试
"test_status" = 'success_success'

-- 查看高德成功但C++失败的测试
"test_status" = 'success_failed'

-- 查看存在失败的测试
"test_status" LIKE '%failed%'
```

#### 2. 按差异程度筛选
```sql
-- 查看距离差异大于10%的测试
"distance_diff_percent" > 10

-- 查看时间差异小于5%的测试
"time_diff_percent" < 5

-- 查看C++查询时间超过1秒的测试
"cpp_query_time_ms" > 1000
```

#### 3. 按距离范围筛选
```sql
-- 查看短距离测试 (< 10km)
"direct_distance_km" < 10

-- 查看长距离测试 (> 50km)
"direct_distance_km" > 50
```

## 📊 可视化分析技巧

### 1. 热力图分析
- 使用对比分析点创建差异热力图
- 颜色映射: 差异百分比 → 颜色深浅
- 识别算路差异集中的区域

### 2. 路径对比
- 同时显示高德和C++路径
- 使用不同颜色区分
- 观察路径选择的差异模式

### 3. 性能分析
- 按C++查询时间创建分级符号
- 识别查询性能较差的区域
- 分析距离与查询时间的关系

### 4. 成功率分析
- 按测试状态创建饼图
- 统计各种状态的分布
- 识别算路失败的模式

## 🔍 问题诊断

### 常见问题及解决方案

#### 1. 路径显示异常
- 检查坐标系是否为WGS84 (EPSG:4326)
- 确认路径坐标顺序为 [经度, 纬度]
- 验证坐标范围是否合理

#### 2. 样式不生效
- 确认按"type"字段正确分类
- 检查字段名称是否匹配
- 重新应用样式设置

#### 3. 标注重叠
- 调整标注偏移量
- 使用标注缓冲区
- 设置标注优先级

## 📋 分析报告模板

### 基础统计
- 总测试数量: ___
- 双算路成功率: ___%
- 平均距离差异: ___%
- 平均时间差异: ___%
- 平均C++查询时间: ___ms

### 差异分析
- 距离差异 < 5%: ___个 (__%)
- 距离差异 5-10%: ___个 (__%)
- 距离差异 > 10%: ___个 (__%)

### 性能分析
- C++查询 < 500ms: ___个 (__%)
- C++查询 500-1000ms: ___个 (__%)
- C++查询 > 1000ms: ___个 (__%)

### 问题区域
- 高差异区域: ___
- 低性能区域: ___
- 失败集中区域: ___

## 🎯 高级分析

### 1. 空间聚类分析
- 使用QGIS聚类工具分析差异分布
- 识别算路质量的空间模式
- 发现区域性的算路问题

### 2. 缓冲区分析
- 创建路径缓冲区
- 分析路径重叠度
- 量化路径相似性

### 3. 网络分析
- 结合路网数据分析
- 识别路径选择偏好
- 分析算法差异原因

### 4. 统计分析
- 导出属性数据到Excel
- 进行相关性分析
- 生成统计图表

---

通过QGIS的强大可视化功能，您可以直观地分析双算路系统的性能差异，发现潜在问题，并优化算路算法！🗺️
