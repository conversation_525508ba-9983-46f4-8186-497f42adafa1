<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>右键菜单功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .test-section {
            padding: 20px;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .test-section:last-child {
            border-bottom: none;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            border-left: 4px solid #1890ff;
            padding-left: 10px;
        }
        
        .test-area {
            background: #fafafa;
            border: 2px dashed #d9d9d9;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 15px 0;
            cursor: crosshair;
            position: relative;
            min-height: 150px;
        }
        
        .test-area:hover {
            border-color: #1890ff;
            background: #f0f8ff;
        }
        
        .coordinates-display {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            align-items: center;
        }
        
        .input-group label {
            min-width: 80px;
            font-weight: 500;
            color: #333;
        }
        
        .input-group input {
            flex: 1;
            padding: 6px 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 12px;
            font-family: monospace;
        }
        
        .btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }
        
        .result {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .result.error {
            background: #fff2f0;
            border-color: #ffccc7;
        }
        
        .result.info {
            background: #e6f7ff;
            border-color: #91d5ff;
        }
        
        .context-menu {
            position: absolute;
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 4px 0;
            min-width: 120px;
            z-index: 1000;
            display: none;
        }
        
        .context-menu-item {
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
        }
        
        .context-menu-item:hover {
            background: #f5f5f5;
        }
        
        .context-menu-divider {
            height: 1px;
            background: #e8e8e8;
            margin: 4px 0;
        }
        
        .history-list {
            max-height: 150px;
            overflow-y: auto;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .history-item {
            padding: 8px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 11px;
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }
        
        .history-coords {
            color: #666;
            font-family: monospace;
            margin-bottom: 2px;
        }
        
        .history-note {
            color: #999;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖱️ 右键菜单功能测试</h1>
            <p>测试右键菜单的"设为起点"和"设为终点"功能是否正确填充到手动坐标输入框并添加到历史记录</p>
        </div>

        <div class="test-section">
            <h3>🎯 右键菜单模拟测试</h3>
            <div class="test-area" id="testArea" oncontextmenu="showTestContextMenu(event)">
                <p style="color: #666; font-size: 16px; margin: 0;">
                    🖱️ 在此区域右键点击测试右键菜单功能
                </p>
                <p style="color: #999; font-size: 12px; margin: 10px 0 0 0;">
                    模拟地图右键点击，测试"设为起点"和"设为终点"功能
                </p>
                <div id="clickCoordinates" class="coordinates-display" style="display: none;">
                    点击坐标将在这里显示
                </div>
            </div>
            
            <!-- 模拟右键菜单 -->
            <div id="testContextMenu" class="context-menu">
                <button class="context-menu-item" onclick="testSetStartPoint()">🟢 设为起点</button>
                <button class="context-menu-item" onclick="testAddWaypoint()">🔵 添加途经点</button>
                <button class="context-menu-item" onclick="testSetEndPoint()">🔴 设为终点</button>
                <div class="context-menu-divider"></div>
                <button class="context-menu-item" onclick="testSearchAround()">🔍 搜索周边</button>
                <button class="context-menu-item" onclick="testReverseGeocode()">📍 获取地址</button>
                <div class="context-menu-divider"></div>
                <button class="context-menu-item" onclick="testCalculateRoute()">🛣️ 开始算路</button>
                <button class="context-menu-item" onclick="testClearPoints()">🗑️ 清除所有点</button>
            </div>
        </div>

        <div class="test-section">
            <h3>📐 手动坐标输入框状态</h3>
            <div class="input-group">
                <label>起点经度:</label>
                <input type="number" id="mockStartLng" placeholder="经度" step="0.000001" readonly>
                <label>起点纬度:</label>
                <input type="number" id="mockStartLat" placeholder="纬度" step="0.000001" readonly>
            </div>
            <div class="input-group">
                <label>终点经度:</label>
                <input type="number" id="mockEndLng" placeholder="经度" step="0.000001" readonly>
                <label>终点纬度:</label>
                <input type="number" id="mockEndLat" placeholder="纬度" step="0.000001" readonly>
            </div>
            <div style="margin-top: 10px;">
                <button class="btn btn-primary" onclick="testManualRoute()">🛣️ 开始算路</button>
                <button class="btn" onclick="clearInputs()">🧹 清空输入框</button>
            </div>
            <div id="inputStatus" class="result">等待右键菜单操作...</div>
        </div>

        <div class="test-section">
            <h3>📚 历史记录状态</h3>
            <div style="margin-bottom: 10px;">
                <button class="btn" onclick="showHistory()">显示历史记录</button>
                <button class="btn" onclick="clearHistory()">清空历史</button>
                <span style="margin-left: 10px; font-size: 12px; color: #666;">
                    历史记录数量: <span id="historyCount">0</span>
                </span>
            </div>
            <div id="historyDisplay" class="history-list">
                <div style="padding: 20px; text-align: center; color: #999;">暂无历史记录</div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试结果</h3>
            <div id="testResults" class="result">
                <strong>测试说明:</strong><br>
                1. 在测试区域右键点击，选择"设为起点"或"设为终点"<br>
                2. 检查手动坐标输入框是否正确填充<br>
                3. 检查历史记录是否正确添加<br>
                4. 验证状态提示是否正确显示
            </div>
        </div>
    </div>

    <script>
        // 模拟全局变量
        let testRightClickPosition = null;
        let testHistory = [];
        let testClickCount = 0;

        // 模拟坐标管理器
        const mockCoordinateManager = {
            addToHistory: function(lng, lat, name, note) {
                const item = {
                    id: Date.now(),
                    lng: lng,
                    lat: lat,
                    name: name,
                    note: note,
                    time: new Date().toLocaleString('zh-CN')
                };
                testHistory.unshift(item);
                updateHistoryCount();
                return true;
            }
        };

        // 显示测试右键菜单
        function showTestContextMenu(event) {
            event.preventDefault();
            
            // 计算相对于测试区域的坐标
            const rect = event.target.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            // 模拟地图坐标 (转换为经纬度)
            const lng = 116.4074 + (x - rect.width/2) * 0.001;
            const lat = 39.9042 + (rect.height/2 - y) * 0.001;
            
            testRightClickPosition = [lng, lat];
            testClickCount++;
            
            // 显示点击坐标
            const coordsDiv = document.getElementById('clickCoordinates');
            coordsDiv.style.display = 'block';
            coordsDiv.innerHTML = `
                <strong>右键点击位置 #${testClickCount}:</strong><br>
                经度: ${lng.toFixed(6)}, 纬度: ${lat.toFixed(6)}<br>
                屏幕坐标: (${x.toFixed(0)}, ${y.toFixed(0)})
            `;
            
            // 显示右键菜单
            const menu = document.getElementById('testContextMenu');
            menu.style.left = event.clientX + 'px';
            menu.style.top = event.clientY + 'px';
            menu.style.display = 'block';
        }

        // 隐藏右键菜单
        function hideTestContextMenu() {
            document.getElementById('testContextMenu').style.display = 'none';
        }

        // 测试设置起点
        function testSetStartPoint() {
            if (!testRightClickPosition) return;

            const coords = testRightClickPosition;
            const coordsText = `${coords[1].toFixed(6)}, ${coords[0].toFixed(6)}`;
            const name = `右键设置起点: ${coordsText}`;

            // 填充到输入框
            document.getElementById('mockStartLng').value = coords[0].toFixed(6);
            document.getElementById('mockStartLat').value = coords[1].toFixed(6);

            // 模拟在地图上创建标记
            showMapMarker('start', coords, name);

            // 添加到历史记录
            mockCoordinateManager.addToHistory(coords[0], coords[1], name, '右键点击设置的起点');

            // 更新状态
            updateInputStatus(`✅ 已设置起点标记并填充坐标: ${coordsText}，请点击"开始算路"按钮`, 'success');

            hideTestContextMenu();
        }

        // 测试设置终点
        function testSetEndPoint() {
            if (!testRightClickPosition) return;

            const coords = testRightClickPosition;
            const coordsText = `${coords[1].toFixed(6)}, ${coords[0].toFixed(6)}`;
            const name = `右键设置终点: ${coordsText}`;

            // 填充到输入框
            document.getElementById('mockEndLng').value = coords[0].toFixed(6);
            document.getElementById('mockEndLat').value = coords[1].toFixed(6);

            // 模拟在地图上创建标记
            showMapMarker('end', coords, name);

            // 添加到历史记录
            mockCoordinateManager.addToHistory(coords[0], coords[1], name, '右键点击设置的终点');

            // 更新状态
            updateInputStatus(`✅ 已设置终点标记并填充坐标: ${coordsText}，请点击"开始算路"按钮`, 'success');

            hideTestContextMenu();
        }

        // 其他右键菜单功能的模拟
        function testAddWaypoint() {
            updateInputStatus('🔵 添加途经点功能 (模拟)', 'info');
            hideTestContextMenu();
        }

        function testSearchAround() {
            updateInputStatus('🔍 搜索周边功能 (模拟)', 'info');
            hideTestContextMenu();
        }

        function testReverseGeocode() {
            updateInputStatus('📍 获取地址功能 (模拟)', 'info');
            hideTestContextMenu();
        }

        function testCalculateRoute() {
            updateInputStatus('🛣️ 开始算路功能 (模拟)', 'info');
            hideTestContextMenu();
        }

        function testClearPoints() {
            clearInputs();
            updateInputStatus('🗑️ 已清除所有点 (模拟)', 'info');
            hideTestContextMenu();
        }

        // 测试手动算路
        function testManualRoute() {
            const startLng = document.getElementById('mockStartLng').value;
            const startLat = document.getElementById('mockStartLat').value;
            const endLng = document.getElementById('mockEndLng').value;
            const endLat = document.getElementById('mockEndLat').value;
            
            if (!startLng || !startLat || !endLng || !endLat) {
                updateInputStatus('❌ 请先设置起点和终点坐标', 'error');
                return;
            }
            
            updateInputStatus(`✅ 模拟算路: 从 (${startLat}, ${startLng}) 到 (${endLat}, ${endLng})`, 'success');
        }

        // 清空输入框
        function clearInputs() {
            document.getElementById('mockStartLng').value = '';
            document.getElementById('mockStartLat').value = '';
            document.getElementById('mockEndLng').value = '';
            document.getElementById('mockEndLat').value = '';
            updateInputStatus('输入框已清空', 'info');
        }

        // 更新输入状态
        function updateInputStatus(message, type = 'info') {
            const element = document.getElementById('inputStatus');
            element.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            element.className = `result ${type}`;
        }

        // 显示历史记录
        function showHistory() {
            const display = document.getElementById('historyDisplay');
            
            if (testHistory.length === 0) {
                display.innerHTML = '<div style="padding: 20px; text-align: center; color: #999;">暂无历史记录</div>';
                return;
            }
            
            let html = '';
            testHistory.forEach(item => {
                html += `
                    <div class="history-item">
                        <div class="history-name">${item.name}</div>
                        <div class="history-coords">${item.lat.toFixed(6)}, ${item.lng.toFixed(6)}</div>
                        <div class="history-note">${item.note} - ${item.time}</div>
                    </div>
                `;
            });
            
            display.innerHTML = html;
        }

        // 清空历史记录
        function clearHistory() {
            testHistory = [];
            updateHistoryCount();
            showHistory();
            updateInputStatus('历史记录已清空', 'info');
        }

        // 更新历史记录数量
        function updateHistoryCount() {
            document.getElementById('historyCount').textContent = testHistory.length;
        }

        // 点击其他地方隐藏右键菜单
        document.addEventListener('click', function(event) {
            if (!event.target.closest('#testContextMenu')) {
                hideTestContextMenu();
            }
        });

        // 模拟地图标记显示
        function showMapMarker(type, coords, name) {
            const testArea = document.getElementById('testArea');

            // 清除之前的同类型标记
            const existingMarkers = testArea.querySelectorAll(`.marker-${type}`);
            existingMarkers.forEach(marker => marker.remove());

            // 创建新标记
            const marker = document.createElement('div');
            marker.className = `marker-${type}`;
            marker.style.position = 'absolute';
            marker.style.width = '20px';
            marker.style.height = '20px';
            marker.style.borderRadius = '50%';
            marker.style.border = '2px solid white';
            marker.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3)';
            marker.style.zIndex = '10';
            marker.style.cursor = 'pointer';

            if (type === 'start') {
                marker.style.backgroundColor = '#52c41a';
                marker.innerHTML = '🟢';
                marker.style.left = '20%';
                marker.style.top = '30%';
            } else {
                marker.style.backgroundColor = '#ff4d4f';
                marker.innerHTML = '🔴';
                marker.style.left = '70%';
                marker.style.top = '60%';
            }

            marker.title = `${type === 'start' ? '起点' : '终点'}: ${name}`;
            testArea.appendChild(marker);

            // 添加点击事件
            marker.addEventListener('click', function() {
                alert(`${type === 'start' ? '起点' : '终点'}信息:\n${name}\n坐标: ${coords[1].toFixed(6)}, ${coords[0].toFixed(6)}`);
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateHistoryCount();
            updateInputStatus('右键菜单功能测试页面已加载，请在测试区域右键点击', 'info');
        });
    </script>
</body>
</html>
