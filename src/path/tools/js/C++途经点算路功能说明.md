# C++算路途经点支持功能说明

## 📋 功能概述

为C++算路服务添加途经点支持，使其能够像高德算路一样处理包含多个途经点的路径规划请求。

## 🔧 修改内容

### 1. **C++服务器端修改 (simple_route_server.cpp)**

#### 1.1 JSON解析器增强
```cpp
// 新增途经点解析功能
static std::vector<std::pair<double, double>> getWaypoints(const std::string& json) {
    std::vector<std::pair<double, double>> waypoints;
    
    // 查找waypoints数组
    std::regex waypoints_pattern("\"waypoints\"\\s*:\\s*\\[(.*?)\\]");
    std::smatch waypoints_match;
    
    if (std::regex_search(json, waypoints_match, waypoints_pattern)) {
        std::string waypoints_str = waypoints_match[1].str();
        
        // 解析每个途经点对象 {"lng":xxx,"lat":xxx}
        std::regex point_pattern("\\{\\s*\"lng\"\\s*:\\s*([0-9.-]+)\\s*,\\s*\"lat\"\\s*:\\s*([0-9.-]+)\\s*\\}");
        std::sregex_iterator iter(waypoints_str.begin(), waypoints_str.end(), point_pattern);
        std::sregex_iterator end;
        
        for (; iter != end; ++iter) {
            try {
                double lng = std::stod((*iter)[1].str());
                double lat = std::stod((*iter)[2].str());
                waypoints.push_back(std::make_pair(lng, lat));
            } catch (const std::exception& e) {
                std::cerr << "Failed to parse waypoint: " << e.what() << std::endl;
            }
        }
    }
    
    return waypoints;
}
```

#### 1.2 请求处理增强
```cpp
// 在HandleRouteRequest中解析途经点
std::vector<std::pair<double, double>> waypoints = SimpleJSON::getWaypoints(json_body);

std::cout << "Parsed coordinates: start(" << start_lng << ", " << start_lat
          << ") end(" << end_lng << ", " << end_lat << ")";
if (!waypoints.empty()) {
    std::cout << " with " << waypoints.size() << " waypoints";
    for (size_t i = 0; i < waypoints.size(); ++i) {
        std::cout << " wp" << (i+1) << "(" << waypoints[i].first << ", " << waypoints[i].second << ")";
    }
}
std::cout << std::endl;

// 调用支持途经点的算路函数
std::string uuid = CalculateRoute(start_lng, start_lat, end_lng, end_lat, waypoints);
```

#### 1.3 路径计算增强
```cpp
std::string CalculateRoute(double start_lng, double start_lat, double end_lng, double end_lat, 
                          const std::vector<std::pair<double, double>>& waypoints = {}) {
    auto path_query = std::make_shared<PathQuery>();
    
    // 设置起点
    auto start = std::make_shared<PathLandmark>();
    start->valid = true;
    start->waypoint_type = WayPointType::kStartPoint;
    start->pt = PointLL(start_lng, start_lat);
    path_query->path_points.push_back(start);
    
    // 添加途经点（按顺序）
    for (const auto& wp : waypoints) {
        auto waypoint = std::make_shared<PathLandmark>();
        waypoint->valid = true;
        waypoint->waypoint_type = WayPointType::kViaPoint;
        waypoint->pt = PointLL(wp.first, wp.second);
        path_query->path_points.push_back(waypoint);
    }
    
    // 设置终点
    auto end = std::make_shared<PathLandmark>();
    end->valid = true;
    end->waypoint_type = WayPointType::kEndPoint;
    end->pt = PointLL(end_lng, end_lat);
    path_query->path_points.push_back(end);
    
    // 请求路径
    std::string uuid = path_interface_->RequestPath(path_query);
    // ...
}
```

### 2. **JavaScript客户端修改 (cpp-route-client.js)**

#### 2.1 函数签名更新
```javascript
/**
 * 计算路径（支持途经点）
 * @param {number} startLng - 起点经度
 * @param {number} startLat - 起点纬度
 * @param {number} endLng - 终点经度
 * @param {number} endLat - 终点纬度
 * @param {Array} waypoints - 途经点数组，格式：[{lng: number, lat: number}, ...]
 * @returns {Promise} 路径计算结果
 */
async calculateRoute(startLng, startLat, endLng, endLat, waypoints = [])
```

#### 2.2 请求数据构建
```javascript
// 发送算路请求
const requestData = {
    start_lng: startLng,
    start_lat: startLat,
    end_lng: endLng,
    end_lat: endLat
};

// 添加途经点数据
if (waypoints && waypoints.length > 0) {
    requestData.waypoints = waypoints;
}
```

#### 2.3 日志增强
```javascript
let logData = { startLng, startLat, endLng, endLat };
if (waypoints && waypoints.length > 0) {
    logData.waypoints = waypoints;
    console.log(`Path算路请求 (含${waypoints.length}个途经点):`, logData);
} else {
    console.log('Path算路请求:', logData);
}
```

### 3. **主程序集成修改 (main.js)**

#### 3.1 途经点数据准备
```javascript
// Path算路（如果服务可用）
if (cppRouteClient) {
    const cppServerAvailable = await cppRouteClient.checkServerStatus();
    if (cppServerAvailable) {
        updateStatus('正在计算Path路径...', 'info');
        
        // 准备途经点数据（C++算路使用WGS84坐标）
        const cppWaypoints = waypointCoords.map(coord => ({
            lng: coord[0],
            lat: coord[1]
        }));
        
        promises.push(
            cppRouteClient.calculateRoute(startCoords[0], startCoords[1], endCoords[0], endCoords[1], cppWaypoints)
                .then(route => ({ source: 'cpp', route }))
                .catch(error => ({ source: 'cpp', error }))
        );
    }
}
```

## 📊 数据流程

### 1. **请求数据格式**
```json
{
    "start_lng": 116.407400,
    "start_lat": 39.904200,
    "end_lng": 116.417400,
    "end_lat": 39.914200,
    "waypoints": [
        {"lng": 116.410000, "lat": 39.907000},
        {"lng": 116.413000, "lat": 39.910000}
    ]
}
```

### 2. **C++路径查询构建**
```
PathQuery:
├── PathLandmark[0]: StartPoint (116.407400, 39.904200)
├── PathLandmark[1]: ViaPoint  (116.410000, 39.907000)
├── PathLandmark[2]: ViaPoint  (116.413000, 39.910000)
└── PathLandmark[3]: EndPoint  (116.417400, 39.914200)
```

### 3. **坐标系处理**
- **JavaScript前端**: WGS84坐标系
- **高德算路**: GCJ02坐标系（需要转换）
- **C++算路**: WGS84坐标系（无需转换）

## ✅ 功能特性

### 1. **完整的途经点支持**
- ✅ 支持多个途经点（理论上无限制，实际受算路引擎限制）
- ✅ 按照起点→途经点1→途经点2→...→终点的顺序规划路径
- ✅ 与高德算路的途经点功能保持一致

### 2. **向后兼容**
- ✅ 不传递途经点时，功能与原来完全一致
- ✅ 途经点参数为可选参数，默认为空数组
- ✅ 现有的双算路对比功能不受影响

### 3. **错误处理**
- ✅ 途经点解析失败时的错误处理
- ✅ 无效途经点坐标的过滤
- ✅ 详细的日志输出便于调试

### 4. **性能优化**
- ✅ 只在有途经点时才进行额外处理
- ✅ 高效的正则表达式解析
- ✅ 最小化内存分配

## 🧪 测试用例

### 1. **基本功能测试**
```javascript
// 无途经点（向后兼容）
await cppRouteClient.calculateRoute(116.407400, 39.904200, 116.417400, 39.914200);

// 单个途经点
await cppRouteClient.calculateRoute(
    116.407400, 39.904200, 
    116.417400, 39.914200,
    [{lng: 116.410000, lat: 39.907000}]
);

// 多个途经点
await cppRouteClient.calculateRoute(
    116.407400, 39.904200, 
    116.417400, 39.914200,
    [
        {lng: 116.410000, lat: 39.907000},
        {lng: 116.413000, lat: 39.910000}
    ]
);
```

### 2. **集成测试**
```javascript
// 在main.js中通过右键菜单添加途经点后进行算路
1. 右键设置起点
2. 右键添加途经点1
3. 右键添加途经点2
4. 右键设置终点
5. 点击"开始算路"按钮
6. 验证高德和C++算路都能正确处理途经点
```

### 3. **边界条件测试**
- 空途经点数组
- 无效的途经点坐标
- 大量途经点（性能测试）
- 途经点与起终点重合

## 🔗 与现有功能的集成

### 1. **右键菜单集成**
- ✅ "添加途经点"功能已存在
- ✅ 途经点标记显示正常
- ✅ 途经点坐标管理完善

### 2. **双算路对比**
- ✅ 高德算路：支持途经点（已有功能）
- ✅ C++算路：支持途经点（新增功能）
- ✅ 结果对比：可以比较两种算路的途经点处理差异

### 3. **坐标管理**
- ✅ 途经点坐标自动记录到历史
- ✅ 支持从历史记录恢复途经点
- ✅ 途经点的添加、删除、清空功能完善

## 🚀 使用示例

### 1. **前端调用示例**
```javascript
// 在calculateRoute函数中
const waypointCoords = waypoints.map(wp => wp.coordinates);
const cppWaypoints = waypointCoords.map(coord => ({
    lng: coord[0],
    lat: coord[1]
}));

const cppRoute = await cppRouteClient.calculateRoute(
    startCoords[0], startCoords[1], 
    endCoords[0], endCoords[1], 
    cppWaypoints
);
```

### 2. **服务器处理示例**
```cpp
// C++服务器自动解析JSON中的waypoints字段
{
    "start_lng": 116.407400,
    "start_lat": 39.904200,
    "waypoints": [
        {"lng": 116.410000, "lat": 39.907000},
        {"lng": 116.413000, "lat": 39.910000}
    ],
    "end_lng": 116.417400,
    "end_lat": 39.914200
}

// 自动构建PathQuery并按顺序添加所有点
```

---

🎯 **C++算路途经点功能开发完成！现在C++算路服务与高德算路具备了相同的途经点处理能力，支持完整的多点路径规划功能！**
