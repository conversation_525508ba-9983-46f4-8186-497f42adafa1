# C++算路策略功能说明

## 📋 功能概述

为C++算路系统添加算路策略选择功能，支持最短时间优先、最短距离优先、高速优先、避开收费路段四种策略，与高德算路形成完整的双算路对比系统。

## 🎯 策略类型

根据`src/path/include/path_def.h`中的`PathStrategy`枚举：

```cpp
enum class PathStrategy {
    kTimeFirst = 0,      // 最短时间优先
    kDistanceFirst = 1,  // 最短距离优先
    KHighWayFirst,       // 高速优先
    kAvoidToll,          // 避开收费路段
};
```

### 策略说明
- **🕐 最短时间优先 (0)**: 优化行驶时间，选择最快路径
- **📏 最短距离优先 (1)**: 优化行驶距离，选择最短路径
- **🛣️ 高速优先 (2)**: 优先选择高速公路路径
- **💰 避开收费路段 (3)**: 避免通过收费道路

## 🔧 实现架构

### 1. **前端界面 (index.html)**

#### 策略选择器
```html
<h4 style="margin: 15px 0 5px 0; font-size: 14px; color: #333;">⚙️ 算路策略</h4>
<div class="strategy-selection">
    <select id="routeStrategy" class="strategy-select">
        <option value="0">⏱️ 最短时间优先</option>
        <option value="1">📏 最短距离优先</option>
        <option value="2">🛣️ 高速优先</option>
        <option value="3">💰 避开收费路段</option>
    </select>
    <div class="strategy-help">
        <p style="font-size: 11px; color: #999; margin: 5px 0 0 0;">
            💡 策略仅适用于C++算路引擎
        </p>
    </div>
</div>
```

#### 样式设计
```css
.strategy-select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #d9d9d9;
    border-radius: 3px;
    font-size: 12px;
    background: white;
    cursor: pointer;
}

.strategy-select:focus {
    border-color: #1890ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
```

### 2. **JavaScript客户端 (cpp-route-client.js)**

#### 函数签名更新
```javascript
/**
 * 计算路径（支持途经点和算路策略）
 * @param {number} startLng - 起点经度
 * @param {number} startLat - 起点纬度
 * @param {number} endLng - 终点经度
 * @param {number} endLat - 终点纬度
 * @param {Array} waypoints - 途经点数组
 * @param {number} strategy - 算路策略：0=最短时间，1=最短距离，2=高速优先，3=避开收费
 * @returns {Promise} 路径计算结果
 */
async calculateRoute(startLng, startLat, endLng, endLat, waypoints = [], strategy = 0)
```

#### 请求数据构建
```javascript
const requestData = {
    start_lng: startLng,
    start_lat: startLat,
    end_lng: endLng,
    end_lat: endLat,
    strategy: strategy  // 新增策略参数
};

// 添加途经点数据
if (waypoints && waypoints.length > 0) {
    requestData.waypoints = waypoints;
}
```

### 3. **主程序集成 (main.js)**

#### 策略参数传递
```javascript
// 获取算路策略
const strategySelect = document.getElementById('routeStrategy');
const strategy = strategySelect ? parseInt(strategySelect.value) : 0;

// 调用C++算路
promises.push(
    cppRouteClient.calculateRoute(
        startCoords[0], startCoords[1], 
        endCoords[0], endCoords[1], 
        cppWaypoints, 
        strategy  // 传递策略参数
    )
    .then(route => ({ source: 'cpp', route }))
    .catch(error => ({ source: 'cpp', error }))
);
```

### 4. **C++服务器端 (simple_route_server.cpp)**

#### JSON解析增强
```cpp
// 新增整数解析函数
static int getInt(const std::string& json, const std::string& key, int defaultValue = 0) {
    std::regex pattern("\"" + key + "\"\\s*:\\s*([0-9-]+)");
    std::smatch match;
    if (std::regex_search(json, match, pattern)) {
        try {
            return std::stoi(match[1].str());
        } catch (const std::exception& e) {
            std::cerr << "Failed to parse int for key " << key << ": " << e.what() << std::endl;
            return defaultValue;
        }
    }
    return defaultValue;
}
```

#### 请求处理增强
```cpp
// 解析算路策略
int strategy = SimpleJSON::getInt(json_body, "strategy", 0);

std::cout << "Parsed coordinates: start(" << start_lng << ", " << start_lat
          << ") end(" << end_lng << ", " << end_lat << ") strategy(" << strategy << ")";

// 调用支持策略的算路函数
std::string uuid = CalculateRoute(start_lng, start_lat, end_lng, end_lat, waypoints, strategy);
```

#### PathQuery策略设置
```cpp
std::string CalculateRoute(double start_lng, double start_lat, double end_lng, double end_lat, 
                          const std::vector<std::pair<double, double>>& waypoints = {}, int strategy = 0) {
    // 创建路径查询
    auto path_query = std::make_shared<PathQuery>();
    
    // 设置算路策略
    path_query->strategy = static_cast<PathStrategy>(strategy);
    path_query->trigger = PathTrigger::kInitialRouting;
    path_query->mode = PathMode::kOffline;
    path_query->date_option.type = DateTimeType::kCurrent;
    
    // 设置起点、途经点、终点...
}
```

## 📊 数据流程

### 1. **请求数据格式**
```json
{
    "start_lng": 116.407400,
    "start_lat": 39.904200,
    "end_lng": 116.427400,
    "end_lat": 39.924200,
    "waypoints": [
        {"lng": 116.417400, "lat": 39.914200}
    ],
    "strategy": 1
}
```

### 2. **C++处理流程**
```
JSON解析 → 策略参数提取 → PathQuery构建 → 策略设置 → 算路执行 → 结果返回
```

### 3. **日志输出示例**
```
Parsed coordinates: start(116.407, 39.9042) end(116.427, 39.9242) strategy(1) with 1 waypoints wp1(116.417, 39.9142)
Creating route with 3 points (Strategy: DistanceFirst):
  Start: (116.407, 39.9042)
  Waypoint 1: (116.417, 39.9142)
  End: (116.427, 39.9242)
```

## ✅ 功能特性

### 1. **完整的策略支持**
- ✅ 支持所有4种PathStrategy策略
- ✅ 策略参数验证和默认值处理
- ✅ 详细的策略日志输出

### 2. **用户界面集成**
- ✅ 直观的策略选择下拉框
- ✅ 清晰的策略图标和说明
- ✅ 响应式设计和焦点样式

### 3. **向后兼容性**
- ✅ 不传递策略时默认使用最短时间优先
- ✅ 现有的途经点功能完全兼容
- ✅ 双算路对比功能正常工作

### 4. **错误处理**
- ✅ 无效策略值的默认处理
- ✅ JSON解析失败的容错机制
- ✅ 详细的错误日志记录

## 🧪 测试功能

### 1. **测试页面增强**
- `test-cpp-waypoints.html`: 新增策略测试功能
- 支持单独测试每种策略
- 支持批量测试所有策略并对比结果

### 2. **测试用例**

#### 基本策略测试
```javascript
// 测试最短时间优先
await cppClient.calculateRoute(startLng, startLat, endLng, endLat, waypoints, 0);

// 测试最短距离优先
await cppClient.calculateRoute(startLng, startLat, endLng, endLat, waypoints, 1);

// 测试高速优先
await cppClient.calculateRoute(startLng, startLat, endLng, endLat, waypoints, 2);

// 测试避开收费路段
await cppClient.calculateRoute(startLng, startLat, endLng, endLat, waypoints, 3);
```

#### 策略对比测试
```javascript
async function testAllStrategies() {
    const strategyNames = ['最短时间优先', '最短距离优先', '高速优先', '避开收费路段'];
    const results = [];
    
    for (let strategy = 0; strategy < 4; strategy++) {
        const result = await cppClient.calculateRoute(
            startLng, startLat, endLng, endLat, waypoints, strategy
        );
        results.push({
            strategy: strategy,
            name: strategyNames[strategy],
            distance: result.totalDistance,
            duration: result.totalTime
        });
    }
    
    // 输出对比结果
    console.log('策略对比结果:', results);
}
```

## 🔗 与现有功能的集成

### 1. **双算路对比**
- ✅ 高德算路：使用固定策略
- ✅ C++算路：支持用户选择策略
- ✅ 结果对比：可以比较不同策略的差异

### 2. **途经点支持**
- ✅ 所有策略都支持途经点算路
- ✅ 策略影响整个路径规划过程
- ✅ 途经点顺序保持不变

### 3. **用户体验**
- ✅ 策略选择位置合理（手动坐标输入区域）
- ✅ 策略说明清晰易懂
- ✅ 操作流程简单直观

## 🚀 使用示例

### 1. **前端使用**
```javascript
// 在主页面中
1. 设置起点和终点坐标
2. 选择算路策略（最短时间优先/最短距离优先/高速优先/避开收费路段）
3. 可选：添加途经点
4. 点击"开始算路"按钮
5. 查看双算路对比结果
```

### 2. **API调用**
```javascript
// 直接调用C++客户端
const result = await cppRouteClient.calculateRoute(
    116.407400, 39.904200,  // 起点
    116.427400, 39.924200,  // 终点
    [                       // 途经点
        {lng: 116.417400, lat: 39.914200}
    ],
    1                       // 策略：最短距离优先
);
```

## 📈 性能优化

### 1. **策略处理优化**
- ✅ 只在有策略参数时进行额外处理
- ✅ 高效的整数解析和验证
- ✅ 最小化内存分配

### 2. **日志优化**
- ✅ 策略名称映射避免重复计算
- ✅ 条件日志输出减少性能影响
- ✅ 结构化日志便于调试

---

🎯 **C++算路策略功能开发完成！现在用户可以通过前端界面选择不同的算路策略，C++算路引擎会根据选择的策略进行路径规划，与高德算路形成完整的双算路对比系统！**
