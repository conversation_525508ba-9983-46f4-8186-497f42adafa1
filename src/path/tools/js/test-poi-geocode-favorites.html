<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POI搜索和地理编码收藏功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .test-section {
            padding: 20px;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .test-section:last-child {
            border-bottom: none;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            border-left: 4px solid #1890ff;
            padding-left: 10px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }
        
        .test-item {
            background: #fafafa;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 15px;
        }
        
        .test-item h4 {
            margin: 0 0 10px 0;
            color: #1890ff;
            font-size: 14px;
        }
        
        .test-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-top: 10px;
        }
        
        .btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }
        
        .btn-success {
            background: #52c41a;
            border-color: #52c41a;
            color: white;
        }
        
        .btn-warning {
            background: #faad14;
            border-color: #faad14;
            color: white;
        }
        
        .result {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .result.error {
            background: #fff2f0;
            border-color: #ffccc7;
        }
        
        .input-group {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .input-group input {
            flex: 1;
            padding: 6px 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .favorites-list {
            max-height: 150px;
            overflow-y: auto;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .favorite-item {
            padding: 8px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 11px;
        }
        
        .favorite-item:last-child {
            border-bottom: none;
        }
        
        .favorite-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }
        
        .favorite-coords {
            color: #666;
            font-family: monospace;
            margin-bottom: 2px;
        }
        
        .favorite-note {
            color: #999;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 POI搜索和地理编码收藏功能测试</h1>
            <p>测试POI搜索结果和地理编码结果的收藏功能</p>
        </div>

        <div class="test-section">
            <h3>🔍 POI搜索收藏测试</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>模拟POI搜索结果</h4>
                    <div id="mockPOIResults">
                        <div class="poi-item" style="border: 1px solid #e8e8e8; padding: 8px; margin: 5px 0; border-radius: 4px;">
                            <div style="font-weight: 500;">北京天安门</div>
                            <div style="font-size: 11px; color: #666;">北京市东城区东长安街</div>
                            <div style="font-size: 10px; color: #999;">距离: 1.2km</div>
                            <div class="test-actions">
                                <button class="btn btn-success" onclick="testPOIFavorite('天安门', 116.4074, 39.9042, '北京市东城区东长安街')">设为起点</button>
                                <button class="btn btn-warning" onclick="testPOIFavorite('天安门', 116.4074, 39.9042, '北京市东城区东长安街')">设为终点</button>
                                <button class="btn" onclick="testPOIFavorite('天安门', 116.4074, 39.9042, '北京市东城区东长安街')">查看位置</button>
                                <button class="btn" onclick="testPOIFavorite('天安门', 116.4074, 39.9042, '北京市东城区东长安街')" style="background: #faad14; color: white;">⭐ 收藏</button>
                            </div>
                        </div>
                        <div class="poi-item" style="border: 1px solid #e8e8e8; padding: 8px; margin: 5px 0; border-radius: 4px;">
                            <div style="font-weight: 500;">上海人民广场</div>
                            <div style="font-size: 11px; color: #666;">上海市黄浦区人民大道</div>
                            <div style="font-size: 10px; color: #999;">距离: 2.5km</div>
                            <div class="test-actions">
                                <button class="btn btn-success" onclick="testPOIFavorite('上海人民广场', 121.4737, 31.2304, '上海市黄浦区人民大道')">设为起点</button>
                                <button class="btn btn-warning" onclick="testPOIFavorite('上海人民广场', 121.4737, 31.2304, '上海市黄浦区人民大道')">设为终点</button>
                                <button class="btn" onclick="testPOIFavorite('上海人民广场', 121.4737, 31.2304, '上海市黄浦区人民大道')">查看位置</button>
                                <button class="btn" onclick="testPOIFavorite('上海人民广场', 121.4737, 31.2304, '上海市黄浦区人民大道')" style="background: #faad14; color: white;">⭐ 收藏</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="test-item">
                    <h4>POI收藏测试结果</h4>
                    <div class="test-actions">
                        <button class="btn btn-primary" onclick="showPOIFavorites()">显示POI收藏</button>
                        <button class="btn" onclick="clearTestResults()">清空结果</button>
                    </div>
                    <div id="poiFavoriteResult" class="result">点击"⭐ 收藏"按钮测试POI收藏功能</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📍 地理编码收藏测试</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>地理编码测试</h4>
                    <div class="input-group">
                        <input type="text" id="testAddress" placeholder="输入地址，如：北京天安门" value="北京天安门">
                        <button class="btn btn-primary" onclick="testGeocode()">地址转坐标</button>
                    </div>
                    <div id="geocodeResult" class="result">等待地理编码测试...</div>
                </div>
                
                <div class="test-item">
                    <h4>逆地理编码测试</h4>
                    <div class="input-group">
                        <input type="number" id="testLng" placeholder="经度" value="116.4074" step="0.000001">
                        <input type="number" id="testLat" placeholder="纬度" value="39.9042" step="0.000001">
                        <button class="btn btn-primary" onclick="testReverseGeocode()">坐标转地址</button>
                    </div>
                    <div id="reverseGeocodeResult" class="result">等待逆地理编码测试...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>⭐ 收藏夹管理测试</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>收藏夹操作</h4>
                    <div class="test-actions">
                        <button class="btn btn-primary" onclick="showAllFavorites()">显示所有收藏</button>
                        <button class="btn" onclick="exportTestFavorites()">导出收藏</button>
                        <button class="btn" onclick="clearAllFavorites()">清空收藏</button>
                    </div>
                    <div id="favoritesDisplay" class="favorites-list">
                        <div style="padding: 20px; text-align: center; color: #999;">暂无收藏数据</div>
                    </div>
                </div>
                
                <div class="test-item">
                    <h4>测试统计</h4>
                    <div id="testStats" class="result">
                        <strong>测试统计信息:</strong><br>
                        POI收藏次数: <span id="poiCount">0</span><br>
                        地理编码收藏次数: <span id="geocodeCount">0</span><br>
                        总收藏数量: <span id="totalCount">0</span><br>
                        最后操作时间: <span id="lastTime">未操作</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟全局变量和函数
        let testFavorites = [];
        let testStats = {
            poiCount: 0,
            geocodeCount: 0,
            totalCount: 0,
            lastTime: null
        };

        // 模拟坐标管理器
        const mockCoordinateManager = {
            addToFavorites: function(lng, lat, name, note) {
                const favorite = {
                    id: Date.now(),
                    lng: lng,
                    lat: lat,
                    name: name,
                    note: note,
                    time: new Date().toLocaleString('zh-CN')
                };
                testFavorites.push(favorite);
                testStats.totalCount++;
                testStats.lastTime = favorite.time;
                updateTestStats();
                return true;
            }
        };

        // 模拟坐标转换器
        const mockCoordinateConverter = {
            gcj02ToWgs84: function(lng, lat) {
                // 简单的模拟转换（实际应用中需要精确转换）
                return [lng - 0.0065, lat - 0.006];
            },
            wgs84ToGcj02: function(lng, lat) {
                return [lng + 0.0065, lat + 0.006];
            }
        };

        // 模拟状态更新函数
        function updateStatus(message, type) {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 测试POI收藏功能
        function testPOIFavorite(name, lng, lat, address) {
            const note = `POI搜索结果 - ${address}`;
            
            // 模拟GCJ02到WGS84转换
            const wgs84Coords = mockCoordinateConverter.gcj02ToWgs84(lng, lat);
            
            // 添加到收藏
            if (mockCoordinateManager.addToFavorites(wgs84Coords[0], wgs84Coords[1], name, note)) {
                testStats.poiCount++;
                updateTestStats();
                
                const result = document.getElementById('poiFavoriteResult');
                result.innerHTML = `
                    <strong>✅ POI收藏成功!</strong><br>
                    名称: ${name}<br>
                    原始坐标(GCJ02): ${lat.toFixed(6)}, ${lng.toFixed(6)}<br>
                    收藏坐标(WGS84): ${wgs84Coords[1].toFixed(6)}, ${wgs84Coords[0].toFixed(6)}<br>
                    地址: ${address}<br>
                    时间: ${new Date().toLocaleString('zh-CN')}
                `;
                result.className = 'result';
            }
        }

        // 测试地理编码
        function testGeocode() {
            const address = document.getElementById('testAddress').value.trim();
            if (!address) {
                document.getElementById('geocodeResult').innerHTML = '❌ 请输入地址';
                return;
            }

            // 模拟地理编码结果
            const mockResult = {
                formattedAddress: address,
                location: { lng: 116.4074, lat: 39.9042 },
                level: '门牌号'
            };

            const resultDiv = document.getElementById('geocodeResult');
            resultDiv.innerHTML = `
                <strong>地理编码结果:</strong><br>
                <strong>地址:</strong> ${mockResult.formattedAddress}<br>
                <strong>GCJ02坐标:</strong> ${mockResult.location.lat.toFixed(6)}, ${mockResult.location.lng.toFixed(6)}<br>
                <strong>置信度:</strong> ${mockResult.level}<br>
                <div style="margin-top: 10px;">
                    <button class="btn btn-success" onclick="testGeocodeAction('${address}', ${mockResult.location.lng}, ${mockResult.location.lat}, false)">设为起点</button>
                    <button class="btn btn-warning" onclick="testGeocodeAction('${address}', ${mockResult.location.lng}, ${mockResult.location.lat}, false)">设为终点</button>
                    <button class="btn" onclick="testGeocodeAction('${address}', ${mockResult.location.lng}, ${mockResult.location.lat}, false)">查看位置</button>
                    <button class="btn" onclick="testGeocodeAction('${address}', ${mockResult.location.lng}, ${mockResult.location.lat}, false)" style="background: #faad14; color: white;">⭐ 收藏</button>
                </div>
            `;
        }

        // 测试逆地理编码
        function testReverseGeocode() {
            const lng = parseFloat(document.getElementById('testLng').value);
            const lat = parseFloat(document.getElementById('testLat').value);
            
            if (isNaN(lng) || isNaN(lat)) {
                document.getElementById('reverseGeocodeResult').innerHTML = '❌ 请输入有效坐标';
                return;
            }

            // 模拟逆地理编码结果
            const mockResult = {
                formattedAddress: '北京市东城区东长安街天安门广场',
                addressComponent: {
                    province: '北京市',
                    city: '北京市',
                    district: '东城区',
                    street: '东长安街',
                    streetNumber: ''
                }
            };

            const resultDiv = document.getElementById('reverseGeocodeResult');
            resultDiv.innerHTML = `
                <strong>逆地理编码结果:</strong><br>
                <strong>详细地址:</strong> ${mockResult.formattedAddress}<br>
                <strong>省份:</strong> ${mockResult.addressComponent.province}<br>
                <strong>城市:</strong> ${mockResult.addressComponent.city}<br>
                <strong>区县:</strong> ${mockResult.addressComponent.district}<br>
                <div style="margin-top: 10px;">
                    <button class="btn btn-success" onclick="testGeocodeAction('${mockResult.formattedAddress}', ${lng}, ${lat}, true)">设为起点</button>
                    <button class="btn btn-warning" onclick="testGeocodeAction('${mockResult.formattedAddress}', ${lng}, ${lat}, true)">设为终点</button>
                    <button class="btn" onclick="testGeocodeAction('${mockResult.formattedAddress}', ${lng}, ${lat}, true)" style="background: #faad14; color: white;">⭐ 收藏</button>
                </div>
            `;
        }

        // 测试地理编码收藏
        function testGeocodeAction(address, lng, lat, isReverse) {
            let coords, name, note;

            if (isReverse) {
                // 逆地理编码：坐标已是WGS84格式
                coords = [lng, lat];
                name = address;
                note = `逆地理编码结果 - ${address}`;
            } else {
                // 地理编码：GCJ02转WGS84
                coords = mockCoordinateConverter.gcj02ToWgs84(lng, lat);
                name = address;
                note = `地理编码结果 - ${address}`;
            }

            if (mockCoordinateManager.addToFavorites(coords[0], coords[1], name, note)) {
                testStats.geocodeCount++;
                updateTestStats();
                
                const type = isReverse ? '逆地理编码' : '地理编码';
                alert(`✅ ${type}收藏成功!\n名称: ${name}\n坐标: ${coords[1].toFixed(6)}, ${coords[0].toFixed(6)}`);
            }
        }

        // 显示所有收藏
        function showAllFavorites() {
            const display = document.getElementById('favoritesDisplay');
            
            if (testFavorites.length === 0) {
                display.innerHTML = '<div style="padding: 20px; text-align: center; color: #999;">暂无收藏数据</div>';
                return;
            }

            let html = '';
            testFavorites.forEach(fav => {
                html += `
                    <div class="favorite-item">
                        <div class="favorite-name">⭐ ${fav.name}</div>
                        <div class="favorite-coords">${fav.lat.toFixed(6)}, ${fav.lng.toFixed(6)}</div>
                        <div class="favorite-note">${fav.note}</div>
                        <div style="font-size: 10px; color: #ccc; margin-top: 2px;">收藏于 ${fav.time}</div>
                    </div>
                `;
            });
            
            display.innerHTML = html;
        }

        // 更新测试统计
        function updateTestStats() {
            document.getElementById('poiCount').textContent = testStats.poiCount;
            document.getElementById('geocodeCount').textContent = testStats.geocodeCount;
            document.getElementById('totalCount').textContent = testStats.totalCount;
            document.getElementById('lastTime').textContent = testStats.lastTime || '未操作';
        }

        // 导出测试收藏
        function exportTestFavorites() {
            if (testFavorites.length === 0) {
                alert('暂无收藏数据可导出');
                return;
            }

            const data = {
                type: 'test_coordinate_favorites',
                version: '1.0',
                exportTime: new Date().toISOString(),
                stats: testStats,
                data: testFavorites
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `test_poi_geocode_favorites_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            alert('测试收藏数据已导出');
        }

        // 清空所有收藏
        function clearAllFavorites() {
            if (confirm('确定要清空所有测试收藏吗？')) {
                testFavorites = [];
                testStats = { poiCount: 0, geocodeCount: 0, totalCount: 0, lastTime: null };
                updateTestStats();
                showAllFavorites();
                alert('所有测试收藏已清空');
            }
        }

        // 清空测试结果
        function clearTestResults() {
            document.getElementById('poiFavoriteResult').innerHTML = '点击"⭐ 收藏"按钮测试POI收藏功能';
            document.getElementById('geocodeResult').innerHTML = '等待地理编码测试...';
            document.getElementById('reverseGeocodeResult').innerHTML = '等待逆地理编码测试...';
        }

        // 显示POI收藏
        function showPOIFavorites() {
            const poiFavorites = testFavorites.filter(fav => fav.note.includes('POI搜索结果'));
            const result = document.getElementById('poiFavoriteResult');
            
            if (poiFavorites.length === 0) {
                result.innerHTML = '暂无POI收藏记录';
                return;
            }

            let html = '<strong>POI收藏列表:</strong><br>';
            poiFavorites.forEach(fav => {
                html += `• ${fav.name} (${fav.lat.toFixed(6)}, ${fav.lng.toFixed(6)})<br>`;
            });
            
            result.innerHTML = html;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTestStats();
            console.log('POI搜索和地理编码收藏功能测试页面已加载');
        });
    </script>
</body>
</html>
