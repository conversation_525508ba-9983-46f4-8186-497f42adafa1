# 手动坐标输入功能说明

## 📋 概述

在双算路规划系统中新增了手动坐标输入功能，允许用户直接输入精确的经纬度坐标来设置起点和终点，提供更精确的算路控制。

## 🎯 功能特点

### 1. **精确坐标输入**
- 支持WGS84坐标系（GPS标准）
- 精度可达小数点后6位（约1米精度）
- 实时坐标验证和范围检查
- 支持经度(-180~180)和纬度(-90~90)范围

### 2. **便捷操作**
- 🟢 **起点设置**: 独立的起点坐标输入区域
- 🔴 **终点设置**: 独立的终点坐标输入区域
- 📍 **获取当前位置**: 一键获取地图中心坐标
- 🏙️ **快速填充**: 预设主要城市坐标

### 3. **智能验证**
- 实时坐标格式验证
- 坐标范围检查
- 错误提示和修正建议
- 坐标精度显示

## 🎨 界面设计

### 控制面板布局
```
📐 手动坐标输入
├── 🟢 起点坐标
│   ├── [经度输入框] [纬度输入框]
│   ├── [设置起点] [获取当前位置]
│   └── 起点状态显示
├── 🔴 终点坐标  
│   ├── [经度输入框] [纬度输入框]
│   ├── [设置终点] [获取当前位置]
│   └── 终点状态显示
├── 🛣️ 算路控制
│   ├── [开始算路] [清空输入]
│   └── 算路状态显示
└── 🏙️ 快速填充
    └── [北京] [上海] [广州] [深圳]
```

### 样式特点
- **清晰分区**: 起点、终点、操作区域明确分离
- **颜色编码**: 绿色起点、红色终点，与地图标记一致
- **响应式设计**: 适配不同屏幕尺寸
- **状态反馈**: 实时显示操作结果和错误信息

## 📖 使用方法

### 基本操作流程

#### 1. **设置起点坐标**
```
输入经纬度 → 点击"设置起点" → 地图显示起点标记
```

**操作步骤**:
1. 在"起点坐标"区域输入经度和纬度
2. 点击"设置起点"按钮
3. 系统验证坐标并在地图上显示绿色起点标记

#### 2. **设置终点坐标**
```
输入经纬度 → 点击"设置终点" → 地图显示终点标记
```

**操作步骤**:
1. 在"终点坐标"区域输入经度和纬度
2. 点击"设置终点"按钮
3. 系统验证坐标并在地图上显示红色终点标记

#### 3. **执行算路**
```
设置起终点 → 点击"开始算路" → 显示双算路结果
```

**操作步骤**:
1. 确保已设置起点和终点
2. 点击"开始算路"按钮
3. 系统自动执行高德和C++双算路对比

### 快捷操作

#### 1. **获取当前位置**
- 点击"获取当前位置"按钮
- 自动填充当前地图中心的坐标
- 适用于基于当前视野设置起终点

#### 2. **快速填充城市坐标**
- 点击城市按钮（北京、上海、广州、深圳）
- 自动填充对应城市的标志性坐标
- 方便快速测试和演示

#### 3. **清空输入**
- 点击"清空输入"按钮
- 清除所有坐标输入和地图标记
- 重置到初始状态

## 🔧 技术实现

### 坐标验证逻辑
```javascript
// 坐标格式验证
if (isNaN(lng) || isNaN(lat)) {
    updateStatus('请输入有效的坐标', 'error');
    return;
}

// 坐标范围验证
if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
    updateStatus('坐标范围错误：经度(-180~180)，纬度(-90~90)', 'error');
    return;
}
```

### 坐标设置流程
```javascript
// 设置起点
function setStartPointFromInput() {
    const coords = [lng, lat];
    const coordsText = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    setStartPointByCoords(coords, `手动输入坐标: ${coordsText}`);
}
```

### 与地图系统集成
```javascript
// 在地图上显示标记
setStartPointByCoords(coords, name);
setEndPointByCoords(coords, name);

// 执行双算路
calculateRoute();
```

## 📊 坐标系说明

### WGS84坐标系
- **定义**: 世界大地坐标系，GPS原始坐标系
- **精度**: 小数点后6位约1米精度
- **范围**: 经度(-180°~180°)，纬度(-90°~90°)
- **格式**: 十进制度数 (Decimal Degrees)

### 常用城市坐标
| 城市 | 经度 | 纬度 | 地标 |
|------|------|------|------|
| 北京 | 116.4074 | 39.9042 | 天安门广场 |
| 上海 | 121.4737 | 31.2304 | 人民广场 |
| 广州 | 113.2644 | 23.1291 | 天河城 |
| 深圳 | 114.0579 | 22.5431 | 市民中心 |

## 🧪 测试功能

### 独立测试页面
- `test-coordinate-input.html`: 专门的坐标输入测试页面
- 包含完整的功能测试和验证
- 提供详细的操作反馈和错误提示

### 测试用例
1. **坐标格式测试**
   - 输入有效坐标: `116.4074, 39.9042`
   - 输入无效格式: `abc, def`
   - 输入超出范围: `200, 100`

2. **功能集成测试**
   - 设置起终点后执行算路
   - 验证地图标记显示
   - 检查算路结果准确性

3. **边界条件测试**
   - 极值坐标: `180, 90` 和 `-180, -90`
   - 零值坐标: `0, 0`
   - 高精度坐标: `116.407395, 39.904211`

## 💡 使用建议

### 1. **坐标获取方式**
- **Google Maps**: 右键点击位置查看坐标
- **百度地图**: 使用坐标拾取工具
- **GPS设备**: 直接读取GPS坐标
- **在线工具**: 地址转坐标服务

### 2. **精度选择**
- **一般用途**: 小数点后4位（约10米精度）
- **精确定位**: 小数点后6位（约1米精度）
- **科学研究**: 小数点后8位（约1厘米精度）

### 3. **常见问题避免**
- 确保经纬度顺序正确（经度在前，纬度在后）
- 注意正负号（东经为正，西经为负；北纬为正，南纬为负）
- 验证坐标是否在合理的地理范围内

## 🔗 与其他功能的集成

### POI搜索集成
- POI搜索结果可以填充到坐标输入框
- 支持从搜索结果直接设置起终点
- 坐标输入可以作为POI搜索的补充

### 地理编码集成
- 地理编码结果可以填充坐标输入
- 手动坐标可以进行逆地理编码获取地址
- 提供地址和坐标的双向转换

### 双算路系统集成
- 手动坐标作为算路的精确输入源
- 支持高德和C++算路引擎
- 提供坐标级别的算路对比

## 📝 开发说明

### 新增文件
- `test-coordinate-input.html`: 独立测试页面

### 修改文件
- `index.html`: 添加手动坐标输入界面
- `main.js`: 添加坐标输入处理函数

### 核心函数
- `setStartPointFromInput()`: 从输入设置起点
- `setEndPointFromInput()`: 从输入设置终点
- `getCurrentLocation()`: 获取当前地图位置
- `calculateRouteFromCoords()`: 基于坐标执行算路
- `clearCoordinateInputs()`: 清空坐标输入
- `fillSampleCoordinates()`: 填充示例坐标

## 🚀 未来扩展

### 可能的增强功能
1. **坐标格式支持**: 度分秒格式、UTM坐标等
2. **批量坐标导入**: 支持CSV文件批量导入坐标
3. **坐标历史记录**: 保存常用坐标点
4. **坐标收藏夹**: 收藏重要位置坐标
5. **坐标分享**: 生成坐标分享链接

---

🎯 **手动坐标输入功能为双算路系统提供了精确的位置控制能力，特别适合需要精确定位的专业应用场景！**
