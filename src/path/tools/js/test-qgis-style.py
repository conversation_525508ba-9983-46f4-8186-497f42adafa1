#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QGIS样式文件测试脚本
用于验证dual-route-comparison.qml样式文件的正确性
"""

import os
import sys
import xml.etree.ElementTree as ET
from pathlib import Path

def test_qml_file(qml_path):
    """测试QML样式文件的有效性"""
    print(f"🔍 测试QML文件: {qml_path}")
    
    if not os.path.exists(qml_path):
        print(f"❌ 文件不存在: {qml_path}")
        return False
    
    try:
        # 解析XML文件
        tree = ET.parse(qml_path)
        root = tree.getroot()
        
        # 检查根元素
        if root.tag != 'qgis':
            print(f"❌ 根元素错误: {root.tag}, 期望: qgis")
            return False
        
        print("✅ XML格式正确")
        
        # 检查渲染器类型
        renderer = root.find('.//renderer-v2')
        if renderer is None:
            print("❌ 未找到renderer-v2元素")
            return False
        
        renderer_type = renderer.get('type')
        if renderer_type != 'categorizedSymbol':
            print(f"❌ 渲染器类型错误: {renderer_type}, 期望: categorizedSymbol")
            return False
        
        print("✅ 渲染器类型正确")
        
        # 检查分类属性
        attr = renderer.get('attr')
        if attr != 'type':
            print(f"❌ 分类属性错误: {attr}, 期望: type")
            return False
        
        print("✅ 分类属性正确")
        
        # 检查类别定义
        categories = root.findall('.//category')
        expected_categories = [
            'start_point',
            'end_point', 
            'direct_line',
            'amap_route',
            'cpp_route',
            'comparison_analysis'
        ]
        
        found_categories = []
        for category in categories:
            value = category.get('value')
            if value:
                found_categories.append(value)
        
        print(f"📋 找到类别: {found_categories}")
        
        missing_categories = set(expected_categories) - set(found_categories)
        if missing_categories:
            print(f"❌ 缺少类别: {missing_categories}")
            return False
        
        print("✅ 所有类别定义正确")
        
        # 检查符号定义
        symbols = root.findall('.//symbols/symbol')
        if len(symbols) != len(expected_categories):
            print(f"❌ 符号数量错误: {len(symbols)}, 期望: {len(expected_categories)}")
            return False
        
        print("✅ 符号数量正确")
        
        # 检查特定符号属性
        symbol_checks = {
            '0': {'type': 'marker', 'name': '起点'},  # start_point
            '1': {'type': 'marker', 'name': '终点'},  # end_point
            '2': {'type': 'line', 'name': '直线连接'},    # direct_line
            '3': {'type': 'line', 'name': '高德路径'},    # amap_route
            '4': {'type': 'line', 'name': 'C++路径'},    # cpp_route
            '5': {'type': 'marker', 'name': '对比分析'}   # comparison_analysis
        }
        
        for symbol in symbols:
            symbol_name = symbol.get('name')
            symbol_type = symbol.get('type')
            
            if symbol_name in symbol_checks:
                expected_type = symbol_checks[symbol_name]['type']
                if symbol_type != expected_type:
                    print(f"❌ 符号{symbol_name}类型错误: {symbol_type}, 期望: {expected_type}")
                    return False
        
        print("✅ 符号类型正确")
        
        # 检查数据驱动属性 (对比分析点的颜色分级)
        comparison_symbol = None
        for symbol in symbols:
            if symbol.get('name') == '5':  # comparison_analysis
                comparison_symbol = symbol
                break
        
        if comparison_symbol is not None:
            data_defined = comparison_symbol.find('.//data_defined_properties')
            if data_defined is not None:
                fill_color = data_defined.find('.//Option[@name="fillColor"]')
                if fill_color is not None:
                    expression = fill_color.find('.//Option[@name="expression"]')
                    if expression is not None and 'distance_diff_percent' in expression.get('value', ''):
                        print("✅ 对比分析点颜色分级配置正确")
                    else:
                        print("⚠️  对比分析点颜色分级表达式可能有问题")
                else:
                    print("⚠️  未找到对比分析点颜色配置")
            else:
                print("⚠️  未找到数据驱动属性配置")
        
        # 检查字段配置
        field_configs = root.findall('.//fieldConfiguration/field')
        expected_fields = ['type', 'test_id', 'name', 'test_status', 'distance_diff_percent']
        
        found_fields = []
        for field in field_configs:
            field_name = field.get('name')
            if field_name:
                found_fields.append(field_name)
        
        missing_fields = set(expected_fields) - set(found_fields)
        if missing_fields:
            print(f"⚠️  缺少字段配置: {missing_fields}")
        else:
            print("✅ 字段配置完整")
        
        # 检查中文别名
        aliases = root.findall('.//aliases/alias')
        alias_count = len(aliases)
        if alias_count > 0:
            print(f"✅ 找到{alias_count}个字段别名")
        else:
            print("⚠️  未找到字段别名配置")
        
        print("🎉 QML文件测试通过!")
        return True
        
    except ET.ParseError as e:
        print(f"❌ XML解析错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def create_sample_geojson():
    """创建示例GeoJSON文件用于测试"""
    sample_data = {
        "type": "FeatureCollection",
        "name": "dual_route_test_sample",
        "crs": {
            "type": "name",
            "properties": {
                "name": "urn:ogc:def:crs:OGC:1.3:CRS84"
            }
        },
        "features": [
            {
                "type": "Feature",
                "properties": {
                    "type": "start_point",
                    "test_id": 1,
                    "name": "测试起点",
                    "test_status": "success_success",
                    "direct_distance_km": 5.2
                },
                "geometry": {
                    "type": "Point",
                    "coordinates": [121.4737, 31.2304]
                }
            },
            {
                "type": "Feature", 
                "properties": {
                    "type": "end_point",
                    "test_id": 1,
                    "name": "测试终点",
                    "test_status": "success_success",
                    "direct_distance_km": 5.2
                },
                "geometry": {
                    "type": "Point",
                    "coordinates": [121.5057, 31.2453]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "type": "comparison_analysis",
                    "test_id": 1,
                    "distance_diff_percent": 8.5,
                    "amap_distance_km": 6.2,
                    "cpp_distance_km": 5.7
                },
                "geometry": {
                    "type": "Point", 
                    "coordinates": [121.4897, 31.2378]
                }
            }
        ]
    }
    
    import json
    sample_file = "sample_dual_route_test.geojson"
    with open(sample_file, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    print(f"📄 创建示例GeoJSON文件: {sample_file}")
    return sample_file

def main():
    """主函数"""
    print("🧪 QGIS双算路对比样式文件测试")
    print("=" * 50)
    
    # 获取当前目录
    current_dir = Path(__file__).parent
    qml_file = current_dir / "dual-route-comparison.qml"
    
    # 测试QML文件
    success = test_qml_file(qml_file)
    
    if success:
        print("\n📋 测试总结:")
        print("✅ QML样式文件格式正确")
        print("✅ 所有要素类型已定义")
        print("✅ 符号样式配置完整")
        print("✅ 字段配置和别名正常")
        
        # 创建示例数据
        print("\n🔧 创建测试数据...")
        sample_file = create_sample_geojson()
        
        print(f"\n📖 使用说明:")
        print(f"1. 在QGIS中导入: {sample_file}")
        print(f"2. 应用样式文件: {qml_file}")
        print(f"3. 验证显示效果是否正确")
        print(f"4. 参考文档: QGIS-使用说明.md")
        
    else:
        print("\n❌ 测试失败，请检查QML文件")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
