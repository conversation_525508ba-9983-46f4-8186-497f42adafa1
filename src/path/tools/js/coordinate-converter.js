/**
 * 坐标转换工具
 * 支持WGS84、GCJ02、BD09坐标系之间的转换
 */

const CoordinateConverter = {
    // 常量定义
    PI: Math.PI,
    X_PI: Math.PI * 3000.0 / 180.0,
    A: 6378245.0, // 长半轴
    EE: 0.00669342162296594323, // 偏心率平方
    
    /**
     * 判断坐标是否在中国境内
     * @param {number} lng 经度
     * @param {number} lat 纬度
     * @returns {boolean}
     */
    isInChina(lng, lat) {
        return (lng > 72.004 && lng < 137.8347) && (lat > 0.8293 && lat < 55.8271);
    },
    
    /**
     * WGS84 转 GCJ02 (GPS坐标转火星坐标)
     * @param {number} lng 经度
     * @param {number} lat 纬度
     * @returns {Array} [lng, lat]
     */
    wgs84ToGcj02(lng, lat) {
        if (!this.isInChina(lng, lat)) {
            return [lng, lat];
        }
        
        let dlat = this.transformLat(lng - 105.0, lat - 35.0);
        let dlng = this.transformLng(lng - 105.0, lat - 35.0);
        
        const radlat = lat / 180.0 * this.PI;
        let magic = Math.sin(radlat);
        magic = 1 - this.EE * magic * magic;
        const sqrtmagic = Math.sqrt(magic);
        
        dlat = (dlat * 180.0) / ((this.A * (1 - this.EE)) / (magic * sqrtmagic) * this.PI);
        dlng = (dlng * 180.0) / (this.A / sqrtmagic * Math.cos(radlat) * this.PI);
        
        const mglat = lat + dlat;
        const mglng = lng + dlng;
        
        return [mglng, mglat];
    },
    
    /**
     * GCJ02 转 WGS84 (火星坐标转GPS坐标)
     * @param {number} lng 经度
     * @param {number} lat 纬度
     * @returns {Array} [lng, lat]
     */
    gcj02ToWgs84(lng, lat) {
        if (!this.isInChina(lng, lat)) {
            return [lng, lat];
        }
        
        let dlat = this.transformLat(lng - 105.0, lat - 35.0);
        let dlng = this.transformLng(lng - 105.0, lat - 35.0);
        
        const radlat = lat / 180.0 * this.PI;
        let magic = Math.sin(radlat);
        magic = 1 - this.EE * magic * magic;
        const sqrtmagic = Math.sqrt(magic);
        
        dlat = (dlat * 180.0) / ((this.A * (1 - this.EE)) / (magic * sqrtmagic) * this.PI);
        dlng = (dlng * 180.0) / (this.A / sqrtmagic * Math.cos(radlat) * this.PI);
        
        const mglat = lat - dlat;
        const mglng = lng - dlng;
        
        return [mglng, mglat];
    },

    gcj02Togcj02(lng, lat) {
        if (!this.isInChina(lng, lat)) {
            return [lng, lat];
        }

        return [lng, lat];
    },
    
    /**
     * GCJ02 转 BD09 (火星坐标转百度坐标)
     * @param {number} lng 经度
     * @param {number} lat 纬度
     * @returns {Array} [lng, lat]
     */
    gcj02ToBd09(lng, lat) {
        const z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * this.X_PI);
        const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * this.X_PI);
        const bd_lng = z * Math.cos(theta) + 0.0065;
        const bd_lat = z * Math.sin(theta) + 0.006;
        return [bd_lng, bd_lat];
    },
    
    /**
     * BD09 转 GCJ02 (百度坐标转火星坐标)
     * @param {number} lng 经度
     * @param {number} lat 纬度
     * @returns {Array} [lng, lat]
     */
    bd09ToGcj02(lng, lat) {
        const x = lng - 0.0065;
        const y = lat - 0.006;
        const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * this.X_PI);
        const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * this.X_PI);
        const gcj_lng = z * Math.cos(theta);
        const gcj_lat = z * Math.sin(theta);
        return [gcj_lng, gcj_lat];
    },
    
    /**
     * WGS84 转 BD09 (GPS坐标转百度坐标)
     * @param {number} lng 经度
     * @param {number} lat 纬度
     * @returns {Array} [lng, lat]
     */
    wgs84ToBd09(lng, lat) {
        const gcj = this.wgs84ToGcj02(lng, lat);
        return this.gcj02ToBd09(gcj[0], gcj[1]);
    },
    
    /**
     * BD09 转 WGS84 (百度坐标转GPS坐标)
     * @param {number} lng 经度
     * @param {number} lat 纬度
     * @returns {Array} [lng, lat]
     */
    bd09ToWgs84(lng, lat) {
        const gcj = this.bd09ToGcj02(lng, lat);
        return this.gcj02ToWgs84(gcj[0], gcj[1]);
    },
    
    /**
     * 纬度转换
     * @param {number} lng 经度
     * @param {number} lat 纬度
     * @returns {number}
     */
    transformLat(lng, lat) {
        let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 
                  0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
        ret += (20.0 * Math.sin(6.0 * lng * this.PI) + 20.0 * Math.sin(2.0 * lng * this.PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(lat * this.PI) + 40.0 * Math.sin(lat / 3.0 * this.PI)) * 2.0 / 3.0;
        ret += (160.0 * Math.sin(lat / 12.0 * this.PI) + 320 * Math.sin(lat * this.PI / 30.0)) * 2.0 / 3.0;
        return ret;
    },
    
    /**
     * 经度转换
     * @param {number} lng 经度
     * @param {number} lat 纬度
     * @returns {number}
     */
    transformLng(lng, lat) {
        let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 
                  0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
        ret += (20.0 * Math.sin(6.0 * lng * this.PI) + 20.0 * Math.sin(2.0 * lng * this.PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(lng * this.PI) + 40.0 * Math.sin(lng / 3.0 * this.PI)) * 2.0 / 3.0;
        ret += (150.0 * Math.sin(lng / 12.0 * this.PI) + 300.0 * Math.sin(lng / 30.0 * this.PI)) * 2.0 / 3.0;
        return ret;
    },
    
    /**
     * 计算两点间距离 (米)
     * @param {number} lng1 点1经度
     * @param {number} lat1 点1纬度
     * @param {number} lng2 点2经度
     * @param {number} lat2 点2纬度
     * @returns {number} 距离(米)
     */
    getDistance(lng1, lat1, lng2, lat2) {
        const radLat1 = lat1 * this.PI / 180.0;
        const radLat2 = lat2 * this.PI / 180.0;
        const a = radLat1 - radLat2;
        const b = (lng1 - lng2) * this.PI / 180.0;
        
        let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + 
                Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * 6378137.0; // 地球半径
        s = Math.round(s * 10000) / 10000;
        return s;
    }
};

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CoordinateConverter;
}
