// 简单的GeoJSON测试程序
#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>

#include "rapidjson/document.h"
#include "rapidjson/error/en.h"

struct SimpleRoute {
    std::string id;
    double start_lon, start_lat;
    double end_lon, end_lat;
    std::vector<std::pair<double, double>> geometry;
    double distance;
    double duration;
    std::string source;
};

bool LoadGeoJSON(const std::string& filename, std::vector<SimpleRoute>& routes) {
    std::cout << "正在读取文件: " << filename << std::endl;
    
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << filename << std::endl;
        return false;
    }
    
    std::stringstream buffer;
    buffer << file.rdbuf();
    file.close();
    std::string content = buffer.str();
    
    std::cout << "文件大小: " << content.size() << " 字节" << std::endl;
    
    rapidjson::Document doc;
    rapidjson::ParseResult result = doc.Parse(content.c_str());
    if (!result) {
        std::cerr << "JSON解析错误: " << rapidjson::GetParseError_En(result.Code()) 
                  << " (offset " << result.Offset() << ")" << std::endl;
        return false;
    }
    
    std::cout << "JSON解析成功" << std::endl;
    
    if (!doc.IsObject() || !doc.HasMember("type") || 
        !doc["type"].IsString() || std::string(doc["type"].GetString()) != "FeatureCollection" ||
        !doc.HasMember("features") || !doc["features"].IsArray()) {
        std::cerr << "不是有效的GeoJSON FeatureCollection" << std::endl;
        return false;
    }
    
    const rapidjson::Value& features = doc["features"];
    std::cout << "找到 " << features.Size() << " 个features" << std::endl;
    
    routes.clear();
    routes.reserve(features.Size());
    
    for (rapidjson::SizeType i = 0; i < features.Size(); i++) {
        const rapidjson::Value& feature = features[i];
        
        if (!feature.IsObject() || !feature.HasMember("geometry") || 
            !feature.HasMember("properties")) {
            std::cout << "跳过无效的feature " << i << std::endl;
            continue;
        }
        
        SimpleRoute route;
        route.id = "route_" + std::to_string(i);
        
        // 处理properties
        const rapidjson::Value& properties = feature["properties"];
        if (properties.IsObject()) {
            // 提取起点坐标
            if (properties.HasMember("original_start_coords") && properties["original_start_coords"].IsArray()) {
                const auto& start_coords = properties["original_start_coords"];
                if (start_coords.Size() >= 2) {
                    route.start_lon = start_coords[0].GetDouble();
                    route.start_lat = start_coords[1].GetDouble();
                }
            }
            
            // 提取终点坐标
            if (properties.HasMember("original_end_coords") && properties["original_end_coords"].IsArray()) {
                const auto& end_coords = properties["original_end_coords"];
                if (end_coords.Size() >= 2) {
                    route.end_lon = end_coords[0].GetDouble();
                    route.end_lat = end_coords[1].GetDouble();
                }
            }
            
            // 提取距离
            if (properties.HasMember("distance") && properties["distance"].IsNumber()) {
                route.distance = properties["distance"].GetDouble();
            }
            
            // 提取时间
            if (properties.HasMember("duration") && properties["duration"].IsNumber()) {
                route.duration = properties["duration"].GetDouble();
            }
            
            // 提取数据源
            if (properties.HasMember("source") && properties["source"].IsString()) {
                route.source = properties["source"].GetString();
            } else {
                route.source = "amap";
            }
        }
        
        // 处理geometry (LineString)
        const rapidjson::Value& geometry = feature["geometry"];
        if (geometry.IsObject() && geometry.HasMember("type") && 
            geometry["type"].IsString() && std::string(geometry["type"].GetString()) == "LineString" &&
            geometry.HasMember("coordinates") && geometry["coordinates"].IsArray()) {
            
            const rapidjson::Value& coordinates = geometry["coordinates"];
            route.geometry.reserve(coordinates.Size());
            
            for (rapidjson::SizeType j = 0; j < coordinates.Size(); j++) {
                const rapidjson::Value& point = coordinates[j];
                if (point.IsArray() && point.Size() >= 2) {
                    route.geometry.emplace_back(point[0].GetDouble(), point[1].GetDouble());
                }
            }
        }
        
        // 验证路线数据完整性
        if (route.start_lon != 0 && route.start_lat != 0 && 
            route.end_lon != 0 && route.end_lat != 0 && 
            !route.geometry.empty() && route.distance > 0) {
            routes.push_back(std::move(route));
        } else {
            std::cout << "跳过不完整的路线 " << i << std::endl;
        }
    }
    
    std::cout << "成功加载 " << routes.size() << " 条路线" << std::endl;
    return !routes.empty();
}

int main(int argc, char** argv) {
    std::string geojson_file = "/mnt/d/osm/map_engine/debug/amap_test/amap_routes_backup_100.geojson";
    
    if (argc >= 2) {
        geojson_file = argv[1];
    }
    
    std::cout << "简单GeoJSON测试程序" << std::endl;
    std::cout << "===================" << std::endl;
    std::cout << "GeoJSON文件: " << geojson_file << std::endl;
    std::cout << "===================" << std::endl;
    
    std::vector<SimpleRoute> routes;
    
    if (!LoadGeoJSON(geojson_file, routes)) {
        std::cerr << "加载GeoJSON文件失败" << std::endl;
        return 1;
    }
    
    // 显示前几条路线的信息
    std::cout << "\n前5条路线信息:" << std::endl;
    for (size_t i = 0; i < std::min(routes.size(), size_t(5)); i++) {
        const auto& route = routes[i];
        std::cout << "路线 " << i << ":" << std::endl;
        std::cout << "  起点: (" << route.start_lon << ", " << route.start_lat << ")" << std::endl;
        std::cout << "  终点: (" << route.end_lon << ", " << route.end_lat << ")" << std::endl;
        std::cout << "  距离: " << route.distance << " 米" << std::endl;
        std::cout << "  时间: " << route.duration << " 秒" << std::endl;
        std::cout << "  几何点数: " << route.geometry.size() << std::endl;
        std::cout << "  数据源: " << route.source << std::endl;
        std::cout << std::endl;
    }
    
    std::cout << "测试完成！" << std::endl;
    return 0;
}
