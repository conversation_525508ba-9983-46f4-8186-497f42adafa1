#ifndef SEARCH_SRC_UTILS_SIGNATURE_H
#define SEARCH_SRC_UTILS_SIGNATURE_H

#include <cstdint>

namespace aurora::search {
/**
 *  Creates a unique 64-bit signature for a given string.
 *
 *  @param[in]  source  The input string.
 *  @param[in]  len     The length of the input string.
 *  @param[out] sign1   The high 32 bits of the resulting signature.
 *  @param[out] sign2   The low 32 bits of the resulting signature.
 *  @return True if the signature was created successfully, false otherwise.
 *  @note Special cases:
 * - If len == 0, the signature will be set to 0:0.
 */
bool Create64BitSignature(const char *source, uint32_t len, uint32_t *sign1,
                          uint32_t *sign2);

bool Create32BitSignature(const char *source, uint32_t *sign);

bool Create64BitSignature(const char *source, uint64_t *sign);
}  // namespace aurora::search

#endif  // SEARCH_SRC_UTILS_SIGNATURE_H
