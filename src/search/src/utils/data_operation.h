#ifndef SEARCH_SRC_UTILS_DATA_OPERATION_H
#define SEARCH_SRC_UTILS_DATA_OPERATION_H
#include "common/search_internal_def.h"

namespace aurora::search {
class DataOperation {
 public:
  static std::string ToExternalPlaceId(uint64_t place_id);
  static uint64_t ToInternalPlaceId(const std::string& place_id);

  static std::string ToExternalCategory(uint32_t category);
  static uint32_t ToInternalCategory(const std::string& category);

  static bool TestField(uint32_t field, FieldFlagBit bit);
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_UTILS_DATA_OPERATION_H
