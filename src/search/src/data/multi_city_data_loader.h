#ifndef SEARCH_SRC_DATA_INVERT_INDEX_LOADER_H
#define SEARCH_SRC_DATA_INVERT_INDEX_LOADER_H
#include <memory>
#include <optional>

#include "cache.h"
#include "data_loader.h"

namespace aurora::search {
struct SearchConfig;

template <typename DataType>
class MultiCityDataLoader final : public DataLoader {
 public:
  explicit MultiCityDataLoader(std::shared_ptr<SearchConfig> config, int cache_size)
      : config_(std::move(config)), cache_(cache_size) {}
  void Init(const std::optional<std::string>& national_city, const std::string& initial_city) {
    if (national_city.has_value()) {
      auto national = Prepare(national_city.value());
      cache_.Put(national_city.value(), national, CityType::kNational);
    }
    auto primary = Prepare(initial_city);
    cache_.Put(initial_city, primary, CityType::kPrimary);
  }

  ~MultiCityDataLoader() override = default;

  void InvalidateCities(const CityVector& invalid_cities) override {
    std::lock_guard lock(cache_mutex_);
    for (auto& city : invalid_cities) {
      cache_.DeleteByKey(city);
    }
  }

  std::shared_ptr<DataType> Get(const std::string& city, CityType city_type) {
    std::lock_guard lock(cache_mutex_);
    auto cached = cache_.Get(city, city_type);
    if (cached != nullptr) {
      return cached;
    }
    auto data = Prepare(city);
    cache_.Put(city, data, city_type);
    return data;
  }

 private:
  std::shared_ptr<DataType> Prepare(const std::string& city_code) const {
    auto data = std::make_shared<DataType>(config_, city_code);
    if (!data->Load()) {
      LOG_WARN("Failed to load invert index for city {}", city_code);
      return nullptr;
    }

    return data;
  }

  std::shared_ptr<SearchConfig> config_;
  std::mutex cache_mutex_;
  LRUCache<std::string, std::shared_ptr<DataType>> cache_;
};
}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_INVERT_INDEX_LOADER_H
