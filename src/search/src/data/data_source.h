#ifndef SEARCH_SRC_DATA_DATA_SOURCE_H
#define SEARCH_SRC_DATA_DATA_SOURCE_H

#include <memory>

#include "data_def.h"

namespace aurora::search {
class InvertIndex;
class PlaceTable;
class PrefixIndex;

struct DataSource {
  std::string city_code;
  CityType city_type;
};

struct AutocompleteDataSource : DataSource {
  std::shared_ptr<PrefixIndex> prefix_index;
  std::shared_ptr<PlaceTable> place_table;
};

struct SearchByTextDataSource : DataSource {
  std::shared_ptr<InvertIndex> invert_index;
  std::shared_ptr<PlaceTable> place_table;
};

struct DetailDataSource : DataSource {
  std::shared_ptr<PlaceTable> place_table;
  std::vector<std::string> place_ids;
};

struct ReverseGeocodeDataSource : DataSource {
  std::shared_ptr<PlaceTable> place_table;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_DATA_SOURCE_H
