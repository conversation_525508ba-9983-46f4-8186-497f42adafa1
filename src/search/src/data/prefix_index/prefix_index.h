﻿#ifndef SEARCH_SRC_DATA_PREFIX_INDEX_PREFIX_INDEX_H
#define SEARCH_SRC_DATA_PREFIX_INDEX_PREFIX_INDEX_H

#include <memory>

#include "data/search_data.h"

namespace aurora::search {
class TransTrie;
class NativeTrie;

class PlaceTable;
class PlaceDetailReader;
struct PrefixIndexLayout;
struct SearchConfig;

class PrefixIndex final : public SearchData {
 public:
  PrefixIndex(std::shared_ptr<SearchConfig> config, const std::string& city_code);
  ~PrefixIndex() override;
  bool Load() override;

  std::shared_ptr<NativeTrie> native_trie() const { return native_trie_; }
  std::shared_ptr<TransTrie> trans_trie() const { return trans_trie_; }

  // TODO(ZQ): delete
  uint32_t TempGetMappingOffset(uint32_t k) const;

 private:
  std::shared_ptr<PrefixIndexLayout> folder_layout_;
  std::shared_ptr<NativeTrie> native_trie_;
  std::shared_ptr<TransTrie> trans_trie_;

  void LoadTempFile(const std::string& index_directory);
  uint32_t* poi_lines_;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_PREFIX_INDEX_PREFIX_INDEX_H
