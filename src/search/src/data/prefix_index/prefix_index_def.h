﻿#ifndef SEARCH_SRC_DATA_PREFIX_INDEX_PREFIX_INDEX_DEF_H
#define SEARCH_SRC_DATA_PREFIX_INDEX_PREFIX_INDEX_DEF_H

#include <cstdint>
namespace aurora::search {

constexpr int kMaxCharLen = 4;  // 单个字最大字节数

// 检索结果信息
struct IndexResult {
  uint32_t deep : 6;       // 对应结果的深度
  uint32_t index_id : 26;  // 对应结果的行号
  IndexResult() : deep(0), index_id(0) {}
};

struct NativeTrieNode {
  char word[kMaxCharLen];  // 字

  uint32_t child_off;  // 子节点在下一层偏移
  uint32_t child_len;  // 子节点在下一层数量

  uint32_t geo_off;  // geohash索引对应位置

  uint32_t poi_off;  // 定位poi链
  uint32_t poi_len;
};

struct TransTrieNode {
  uint32_t child_off;  // 定位下一级
  uint32_t child_len;

  uint32_t py_hz_off;  // 定位到汉字
  uint32_t py_hz_len;

  char code;  // 拼音的字母
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_PREFIX_INDEX_PREFIX_INDEX_DEF_H
