﻿#include "mixed_strategy.h"

#include <logger.h>

#include <cstring>

#include "data/prefix_index/native_trie.h"
#include "data/prefix_index/prefix_index.h"
#include "data/prefix_index/prefix_index_def.h"
#include "data/prefix_index/trans_trie.h"
#include "utils/string_utils.h"

using namespace aurora::search;

namespace aurora::search {

int MixedSearch::Extend(const std::shared_ptr<PrefixIndex>& prefix_index, const char* words,
                        std::vector<IndexResult>* index_result) {
  std::vector<IndexResult> filter;
  std::vector<IndexResult> all_result;
  std::vector<IndexResult> one_result;
  const auto native_trie = prefix_index->native_trie();
  const auto trans_trie = prefix_index->trans_trie();

  std::vector<const TransTrieNode*> trans_result;
  std::vector<const TransTrieNode*> trans_expand;

  char new_words[100];
  int off = 0;
  while (true) {
    filter = all_result;

    one_result.clear();
    // get pinyin
    IndexResult tmp_result;
    int l = 0;
    char tmp[5];
    int next_off = GetNewWord(words, off, tmp);
    while (next_off != -1 && strlen(tmp) == 1) {
      off = next_off;
      new_words[l] = tmp[0];
      ++l;
      next_off = GetNewWord(words, off, tmp);
    }
    new_words[l] = '\0';

    if (l != 0) {
      // search pinyin
      if (all_result.empty()) {
        if (trans_trie->SearchTrie(new_words, nullptr, &tmp_result) == -1) {
          break;
        }
        const TransTrieNode* trans_node = trans_trie->GetNode(tmp_result);

        // pinyin success， but no hanzi
        trans_result.clear();
        trans_expand.clear();
        trans_result.push_back(trans_node);
        if (trans_node->py_hz_len == 0) {
          uint32_t left = 0;
          uint32_t num = 0;
          uint32_t pinyin_deep = tmp_result.deep;
          while (left < trans_result.size()) {
            num = trans_result.size();
            ++pinyin_deep;
            for (uint32_t i = left; i < num; ++i) {
              tmp_result.deep = pinyin_deep;
              for (uint32_t j = 0; j < trans_result[i]->child_len; ++j) {
                tmp_result.index_id = trans_result[i]->child_off + j;
                trans_node = trans_trie->GetNode(tmp_result);

                if (trans_node->py_hz_len == 0) {
                  trans_result.push_back(trans_node);
                } else {
                  trans_expand.push_back(trans_node);
                }
              }
            }
            left = num;
          }
        } else {
          trans_expand.push_back(trans_node);
        }

        for (auto& node : trans_expand) {
          trans_node = node;
          const IndexResult* pyhz = trans_trie->GetNativeIndex(trans_node->py_hz_off);
          for (uint32_t i = 0; i < trans_node->py_hz_len; ++i) {
            one_result.push_back(pyhz[i]);
          }
        }

      } else {
        for (uint32_t i = 0; i < all_result.size(); ++i) {
          const IndexResult* hzpy = native_trie->GetTransIndex(all_result[i]);
          if (trans_trie->SearchTrie(new_words, &hzpy[i], &tmp_result) == -1) {
            continue;
          }

          const TransTrieNode* pinyin_index = trans_trie->GetNode(tmp_result);

          // pinyin success， but no hanzi
          trans_result.clear();
          trans_expand.clear();
          trans_result.push_back(pinyin_index);
          if (pinyin_index->py_hz_len == 0) {
            uint32_t l = 0;
            uint32_t num = 0;
            uint32_t pinyin_deep = tmp_result.deep;
            while (l < trans_result.size()) {
              num = trans_result.size();
              ++pinyin_deep;
              for (uint32_t i = l; i < num; ++i) {
                tmp_result.deep = pinyin_deep;
                for (uint32_t j = 0; j < trans_result[i]->child_len; ++j) {
                  tmp_result.index_id = trans_result[i]->child_off + j;
                  pinyin_index = trans_trie->GetNode(tmp_result);

                  if (pinyin_index->py_hz_len == 0) {
                    trans_result.push_back(pinyin_index);
                  } else {
                    trans_expand.push_back(pinyin_index);
                  }
                }
              }
              l = num;
            }
          } else {
            trans_expand.push_back(pinyin_index);
          }

          for (uint32_t i = 0; i < trans_expand.size(); ++i) {
            pinyin_index = trans_expand[i];
            const IndexResult* pyhz = trans_trie->GetNativeIndex(pinyin_index->py_hz_off);
            for (uint32_t i = 0; i < pinyin_index->py_hz_len; ++i) {
              one_result.push_back(pyhz[i]);
            }
          }
        }
      }

      if (one_result.empty()) {
        return 0;
      }
      all_result = one_result;
      one_result.clear();
    }
    if (next_off == -1) {
      break;
    }

    // get hanzi
    l = 0;
    next_off = GetNewWord(words, off, tmp);
    while (next_off != -1 && strlen(tmp) == 3) {
      off = next_off;
      strcpy(&new_words[l], tmp);
      l += 3;
      next_off = GetNewWord(words, off, tmp);
    }
    new_words[l] = '\0';
    if (l != 0) {
      // search hanzi
      if (all_result.empty()) {
        if (native_trie->SearchTrie(new_words, nullptr, &tmp_result) != -1) {
          one_result.push_back(tmp_result);
        }
      } else {
        for (auto& start : all_result) {
          if (native_trie->SearchTrie(new_words, &start, &tmp_result) != -1) {
            one_result.push_back(tmp_result);
          }
        }
      }

      if (one_result.empty()) {
        return 0;
      }
      all_result = one_result;
      one_result.clear();
    }
  }
  *index_result = all_result;
  Filter(prefix_index, filter, index_result);
  return 0;
}

void MixedSearch::Filter(const std::shared_ptr<PrefixIndex>& prefix_index,
                         const std::vector<IndexResult>& filter, std::vector<IndexResult>* result) {
  auto trie_data = prefix_index->native_trie();

  uint32_t i = 0;
  while (i < result->size()) {
    IndexResult index_result = result->at(i);
    IndexResult filter_start;
    IndexResult filter_end;
    bool is_remove = false;
    for (uint32_t j = 0; j < filter.size(); ++j) {
      filter_start = filter[j];
      filter_end = filter[j];
      int len = 1;

      const NativeTrieNode* start = trie_data->GetNode(filter_start);
      const NativeTrieNode* end = trie_data->GetNode(filter_end);

      while (start != nullptr && end != nullptr && index_result.deep > filter_start.deep &&
             len > 0) {
        filter_start.deep = filter_start.deep + 1;
        filter_start.index_id = start->child_off;

        filter_end.deep = filter_end.deep + 1;
        filter_end.index_id = end->child_off + end->child_len - 1;

        len = end->child_off + end->child_len - start->child_off;

        start = trie_data->GetNode(filter_start);
        end = trie_data->GetNode(filter_end);
      }

      if (index_result.deep == filter_start.deep &&
          index_result.index_id >= filter_start.index_id &&
          index_result.index_id <= filter_end.index_id) {
        break;
      } else {
        is_remove = true;
        result->erase(result->begin() + i);
        break;
      }
    }
    if (!is_remove) {
      ++i;
    }
  }
}

}  // namespace aurora::search
