#ifndef SEARCH_SRC_RETRIEVAL_AUTOCOMPLETE_RETRIEVER_H
#define SEARCH_SRC_RETRIEVAL_AUTOCOMPLETE_RETRIEVER_H

#include <memory>

#include "data/data_source.h"
#include "search_def.h"

namespace aurora::search {
class AutocompleteStrategy;
struct DataFolderLayout;
struct IndexResult;
struct MatchedPlace;
class PlaceTable;
class PrefixIndex;
struct SearchConfig;

class AutocompleteRetriever {
 public:
  explicit AutocompleteRetriever(const AutocompleteDataSource& data_source,
                                 int max_retrieval_count);

  bool Extend(const AutocompleteRequestPtr& request, const AutocompleteResponsePtr& response);

 private:
  bool ToMatchedPlaces(const std::vector<IndexResult>&, std::vector<MatchedPlace>* matched_places);
  void SortMatchedPlaces(const std::string& query, std::vector<MatchedPlace>* matched_places);
  int max_retrieval_count_ = 200;

  std::string city_code_;
  CityType city_type_;
  std::shared_ptr<AutocompleteStrategy> strategy_;
  std::shared_ptr<PrefixIndex> prefix_index_;
  std::shared_ptr<PlaceTable> place_table_;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_RETRIEVAL_AUTOCOMPLETE_RETRIEVER_H
