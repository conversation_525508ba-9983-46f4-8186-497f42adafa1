#ifndef SEARCH_REPOSITORY_BUILD_CONFIG_DEFINE_H_
#define SEARCH_REPOSITORY_BUILD_CONFIG_DEFINE_H_
#include <memory>
#include <string>
namespace aurora::search {
struct PlaceTableLayout {
  std::string place_dir;

  std::string primary_key_index;
  std::string place_table;

  PlaceTableLayout()
      : place_dir("detail_index"),
        primary_key_index("place_detail_index"),
        place_table("place_detail") {}
};
using PlaceTableLayoutPtr = std::shared_ptr<PlaceTableLayout>;

struct PrefixIndexLayout {
  std::string prefix_index_dir;

  std::string native_trie;
  std::string native_trans_mapping;

  std::string trans_trie;
  std::string trans_native_mapping;

  std::string place_name_sort_file;

  PrefixIndexLayout()
      : prefix_index_dir("prefix_index"),
        native_trie("trie_index"),
        native_trans_mapping("hanzi_pinyin"),
        trans_trie("pinyin_file"),
        trans_native_mapping("pinyin_hanzi"),
        place_name_sort_file("place_name_sort") {}
};
using PrefixIndexLayoutPtr = std::shared_ptr<PrefixIndexLayout>;

struct InvertIndexLayout {
  std::string invert_index_dir;

  std::string term_block;
  std::string term_index;
  std::string geo_trie;
  std::string term_data;
  std::string brief_all;

  InvertIndexLayout()
      : invert_index_dir("invert_index"),
        term_block("term_block"),
        term_index("term_index"),
        geo_trie("geo_data"),
        term_data("term_data"),
        brief_all("brief_all") {}
};
using InvertIndexLayoutPtr = std::shared_ptr<InvertIndexLayout>;

struct DataFolderLayout {
  PlaceTableLayoutPtr place_table_layout;
  PrefixIndexLayoutPtr prefix_index_layout;
  InvertIndexLayoutPtr invert_index_layout;

  DataFolderLayout()
      : place_table_layout(std::make_shared<PlaceTableLayout>()),
        prefix_index_layout(std::make_shared<PrefixIndexLayout>()),
        invert_index_layout(std::make_shared<InvertIndexLayout>()) {}
};

}  // namespace aurora::search
#endif
