#ifndef MAP_SRC_GUIDE_SRC_DATA_ENHANCE_NODE_H
#define MAP_SRC_GUIDE_SRC_DATA_ENHANCE_NODE_H

#include <set>

#include "guidance/src/common/common_def.h"
#include "guidance/src/data/graph_traverse.h"
#include "guidance/src/data/directed_edge.h"
#include "guidance/src/data/intersection_branch.h"

#include "guidance/src/common/turn.h"
#include "guidance/src/data/enhance_sign_post.h"
#include "guidance/src/data/enhance_junction_view.h"
#include "guidance/src/data/enhance_toll_station.h"

namespace aurora {
namespace guide {

struct IntersectingEdgeSimpleCounts {
    IntersectingEdgeSimpleCounts() {
      Clear();
    }

    void Clear() {
      left = 0;
      right = 0;
    }

uint32_t left;
uint32_t right;
};

struct IntersectingEdgeCounts {

    IntersectingEdgeCounts() {
        Clear();
    }

    IntersectingEdgeCounts(uint32_t r,
                         uint32_t rs,
                         uint32_t rdo,
                         uint32_t l,
                         uint32_t ls)
      : right(r), right_similar(rs), left(l), left_similar(ls) {        
    }

    void Clear() {
        right = 0;
        right_similar = 0;
        left = 0;
        left_similar = 0;
    }

  uint32_t right;
  uint32_t right_similar; 
  uint32_t left;
  uint32_t left_similar;
};

class EnhanceNode {
public:
    EnhanceNode(const RouteNodeId& route_node_id, 
                EnhanceDataProvider *provider, 
                DirectEdgeId *next_non_intersection_edge_id,
                DirectEdgeId *prev_edge_id);
    
    void UpdateXEdgePrevNameConsistency(std::shared_ptr<StreetNames> prev_street_names);
    void UpdateXEdgeCurrNameConsistency(std::shared_ptr<StreetNames> curr_street_names);

    void SetDirectEdge(DirectedEdge&& edge);
    void UpdateSignPosts(EnhanceDataProvider *provider, const DirectEdgeId &from_edge_id, const DirectEdgeId &to_edge_id);
    void UpdateJunctionViews(EnhanceDataProvider *provider, const DirectEdgeId &from_edge_id, DirectEdgeId *to_edge_ids, int32_t to_edge_num);
    void UpdateTollStations(EnhanceDataProvider *provider, const DirectEdgeId &from_edge_id, const DirectEdgeId &to_edge_id);

    DirectedEdge* GetDirectEdge();
    
    int32_t GetOutIntersectionSize() const {return out_branches_.size();}
    const OutIntersectionBranch* GetOutIntersectionBranch(int32_t index) const;
    
    int32_t GetInIntersectionSize() const {return in_branches_.size();}
    const InIntersectionBranch* GetInIntersectionBranch(int32_t index) const;

    bool IsFork() const;
    bool IsMotorwayJunction() const;
    bool IsInternalIntesection() const;

    bool HasXEdgeNameConssitency() const;
    
    bool HasOutIntersectionEdge() const;
    bool HasInIntersectionEdge() const;
    
    bool HasForwardIntersectionEdge(uint32_t from_heading);
    bool HasOnlyForwardIntersectionEdge(uint32_t from_heading);
    bool HasNonBackwardTraversableSameNameRampIntersectingEdge(uint32_t from_heading);

    bool HasWideForwardIntersectionEdge(uint32_t from_heading);
    bool HasSpecifiedTurnXEdge(const Turn::Type turn_type, uint32_t from_heading);
    bool HasSimilarStraightRoadClassXEdge(uint32_t path_turn_degree, uint32_t from_heading, int8_t road_class);
    bool HasSimilarStraightNonRampOrSameNameRampXEdge(uint32_t path_turn_degree, uint32_t from_heading);
    bool HasForwardRampXEdge(uint32_t from_heading);

    uint32_t GetStraightestIntersectionEdgeTurnDegree(uint32_t from_heading);
    bool IsStraightestIntersectingEdgeReversed(uint32_t from_heading);
    // y-axis positive
    uint32_t GetLeftMostTurnDegree(uint32_t default_turn_degree, uint32_t from_heading);
    
    // y-axis negative
    uint32_t GetRightMostTurnDegree(uint32_t default_turn_degree, uint32_t from_heading);

    void CalculateRightLeftIntersectingEdgeCounts(int16_t from_heading, 
                                                  IntersectingEdgeCounts &xedge_count);
    
    void CalculateInEdgeRightLeftForNonStreetIntersection(int16_t prev_end_heading, 
                                                          IntersectingEdgeSimpleCounts &xedge_count);

    int32_t GetSignPostNum() const;
    EnhanceSignPostPtr GetSignPost(int32_t index) const;
    const std::vector<EnhanceSignPostPtr>& GetSignPosts() const;

    int32_t GetJunctionViewNum() const;
    EnhanceJunctionViewPtr GetJunctionView(int32_t index) const;
    const std::vector<EnhanceJunctionViewPtr>& GetJunctionViews() const;
    EnhanceJunctionViewPtr GetRecommandJunctionView() const;

    
    bool HasTollStations() const;
    EnhanceTollStationPtr GetTollStation() const;

protected:
    void BuildNodeProperty(EnhanceDataProvider *provider, const RouteNodeId& route_node_id);
    void BuildForwardBranches(EnhanceDataProvider *provider, 
                              const RouteNodeId &node_id, 
                              DirectEdgeId *next_non_inner_id);

    void BuildBackwardBranchForNonStreetIntersection(EnhanceDataProvider *prvoder, 
                                                     const RouteEdgeId &node_id,
                                                     DirectEdgeId *pred_edge_id);

private:
    PointLL coord_;
    bool  has_traffic_light_;

    DirectedEdge direct_edge_;
    std::vector<OutIntersectionBranch> out_branches_;
    std::vector<InIntersectionBranch> in_branches_;
    std::vector<EnhanceSignPostPtr> sign_posts_;
    std::vector<EnhanceJunctionViewPtr> junction_views_;
    EnhanceTollStationPtr toll_station_;
    // mutable std::vector<GraphIntersectionEdge> intersection_edges_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_DATA_ENHANCE_NODE_H
/* EOF */
