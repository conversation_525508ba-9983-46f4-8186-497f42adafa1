#ifndef MAP_SRC_GUIDE_SRC_DATA_ENHANCE_DATA_PROVIDER_H
#define MAP_SRC_GUIDE_SRC_DATA_ENHANCE_DATA_PROVIDER_H

#include <vector>
#include <map>

#include "guidance/src/common/common_def.h"

namespace aurora {
namespace guide {

class EnhanceDataProvider {
public:
    EnhanceDataProvider();
    ~EnhanceDataProvider() = default;

    void SetDataProvider(std::shared_ptr<parser::DataProvider> provider);
    
    bool GetNodeOutEdgeIds(const RouteNodeId &node_id, std::vector<DirectEdgeId>& direct_edge_ids);
    bool GetNodeInEdgeIds(const RouteNodeId &node_id, std::vector<DirectEdgeId>& direct_edge_ids);

    TopolEdge*  GetTopoEdge(const RouteEdgeId &edge_id) const;
    AugmentEdge* GetAugmentEdge(const RouteEdgeId &edge_id) const;
    RouteNode* GetRouteNode(const RouteNodeId &node_id) const;

    LaneInfo* GetLaneInfo(const DirectEdgeId &direct_id) const;

    FacilityInfo* GetFacility(const DirectEdgeId &edge_id, int32_t &num) const;

    SignPostInfo* GetSignPostInfo(const DirectEdgeId &from_edge_id, 
                                  const DirectEdgeId &to_edge_id, 
                                  int32_t &num) const;

    JuncviewInfo* GetJunctionViewInfo(const DirectEdgeId &from_edge_id, 
                                      const DirectEdgeId &to_edge_id, 
                                      int32_t &num) const;

    JuncviewInfo* GetJunctionViewInfo(const DirectEdgeId &from_edge_id, 
                                      int32_t &num) const;
    
    TollGateInfo* GetTollStationInfo(const DirectEdgeId &from_edge_id, const DirectEdgeId &to_edge_id);

    bool SetCacheOption(bool cache_flag);

protected:
    RouteTileReaderPtr GetRouteTileReader(const RouteTileId &tile_id) const;

private:
    std::shared_ptr<parser::DataProvider> provider_;
    // cache
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_DATA_ENHANCE_DATA_PROVIDER_H
/* EOF */
