#include "guidance/src/data/enhance_data_provider.h"

#include <cassert>
#include <algorithm>

#include "guidance/src/common/guide_log.h"


namespace aurora {
namespace guide {

 EnhanceDataProvider::EnhanceDataProvider() {
    provider_ = nullptr;
 }

void EnhanceDataProvider::SetDataProvider(std::shared_ptr<parser::DataProvider> provider) {
    provider_ = provider;
}


RouteTileReaderPtr EnhanceDataProvider::GetRouteTileReader(const RouteTileId &tile_id) const {
    GUIDE_ASSERT(tile_id.value != 0);
    RouteTilePackagePtr ptr = provider_->GetRouteTileByID(tile_id);
    GUIDE_ASSERT(ptr != nullptr);
    if (ptr == nullptr) {
        return nullptr;
    }

    RouteTileReaderPtr reader = std::make_shared<RouteTileReader>();
    reader->SetTarget(ptr);
    return reader;
}

bool EnhanceDataProvider::GetNodeOutEdgeIds(const RouteNodeId &node_id, std::vector<DirectEdgeId>& direct_ids) {
    RouteTileReaderPtr reader = GetRouteTileReader(node_id.tile_id);
    if (reader == nullptr) {
        GUIDE_ASSERT(false);
        return false;
    }

    std::vector<bool> dirs;
    std::vector<RouteEdgeId> edge_ids;
    bool ret = reader->GetOutEdgeID(node_id, edge_ids, dirs); // // farward: start_node is node_id
    // assert(ret);
    if (!ret) {
        return ret;
    }

    direct_ids.reserve(edge_ids.size());
    for (uint32_t index = 0; index < edge_ids.size(); ++index) {
        DirectEdgeId val;
        val.edge_id = edge_ids[index];
        val.forward = dirs[index];
        direct_ids.emplace_back(val);
    }
    return ret;
}
    
bool EnhanceDataProvider::GetNodeInEdgeIds(const RouteNodeId &node_id, std::vector<DirectEdgeId>& direct_ids) {
    RouteTileReaderPtr reader = GetRouteTileReader(node_id.tile_id);
    if (reader == nullptr) {
        return false;
    }

    std::vector<bool> dirs;
    std::vector<RouteEdgeId> edge_ids;
    bool ret = reader->GetInEdgeID(node_id, edge_ids, dirs); // forward: end_node is node_id
    GUIDE_ASSERT(ret);

    direct_ids.reserve(edge_ids.size());
    for (uint32_t index = 0; index < edge_ids.size(); ++index) {
        DirectEdgeId val;
        val.edge_id = edge_ids[index];
        val.forward = dirs[index];
        direct_ids.emplace_back(val);
    }
    return ret;
}


TopolEdge*  EnhanceDataProvider::GetTopoEdge(const RouteEdgeId &edge_id) const {
    RouteTileReaderPtr reader = GetRouteTileReader(edge_id.tile_id);
    if (reader == nullptr) {
        return nullptr;
    }

    return reader->GetTopolEdgeByID(edge_id.feature_id);
}

AugmentEdge* EnhanceDataProvider::GetAugmentEdge(const RouteEdgeId &edge_id) const {
    RouteTileReaderPtr reader = GetRouteTileReader(edge_id.tile_id);
    if (reader == nullptr) {
        return nullptr;
    }

    AugmentEdge* ret = reader->GetAugmentEdgeByID(edge_id.feature_id);
    GUIDE_ASSERT(ret != nullptr);
    return ret;
}

RouteNode* EnhanceDataProvider::GetRouteNode(const RouteNodeId &node_id) const {
    RouteTileReaderPtr reader = GetRouteTileReader(node_id.tile_id);
    if (reader == nullptr) {
        return nullptr;
    }
    RouteNode* ret = reader->GetNodeByID(node_id.feature_id);
    GUIDE_ASSERT(ret);
    return ret;
}

LaneInfo* EnhanceDataProvider::GetLaneInfo(const DirectEdgeId &direct_id) const {
    RouteTileReaderPtr reader = GetRouteTileReader(direct_id.edge_id.tile_id);
    if (reader == nullptr) {
        return nullptr;
    }
    
    parser::LaneBase target_lane_base;
    target_lane_base.in_edge_id = direct_id.edge_id.feature_id;
    target_lane_base.in_edge_dir = direct_id.forward ? 0:1;

    auto lane_base_cmp = [](const parser::LaneBase &lhs, const parser::LaneBase &rhs) -> int32_t {
        LaneInfo lhs_li(&lhs);
        LaneInfo rhs_li(&rhs);

        if (lhs_li == rhs_li) {
            return 0;
        }

        return (lhs_li < rhs_li) ? 1:(-1);
    };

    LaneInfoSet& lane_infos = reader->GetLaneInfo();

    int32_t left = 0;
    int32_t right = lane_infos.size() - 1;
    while (left <= right) {
        int32_t mid = left + (right - left)/2;
        GUIDE_ASSERT(lane_infos[mid].GetBaseInfo() != nullptr);

        if (lane_infos[mid].GetBaseInfo() == nullptr) {
            GUIDE_LOG_ERROR("lane info is nullptr .");
            return nullptr;
        }

        int cmp = lane_base_cmp(*(lane_infos[mid].GetBaseInfo()), target_lane_base);
        if (cmp == 0) {
            return (lane_infos.data() + mid);
        } else if (cmp > 0) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    return nullptr;
}

FacilityInfo* EnhanceDataProvider::GetFacility(const DirectEdgeId &edge_id, int32_t &num) const {
    RouteTileReaderPtr reader = GetRouteTileReader(edge_id.edge_id.tile_id);
    if (reader == nullptr) {
        return nullptr;
    }
    
    parser::FacilityBase target_facility_base;
    target_facility_base.in_edge_id = edge_id.edge_id.feature_id;
    target_facility_base.in_edge_dir = edge_id.forward ? 0:1;

    auto facility_base_cmp = [](const parser::FacilityBase &lhs, const parser::FacilityBase &rhs) -> int32_t {
        FacilityInfo lhs_li(&lhs);
        FacilityInfo rhs_li(&rhs);

        if (lhs_li == rhs_li) {
            return 0;
        }

        return (lhs_li < rhs_li) ? 1:(-1);
    };

    FacilityInfoSet& facility_infos = reader->GetFacilityInfo();

    int32_t left = 0;
    int32_t right = facility_infos.size() - 1;
    int32_t mid = 0;
    bool find = false;
    while (left <= right) {
        mid = left + (right - left)/2;
        GUIDE_ASSERT(facility_infos[mid].GetBaseInfo() != nullptr);

        if (facility_infos[mid].GetBaseInfo() == nullptr) {
            GUIDE_LOG_ERROR("facility info is nullptr .");
            return nullptr;
        }

        int cmp = facility_base_cmp(*(facility_infos[mid].GetBaseInfo()), target_facility_base);
        if (cmp == 0) {
            find = true;
            if (mid == 0) {
                break;
            } else  {
                if (facility_base_cmp(*facility_infos[mid-1].GetBaseInfo(), target_facility_base) == 0) {
                    right = mid - 1;
                } else {
                    break;
                }
            }
        } else if (cmp > 0) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }

    num = 0;
    if (find) {
        FacilityInfo* result = facility_infos.data() + mid;
        GUIDE_ASSERT(facility_base_cmp(*(result->GetBaseInfo()), target_facility_base) == 0);
        num = 1;
        for (int index = mid + 1; index < facility_infos.size(); ++index) {
            GUIDE_ASSERT(facility_infos[index].GetBaseInfo() != nullptr);

            if (facility_infos[index].GetBaseInfo() == nullptr) {
                break;
            }

            if (facility_base_cmp(*(facility_infos[index].GetBaseInfo()), target_facility_base) == 0) {
                ++num;
            } else {
                break;
            }
        }
        return result;
    }

    return nullptr;
}

SignPostInfo* EnhanceDataProvider::GetSignPostInfo(const DirectEdgeId &from_edge_id, 
                                                   const DirectEdgeId &to_edge_id, 
                                                   int32_t &num) const {
    
    num = 0;
    RouteTileReaderPtr reader = GetRouteTileReader(from_edge_id.edge_id.tile_id);
    if (reader == nullptr) {
        return nullptr;
    }

    auto sign_post_cmp = [](const parser::SignpostBase &lhs, const parser::SignpostBase &rhs) -> int32_t {
        SignPostInfo lhs_si(&lhs);
        SignPostInfo rhs_si(&rhs);

        if (lhs_si == rhs_si) {
            return 0;
        }

        return (lhs_si < rhs_si) ? 1 : -1;
    };

    SignpostInfoSet& sign_posts = reader->GetSignpostInfo();
    int32_t left = 0;
    int32_t right = sign_posts.size() - 1;

    parser::SignpostBase target_sign_post; // 此处只赋值参与operator< & ==的字段
    target_sign_post.in_edge_id = from_edge_id.edge_id.feature_id;
    target_sign_post.in_edge_dir = from_edge_id.forward ? 0 : 1;
    target_sign_post.out_mesh_row = to_edge_id.edge_id.tile_id.mesh_row;
    target_sign_post.out_mesh_col = to_edge_id.edge_id.tile_id.mesh_col;
    target_sign_post.out_tile_id = to_edge_id.edge_id.tile_id.tile_id;
    target_sign_post.out_edge_id = to_edge_id.edge_id.feature_id;
    target_sign_post.out_edge_dir = to_edge_id.forward ?  0 : 1;

    bool find = false;
    int32_t mid = 0;
    while (left <= right) {
        mid = left + (right - left)/2;
        GUIDE_ASSERT(sign_posts[mid].GetBaseInfo() != nullptr);

        if (sign_posts[mid].GetBaseInfo() == nullptr) {
            GUIDE_LOG_ERROR("sign post base info is nullptr .");
            return nullptr;
        }

        int32_t cmp = sign_post_cmp(*(sign_posts[mid].GetBaseInfo()), target_sign_post);
        if (cmp == 0) { // 查找最左端
            find = true;
            if (mid == 0) {
                break;
            } else  {
                if (sign_post_cmp(*sign_posts[mid-1].GetBaseInfo(), target_sign_post) == 0) {
                    right = mid - 1;
                } else {
                    break;
                }
            }
        } else if (cmp > 0) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }

    if (find) {
        SignPostInfo* result = sign_posts.data() + mid;
        GUIDE_ASSERT(sign_post_cmp(*(result->GetBaseInfo()), target_sign_post) == 0);
        num = 1;
        for (int index = mid + 1; index < sign_posts.size(); ++index) {
            GUIDE_ASSERT(sign_posts[index].GetBaseInfo() != nullptr);

            if (sign_posts[index].GetBaseInfo() == nullptr) {
                break;
            }

            if (sign_post_cmp(*(sign_posts[index].GetBaseInfo()), target_sign_post) == 0) {
                ++num;
            } else {
                break;
            }
        }
        return result;
    }

    return nullptr;
}

JuncviewInfo* EnhanceDataProvider::GetJunctionViewInfo(const DirectEdgeId &from_edge_id, 
                                                       const DirectEdgeId &to_edge_id, 
                                                       int32_t &num) const {
    num = 0;
    RouteTileReaderPtr reader = GetRouteTileReader(from_edge_id.edge_id.tile_id);
    if (reader == nullptr) {
        return nullptr;
    }

    auto junc_view_cmp = [](const parser::JuncviewBase &lhs, const parser::JuncviewBase &rhs) -> int32_t {
        JuncviewInfo lhs_si(&lhs);
        JuncviewInfo rhs_si(&rhs);

        if (lhs_si == rhs_si) {
            return 0;
        }
        return (lhs_si < rhs_si) ? 1 : -1;
    };

    JuncviewInfoSet& junc_views = reader->GetJuncviewInfo();
    int32_t left = 0;
    int32_t right = junc_views.size() - 1;

    parser::JuncviewBase target_junction_view; // 此处只赋值参与operator< & ==的字段
    target_junction_view.in_edge_id = from_edge_id.edge_id.feature_id;
    target_junction_view.in_edge_dir = from_edge_id.forward ? 0 : 1;
    target_junction_view.out_mesh_row = to_edge_id.edge_id.tile_id.mesh_row;
    target_junction_view.out_mesh_col = to_edge_id.edge_id.tile_id.mesh_col;
    target_junction_view.out_tile_id = to_edge_id.edge_id.tile_id.tile_id;
    target_junction_view.out_edge_id = to_edge_id.edge_id.feature_id;
    target_junction_view.out_edge_dir = to_edge_id.forward ?  0 : 1;

    bool find = false;
    int32_t mid = 0;
    while (left <= right) {
        mid = left + (right - left)/2;
        GUIDE_ASSERT(junc_views[mid].GetBaseInfo() != nullptr);

        if (junc_views[mid].GetBaseInfo() == nullptr) {
            GUIDE_LOG_ERROR("junction view base info is nullptr .");
            return nullptr;
        }

        int32_t cmp = junc_view_cmp(*(junc_views[mid].GetBaseInfo()), target_junction_view);
        if (cmp == 0) { // 查找最左端
            find = true;
            if (mid == 0) {
                break;
            } else  {
                if (junc_view_cmp(*junc_views[mid-1].GetBaseInfo(), target_junction_view) == 0) {
                    right = mid - 1;
                } else {
                    break;
                }
            }
        } else if (cmp > 0) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }

    if (find) {
        JuncviewInfo *result = junc_views.data() + mid;
        GUIDE_ASSERT(junc_view_cmp(*(result->GetBaseInfo()), target_junction_view) == 0);
        num = 1;
        for (int index = mid + 1; index < junc_views.size(); ++index) {
            GUIDE_ASSERT(junc_views[index].GetBaseInfo() != nullptr);

            if (junc_views[index].GetBaseInfo() == nullptr) {
                break;
            }

            if (junc_view_cmp(*(junc_views[index].GetBaseInfo()), target_junction_view) == 0) {
                ++num;
            } else {
                break;
            }
        }
        return result;
    }

    return nullptr;   
}

JuncviewInfo* EnhanceDataProvider::GetJunctionViewInfo(const DirectEdgeId &from_edge_id, int32_t &num) const {
    num = 0;
    RouteTileReaderPtr reader = GetRouteTileReader(from_edge_id.edge_id.tile_id);
    if (reader == nullptr) {
        return nullptr;
    }

    auto junc_view_cmp = [](const parser::JuncviewBase &lhs, const parser::JuncviewBase &rhs) -> int32_t {
        JuncviewInfo lhs_si(&lhs);
        JuncviewInfo rhs_si(&rhs);

        if (lhs_si.GetBaseInfo()->in_edge_id != rhs_si.GetBaseInfo()->in_edge_id) {
            return (lhs_si.GetBaseInfo()->in_edge_id < rhs_si.GetBaseInfo()->in_edge_id) ? 1:(-1);
        }

        if (lhs_si.GetBaseInfo()->in_edge_dir != rhs_si.GetBaseInfo()->in_edge_dir) {
            return (lhs_si.GetBaseInfo()->in_edge_dir < rhs_si.GetBaseInfo()->in_edge_dir) ? 1:(-1);
        }
        return 0;
    };

    JuncviewInfoSet& junc_views = reader->GetJuncviewInfo();
    int32_t left = 0;
    int32_t right = junc_views.size() - 1;

    parser::JuncviewBase target_junction_view; // 此处只赋值参与operator< & ==的字段
    target_junction_view.in_edge_id = from_edge_id.edge_id.feature_id;
    target_junction_view.in_edge_dir = from_edge_id.forward ? 0 : 1;

    bool find = false;
    int32_t mid = 0;
    while (left <= right) {
        mid = left + (right - left)/2;
        GUIDE_ASSERT(junc_views[mid].GetBaseInfo() != nullptr);

        if (junc_views[mid].GetBaseInfo() == nullptr) {
            GUIDE_LOG_ERROR("junction view base info is nullptr .");
            return nullptr;
        }

        int32_t cmp = junc_view_cmp(*(junc_views[mid].GetBaseInfo()), target_junction_view);
        if (cmp == 0) { // 查找最左端
            find = true;
            if (mid == 0) {
                break;
            } else  {
                if (junc_view_cmp(*junc_views[mid-1].GetBaseInfo(), target_junction_view) == 0) {
                    right = mid - 1;
                } else {
                    break;
                }
            }
        } else if (cmp > 0) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }

    if (find) {
        JuncviewInfo *result = junc_views.data() + mid;
        GUIDE_ASSERT(junc_view_cmp(*(result->GetBaseInfo()), target_junction_view) == 0);
        num = 1;
        for (int index = mid + 1; index < junc_views.size(); ++index) {
            GUIDE_ASSERT(junc_views[index].GetBaseInfo() != nullptr);

            if (junc_views[index].GetBaseInfo() == nullptr) {
                break;
            }

            if (junc_view_cmp(*(junc_views[index].GetBaseInfo()), target_junction_view) == 0) {
                ++num;
            } else {
                break;
            }
        }
        return result;
    }

    return nullptr;   
}

TollGateInfo* EnhanceDataProvider::GetTollStationInfo(const DirectEdgeId &from_edge_id, const DirectEdgeId &to_edge_id) {
    RouteTileReaderPtr reader = GetRouteTileReader(from_edge_id.edge_id.tile_id);
    if (reader == nullptr) {
        return nullptr;
    }

    auto toll_station_cmp = [](const parser::TollgateBase &lhs, const parser::TollgateBase &rhs) -> int32_t {
        TollGateInfo lhs_si(&lhs);
        TollGateInfo rhs_si(&rhs);

        if (lhs_si == rhs_si) {
            return 0;
        }
        return (lhs_si < rhs_si) ? 1 : -1;
    };

    TollGateInfoSet& toll_stations = reader->GetTollgateInfo();
    int32_t left = 0;
    int32_t right = toll_stations.size() - 1;

    parser::TollgateBase target_toll_station; // 此处只赋值参与operator< & ==的字段
    target_toll_station.in_edge_id = from_edge_id.edge_id.feature_id;
    target_toll_station.in_edge_dir = from_edge_id.forward ? 0 : 1;
    target_toll_station.out_mesh_row = to_edge_id.edge_id.tile_id.mesh_row;
    target_toll_station.out_mesh_col = to_edge_id.edge_id.tile_id.mesh_col;
    target_toll_station.out_tile_id = to_edge_id.edge_id.tile_id.tile_id;
    target_toll_station.out_edge_id = to_edge_id.edge_id.feature_id;
    target_toll_station.out_edge_dir = to_edge_id.forward ?  0 : 1;

    int32_t mid = 0;
    while (left <= right) {
        mid = left + (right - left)/2;
        GUIDE_ASSERT(toll_stations[mid].GetBaseInfo() != nullptr);

        if (toll_stations[mid].GetBaseInfo() == nullptr) {
            GUIDE_LOG_ERROR("junction view base info is nullptr .");
            return nullptr;
        }

        int32_t cmp = toll_station_cmp(*(toll_stations[mid].GetBaseInfo()), target_toll_station);
        if (cmp == 0) {
            return (toll_stations.data() + mid);
        } else if (cmp > 0) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }

    return nullptr;   
}

bool EnhanceDataProvider::SetCacheOption(bool cache_flag) {
    provider_->SwitchRouteTileCacheMode(cache_flag);
    return true;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
